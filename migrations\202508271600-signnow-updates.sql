-- Add new status values and error_message field to invoice_signnow_sessions table

ALTER TABLE `invoice_signnow_sessions` 
MODIFY COLUMN `status` ENUM('PENDING', 'SIGNED', 'CANCELLED', 'EXPIRED', 'DOCUMENT_UPLOADED', 'INVITE_FAILED') DEFAULT 'PENDING';

ALTER TABLE `invoice_signnow_sessions` 
ADD COLUMN `error_message` TEXT NULL COMMENT 'Error message if invite or signing fails' AFTER `signed_document_path`;
