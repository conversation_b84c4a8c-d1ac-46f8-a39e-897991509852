import { request } from "umi";
import { paramsSerializer } from "../../api";

const urlPrefix = "/api/jobs";

/**
 * Get Dispatch list
 *
 * GET api/jobs
 */
export async function getDispatchListByPage(params: API.PageParams & Partial<API.Dispatch>, sort?: any, filter?: any) {
    return request<API.ResultObject<API.PaginatedResult<API.Dispatch>>>(`${urlPrefix}/index`, {
        method: "GET",
        params: {
            ...params,
            perPage: params.pageSize,
            page: params.current,
            sort,
            filter,
        },
        withToken: true,
        paramsSerializer,
    }).then((res) => ({
        data: res.data.rows,
        success: res.success,
        total: res.data.pagination?.total_records,
        pagination: res.data.pagination,
    }));
}


/**
 * Search Dispatch list
 *
 * GET api/jobs/search_dispatches
 */
export async function searchDispatch(params: API.DispatchSearchParams) {
    return request<API.ResultArray<API.Dispatch>>(`${urlPrefix}/search_dispatches`, {
        method: "GET",
        params: {
            ...params
        },
        withToken: true,
        paramsSerializer,
    }).then((res) => (res));
}

/**
 * Assign Dispatch
 *
 * POST api/jobs/assign
 */
export async function assignDispatch(data: API.DispatchAssignPayload, options?: { [key: string]: any }) {
    return request<Record<string, any>>(`${urlPrefix}/assign`, {
        method: "POST",
        data: data,
        withToken: true,
        ...(options || {}),
    }).then((res) => (res));
}

/**
 * Accept or Decline Dispatch
 *
 * POST api/jobs/accept_decline
 */
export async function acceptOrDeclineDispatch(data: API.DispatchAcceptOrDeclinePayload, options?: { [key: string]: any }) {
    return request<Record<string, any>>(`${urlPrefix}/accept_decline`, {
        method: "POST",
        data: data,
        withToken: true,
        ...(options || {}),
    }).then((res) => (res));
}