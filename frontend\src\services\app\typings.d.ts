type ZeroOrOne = 0 | 1;

type TimestampData = {
  created_at?: string;
  updated_at?: string;
};

type CreatorData = {
  created_by?: any;
  created_at?: string;
};
type UpdaterData = {
  updated_by?: any;
  updated_on?: string;
};

declare namespace DispatchTheme {
  type ItemColors = {
    soft: string;
    hard: string;
  };

}

declare namespace API {
  type BaseResult = {
    success?: boolean;
    data?: any;
    message?: any;
  };

  type Pagination = {
    current_page?: number;
    limit?: number;
    total_records?: number;
    total_pages?: number;
    has_more?: boolean;
  }

  type PaginatedResult<T> = {
    rows: T[];
    pagination?: Pagination;
  };

  type ResultList<T> = Omit<BaseResult, 'data'> & {
    data: PaginatedResult<T>;
  };

  type ResultObject<T> = Omit<BaseResult, 'data'> & {
    data: T;
  };

  type Result<T> = Omit<BaseResult, 'message'> & {
    message: PaginatedResult<T>;
  };
  type ResultArray<T> = T[];
  type CurrentUser = {
    id?: number;
    user_name?: string;
    user_email?: string;
    avatar?: string;
    userid?: string;
    email?: string;
    signature?: string;
    group?: string;
    profile_image?: string;
    api_token?: string;
    token_expires?: string;
    refresh_token?: string;
  } & TimestampData;

  type LoginResult = {
    success?: boolean;
    token?: string;
    expires?: string;
    user?: API.CurrentUser;
  };
  type UserListItem = CurrentUser;
  type UserList = {
    data: UserListItem[];
    success: boolean;
    total: number;
  };
  type userParams = {
    page?: number;
    perPage?: number;
  };

  type PageParams = {
    current?: number;
    pageSize?: number;
    keyWords?: string;
  } & {
    with?: string
  };

  type AppSettings = {
    dict?: Record<string, any>;
    drSelection?: Record<string, string[]>;
    serverTz?: number;
    FY_START_MONTH?: number;
  };

  type LoginParams = {
    username?: string;
    password?: string;
    autoLogin?: boolean;
    user_type?: string;
  };

  type ErrorResponse = {
    errorCode: string;
    errorMessage?: string;
    success?: boolean;
  };


  /** Dispatch related
   * -------------------------------------------------------------- */
  type Customer = {
    id?: number;
    external_id?: number;
    name?: string;
    email?: string;
    preferredCommunicationType?: string;
    customer_properties?: CustomerProperty[];
    customer_phones?: CustomerPhone[];
  }

  type CustomerPhone = {
    id?: number;
    customer_id?: number;
    phone?: string;
    phone_type?: string;
  } & TimestampData & {
    customer?: Customer
  };

  type CustomerPropertyGeolocation = {
    id?: number;
    property_id?: number;
    lat?: number;
    lng?: number;
    zip?: number;
  }

  type Address = {
    id?: number;
    customer_id?: number
    external_id?: number
    city?: string
    state?: string
    streetDirection?: string
    streetName?: string
    streetNumber?: string
    unitNumber?: string
    unitType?: string
    zip?: number
    zipFour?: number
  } & TimestampData & {
    customer?: Customer;
    customer_property_geolocation?: CustomerPropertyGeolocation;
    dispatch_customer_properties?: any;
    full_address?: string;
  };

  type CustomerProperty = Address;

  type Dispatch = {
    id?: number;
    tenant_id?: number;
    source_org_id?: number;
    external_id?: number;
    dispatchType?: string;
    trade?: string;
    priority?: string;
    date?: string;
    isAuthoRequired?: string;
    job_source?: number;
    env?: number;
  } & TimestampData & {
    dispatch_status?: DispatchStatus;
    dispatch_user?: DispatchUser;
    dispatch_customers?: DispatchCustomer[];
    dispatch_appointment?: DispatchAppointment;
    customer?: Customer;
    address?: Address;
    addresses?: Address[];
    gps_dispatch?: GpsDispatch;
    estimates?: DispatchEstimate[];
    invoices?: DispatchInvoice[];
    payments?: DispatchPayment[];
  };

  type DispatchByZip = {
    [key: string]: {
      [key: string]: Dispatch[]
    }
  }

  type DispatchByDate = DispatchByZip;

  type DispatchSearchParams = Partial<API.Dispatch> & {
    employeeId?: string;
    startDate?: string;
    endDate?: string;
    date?: string;
    jobStatus?: string;
    appointmentStatus?: string;
    with?: string;
  }

  type DispatchAcceptOrDeclinePayload = {
    job_id?: number;
    action?: string;
  }

  type DispatchAssignPayload = {
    job_id?: number;
    appointment_set?: string;
    assigned_techs?: string;
    index?: number;
  }

  type DispatchUser = {
    dispatch_id?: number;
    user_id?: number;
    created_at?: string;
    updated_at?: string;
  }

  type DispatchCustomer = {
    customer?: Customer;
    customer_id?: number;
    dispatch_id?: number;
  }

  type DispatchStatus = {
    id?: number;
    dispatch_id?: number;
    flow_step?: string;
    appliance_status?: string;
    appointment_status?: string;
    authorization_status?: string;
    cash_status?: string;
    cil_status?: string;
    contact_status?: string;
    equipment_status?: string;
    invoice_status?: string;
    job_status?: string;
    ncc_status?: string;
    parking_status?: string;
    parts_status?: string;
    quote_status?: string;
  }

  type DispatchAppointment = {
    appointment_date?: string;
    appointment_time?: string;
    created_at?: string;
    dispatch_id?: number;
    frontdoor_appointment?: string;
    id?: number;
    stop_sequence?: number;
    updated_at?: string;
    user_id?: number;
  }

  /** Pricebook related
   * -------------------------------------------------------------- */
  type PbService = {
    id?: number;
    tenant_id?: number;
    name?: string;
    description?: string;
    cost?: number;
    price?: number;
    taxable?: number;
    tax_percent?: number;
    duration?: number;
    unit_of_measurement?: string;
    task_code?: string;
    is_online_booking?: number;
    settings?: Record<string, any>;
  } & CreatorData & UpdaterData & {
    // relations
    pb_service_categories?: PbServiceCategory[];
  }

  type PbServiceCategory = {
    id?: number;
    file_id?: number;
    tenant_id?: number;
    parent_id?: number;
    name?: string;
    position?: number;
  } & TimestampData & {
    file?: File;
    pb_services?: PbService[];
  };

  /** Dispatch Estimate related
   * -------------------------------------------------------------- */
  type DispatchEstimate = {
    id?: number;
    dispatch_id?: number;
    customer_id?: number;
    file_id?: number;
    purchase_no?: string;
    status?: string;
    issue_date?: string;
    work_start_date?: string;
    grand_total?: number;
    subtotal?: number;
    tax_percent?: number;
    tax_amount?: number;
    discount_amount?: number;
    total_item_count?: number;
    notes?: string;
    payment_terms?: string;
    valid_until?: string;
    billing_company?: string;
    billing_name?: string;
    billing_email?: string;
    billing_phone?: string;
    billing_city?: string;
    billing_state?: string;
    billing_street_direction?: string;
    billing_street_name?: string;
    billing_street_string?: string;
    billing_unit_string?: string;
    billing_unit_type?: string;
    billing_zip?: number;
    billing_zip_four?: string;
    customer_company?: string;
    customer_name?: string;
    customer_email?: string;
    customer_phone?: string;
    customer_city?: string;
    customer_state?: string;
    customer_street_direction?: string;
    customer_street_name?: string;
    customer_street_string?: string;
    customer_unit_string?: string;
    customer_unit_type?: string;
    customer_zip?: number;
    customer_zip_four?: string;
  } & CreatorData & UpdaterData & {
    items?: DispatchEstimateItem[];
    logs?: DispatchEstimateLog[];
    customer?: Customer;
    dispatch?: Dispatch;
    file?: File;
    invoices?: DispatchInvoice[];
  };

  type DispatchEstimateItem = {
    id?: number;
    dispatch_estimate_id?: number;
    service_id?: number;
    name?: string;
    description?: string;
    qty?: number;
    cost?: number;
    price?: number;
    price_total?: number;
    taxable?: ZeroOrOne;
    tax_percent?: number;
    tax_amount?: number;
    total?: number;
    duration?: number;
    sort_order?: number;
  } & TimestampData & {
    pb_service?: PbService;
    estimate?: DispatchEstimate;
  };

  type DispatchEstimateLog = {
    id?: number;
    dispatch_estimate_id?: number;
    title?: string;
    notes?: string;
  } & CreatorData & {
    estimate?: DispatchEstimate;
    creator?: CurrentUser;
  };

  /** Dispatch Invoice related
   * -------------------------------------------------------------- */
  type DispatchInvoice = {
    id?: number;
    tenant_id?: number;
    dispatch_id?: number;
    estimate_id?: number;
    customer_id?: number;
    invoice_no?: string;
    file_id?: number;
    status?: string;
    issue_date?: string;
    service_date?: string;
    grand_total?: number;
    subtotal?: number;
    tax_percent?: number;
    tax_amount?: number;
    discount_amount?: number;
    total_item_count?: number;
    notes?: string;
    payment_terms?: string;
    valid_until?: string;
    billing_company?: string;
    billing_name?: string;
    billing_email?: string;
    billing_phone?: string;
    billing_city?: string;
    billing_state?: string;
    billing_street_direction?: string;
    billing_street_name?: string;
    billing_street_number?: string;
    billing_unit_number?: string;
    billing_unit_type?: string;
    billing_zip?: number;
    billing_zip_four?: string;
    customer_company?: string;
    customer_name?: string;
    customer_email?: string;
    customer_phone?: string;
    customer_city?: string;
    customer_state?: string;
    customer_street_direction?: string;
    customer_street_name?: string;
    customer_street_number?: string;
    customer_unit_number?: string;
    customer_unit_type?: string;
    customer_zip?: number;
    customer_zip_four?: string;
  } & CreatorData & UpdaterData & {
    // relations
    items?: DispatchInvoiceItem[];
    file?: File;
    customer?: Customer;
    estimate?: DispatchEstimate;
    dispatch?: Dispatch;
    tenant?: Tenant;
    logs?: DispatchInvoiceLog[];
    payments?: DispatchInvoicePayment[];
  };

  type DispatchInvoiceItem = {
    id?: number;
    dispatch_invoice_id?: number;
    service_id?: number;
    name?: string;
    description?: string;
    qty?: number;
    cost?: number;
    price?: number;
    price_total?: number;
    taxable?: number;
    tax_percent?: number;
    tax_amount?: number;
    total?: number;
    duration?: number;
    sort_order?: number;
  } & TimestampData & {
    invoice?: DispatchInvoice;
    pb_service?: PbService;
  };

  type DispatchInvoiceLog = {
    id?: number;
    dispatch_invoice_id?: number;
    title?: string;
    notes?: string;
  } & CreatorData & {
    invoice?: DispatchInvoice;
    creator?: CurrentUser;
  };

  type DispatchInvoicePayment = {
    id?: number;
    dispatch_invoice_id?: number;
    amount_ordered?: number;
    amount_paid?: number;
    amount_refunded?: number;
    amount_canceled?: number;
    method?: string;
    details?: Record<string, any>;
  } & UpdaterData & CreatorData & {
    invoice?: DispatchInvoice;
  };

  /** Gps Butler related
   * -------------------------------------------------------------- */
  type GpsDispatchNote = {
    id?: number;
    dispatch_id?: number;
    device_id?: number;
    driver_name?: string;
    customer_full_name?: string;
    customer_full_address?: string;
    note_date?: string;
    issue_note?: string;
    has_pics?: string;
    job_status?: string;
    dispatch_url?: string;
    smart_url?: string;
    acknowledged?: string;
    submitted?: ZeroOne;
  } & CreatorData & {
    driver_device?: DriverDevice;
    dispatch?: Dispatch;
  }

  type GpsDispatchStatus = {
    id?: number;
    tenant_id?: number;
    status_name?: string;
    color_hex?: string;
  }

  type GpsDispatchBoard = {
    dispatch_id?: number;
    device_id?: number;
    customer_first_name?: string;
    customer_last_name?: string;
    customer_full_address?: string;
    device_display_name?: string;
    on_site?: string;
    off_site?: string;
    time?: number;
    exclude_from_board?: ZeroOne;
  } & {
    customer_full_name?: string;  // calc value
    driver_device?: DriverDevice;
    dispatch?: API.Dispatch;
  }

  /** Driver positions
   * -------------------------------------------------------------- */
  type DriverDevice = {
    id?: number;
    tenant_id?: number;
    display_name?: string;
    device_sid?: string;
    factory_id?: string;
    make?: string;
    model?: string;
    active_state?: string;
  } & {
    last_position?: DriverDevicePositionAggregated;
    positions?: DriverDevicePosition[];
    user?: CurrentUser;
    gps_dispatch_notes?: CurrentUser;
    gps_dispatch_boards?: CurrentUser;
  }

  type DriverDevicePosition = {
    id?: number;
    device_point_id?: string;
    device_sid?: string;
    lat?: number;
    lng?: number;
    angle?: number;
    speed?: number;
    speed_unit?: string;
    drive_status?: string;
    drive_status_duration_value?: number;
    drive_status_duration_unit?: string;
    device_state_stale?: ZeroOne;
    dt_server?: string;
  }

  type DriverDevicePositionAggregated = {
    device_sid?: string;
    lat?: number;
    lng?: number;
    angle?: number;
    speed?: number;
    speed_unit?: string;
    drive_status?: string;
    drive_status_duration_value?: number;
    drive_status_duration_unit?: string;
    device_state_stale?: ZeroOne;
    dt_server?: string;
  } & {
    // calc values.
    is_active?: ZeroOne;  // Driver is active?
  }

  /** Tenant related
   * -------------------------------------------------------------- */
  type Tenant = {
    id?: number;
    tenant_name?: string;
    base_url?: string;
    tenant_logo?: string;
    created_at?: string;
    updated_at?: string;
    external_id?: number;
    source?: string;
  }

  /** User related
   * -------------------------------------------------------------- */
  type UserType = {
    user_id: number;
    user_type: string;
    tenant_id: number;
  }

  type UserProfile = {
    user_id: number;
    user_phone: string;
    user_role: string;
    user_color: string;
    user_address: string;
    home_lat: string;
    home_lng: string;
    is_forwarding_active: number;
    msg_in_hours: string;
    msg_after_hours: string;
    home_geofence_radius: number;
    home_geofence_center: string;
    created_at?: string;
    updated_at?: string;
  }

  type UserSearchParams = {
    with?: string;
  }
}
