import { request } from "umi";
import { paramsSerializer } from "../../api";
import { nf2 } from "@/util";

const urlPrefix = "/api/tenant/pricebook/service";

/**
 * Search pricebook's services
 *
 * GET /api/tenant/pricebook/service/search
 */
export async function getPbServiceACList(params: API.PageParams, sort?: any, filter?: any) {
    return request<API.PaginatedResult<API.PbService>>(`${urlPrefix}/search`, {
        method: "GET",
        params: {
            ...params,
            perPage: params.pageSize,
            page: params.current,
            sort,
            filter,
        },
        withToken: true,
        paramsSerializer,
    }).then((res) => res.rows.map(service => ({
        value: service.name,
        label: `${service.name} - $${nf2(service.price || 0)}`,
        data: service,
    })));
}