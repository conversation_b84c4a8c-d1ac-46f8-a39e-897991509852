<?php
namespace Butler\Lib\FrontDoor;

use Butler\Models\ButlerDB;
use Butler\Models\Dispatch\Dispatch;
use Butler\Models\Dispatch\DispatchOrg;
use Butler\Models\Dispatch\DispatchItem;
use Butler\Models\Dispatch\DispatchItemSymptom;
use Butler\Models\Dispatch\DispatchPayment;
use Butler\Models\Dispatch\DispatchCoverage;
use Butler\Models\Dispatch\DispatchHighlight;
use Butler\Models\Dispatch\DispatchCoverageNote;
use Butler\Models\Dispatch\DispatchContract;
use Butler\Models\Dispatch\DispatchCustomer;
use Butler\Models\Dispatch\DispatchStatus;
use Butler\Models\Dispatch\DispatchCustomerProperty;
use Butler\Models\Customer\Customer;
use Butler\Models\Customer\CustomerProperty;
use Butler\Models\Customer\CustomerPhone;
use Butler\Models\Customer\CustomerPropertyGeolocation;
use Butler\Helpers\GoogleHelper;
use PDO;

class DispatchJob {

    private $db;
    private $tenantId;
    private $dispatchId = null;
    private $itemId = null;
    private $symptomId = null;
    private $coverageId = null;
    private $notesId = null;
    private $contractId = null;
    private $customerId = null;
    private $propertyId = null;
    
    public function __construct($tenantId) {    
        $this->db = ButlerDB::getMainInstance();
        $this->tenantId = $tenantId;
        $this->environment = $_ENV['ENVIRONMENT'] === 'production' ? 'prod' : 'sandbox';     
    }

    public function handle($outbound) {
        $data = $outbound[0];

        // Get organization by external ID
        if (!isset($data['external_organization_id'])) {
            return;
        }
        $orgId = $data['external_organization_id'];
        $dispatchOrgModel = new DispatchOrg();
        $existingOrg = $dispatchOrgModel->getByExternalId($orgId);
        $orgId = $existingOrg['id'];

        $dispatch = $data['dispatch'];
        $jobId = $dispatch['external_id'];
        
        // Check if job already exists using model
        $dispatchModel = new Dispatch();
        $existingJob = $dispatchModel->getByExternalId($jobId);

        $env = $this->environment == 'prod' ? 'production' : 'sandbox';
        
        if ($existingJob) {
            $isAuthoRequired = $dispatch['isAuthoRequired'] ? 'true' : 'false';
            
            $updateData = [
                'dispatchType' => $dispatch['dispatchType'],
                'trade' => $dispatch['trade'],
                'priority' => $dispatch['priority'],
                'date' => $dispatch['date'],
                'isAuthoRequired' => $isAuthoRequired,
                'source_org_id' => $orgId,
                'tenant_id' => $this->tenantId,
                'job_source' => 'frontdoor',
                'env' => $env
            ];
            
            $dispatchModel->where('id', $existingJob['id'])->update($updateData);
            $this->dispatchId = $existingJob['id'];
            return $this->dispatchId;
        } else {
            // Insert new job using model
            $isAuthoRequired = $dispatch['isAuthoRequired'] ? 'true' : 'false';
            
            $insertData = [
                'dispatchType' => $dispatch['dispatchType'],
                'external_id' => $jobId,
                'tenant_id' => $this->tenantId,
                'source_org_id' => $orgId,
                'trade' => $dispatch['trade'],
                'priority' => $dispatch['priority'],
                'date' => $dispatch['date'],
                'isAuthoRequired' => $isAuthoRequired,
                'job_source' => 'frontdoor',
                'env' => $env
            ];
            
            $newDispatch = $dispatchModel->create($insertData);
            $this->dispatchId = $newDispatch->id;
            return $this->dispatchId;
        }
    }

    public function addItems($outbound) {
        $data = $outbound[0];
        if (!isset($data['dispatch'])) {
            return;
        }
        $dispatch = $data['dispatch'];

        if (!isset($dispatch['items'])) {
            return;
        }
        $items = $dispatch['items'];

        foreach($items as $key => $item) {
            // Use DispatchItem model to check if item exists
            $dispatchItemModel = new DispatchItem();
            $existingItem = $dispatchItemModel->where('external_id', $item['external_id'])
                ->where('dispatch_id', $this->dispatchId)
                ->first();
            
            if ($existingItem) {
                // Update existing item using model
                $existingItem->description = $item['description'];
                $existingItem->status = $item['status'] ?? 'Open';
                $existingItem->save();
                $this->itemId = $existingItem->id;
            } else {
                // Insert new item using model
                $newItem = $dispatchItemModel->create([
                    'external_id' => $item['external_id'],
                    'dispatch_id' => $this->dispatchId,
                    'description' => $item['description'],
                    'status' => $item['status'] ?? 'Open'
                ]);
                $this->itemId = $newItem->id;

                // Use DispatchItemSymptom model for symptoms
                $dispatchItemSymptomModel = new DispatchItemSymptom();
                $symptom = $dispatchItemSymptomModel->create([
                    'dispatch_id' => $this->dispatchId,
                    'item_id' => $this->itemId,
                    'content' => json_encode($item['symptoms'] ?? [])
                ]);
                $this->symptomId = $symptom->id;
            }
        }
    }

    public function addPayments($outbound) {
        $data = $outbound[0];      
        if (!isset($data['payments'])) {
            return;
        }  
        $payments = $data['payments'];

        if ($this->dispatchId) {
            // Use DispatchPayment model
            $dispatchPaymentModel = new DispatchPayment();
            $existingPayment = $dispatchPaymentModel->where('dispatch_id', $this->dispatchId)->first();
            
            if ($existingPayment) {
                // Update existing payment record using model
                $existingPayment->total = $payments['total'];
                $existingPayment->paid = $payments['paid'];
                $existingPayment->remaining = $payments['remaining'];
                $existingPayment->save();
            } else {
                // Insert new payment record using model
                $dispatchPaymentModel->create([
                    'dispatch_id' => $this->dispatchId,
                    'total' => $payments['total'],
                    'paid' => $payments['paid'],
                    'remaining' => $payments['remaining']
                ]);
            }
        }
    }

    public function addCoverages($outbound) {
        $data = $outbound[0];   
        if (!isset($data['coverage'])) {
            return;
        }
        $coverage = $data['coverage'];

        $details = $coverage['details'];
        $notes = $coverage['notes'];

        // Use DispatchCoverage model
        $dispatchCoverageModel = new DispatchCoverage();
        $existingCoverage = $dispatchCoverageModel->where('dispatch_id', $this->dispatchId)->first();
        
        if ($existingCoverage) {
            // Update existing coverage record using model
            $existingCoverage->header = $details['header'];
            $existingCoverage->content = $details['content'];
            $existingCoverage->save();
            $this->coverageId = $existingCoverage->id;
        } else {
            // Insert new coverage record using model
            $newCoverage = $dispatchCoverageModel->create([
                'dispatch_id' => $this->dispatchId,
                'header' => $details['header'],
                'content' => $details['content']
            ]);
            $this->coverageId = $newCoverage->id;
        }

        // Use DispatchCoverageNote model for notes
        $dispatchCoverageNoteModel = new DispatchCoverageNote();
        foreach($notes as $note) {
            $dispatchCoverageNoteModel->create([
                'dispatch_id' => $this->dispatchId,
                'coverage_id' => $this->coverageId,
                'note' => $note
            ]);
        }
        // Store the ID of the last note created
        $lastNote = $dispatchCoverageNoteModel->where('coverage_id', $this->coverageId)
            ->orderBy('id', 'desc')
            ->first();
        if ($lastNote) {
            $this->notesId = $lastNote->id;
        }
    }

    public function addHighlights($outbound) {
        $data = $outbound[0];   
        if (!isset($data['highlights'])) {
            return;
        }
        $highlights = $data['highlights'];

        // Use DispatchHighlight model
        $dispatchHighlightModel = new DispatchHighlight();
        
        foreach($highlights as $highlight) {
            $dispatchHighlightModel->create([
                'dispatch_id' => $this->dispatchId,
                'header' => $highlight['header'],
                'content' => $highlight['content']
            ]);
        }
                
    }

    public function addContracts($outbound) {
        $data = $outbound[0];
        if (!isset($data['contract'])) {
            return;
        }
        $contracts = $data['contract'];        
        // Use DispatchContract model
        $dispatchContractModel = new DispatchContract();
        $existingContract = $dispatchContractModel->where('dispatch_id', $this->dispatchId)->first();
        
        if ($existingContract) {
            // Update existing contract using model
            $existingContract->external_id = $contracts['external_id'];
            $existingContract->effectiveDate = $contracts['effectiveDate'];
            $existingContract->expiryDate = $contracts['expiryDate'];
            $existingContract->listingEffectiveDate = $contracts['listingEffectiveDate'];
            $existingContract->listingExpiryDate = $contracts['listingExpiryDate'];
            $existingContract->save();
            $this->contractId = $existingContract->id;
        } else {
            // Insert new contract using model
            $newContract = $dispatchContractModel->create([
                'dispatch_id' => $this->dispatchId,                
                'external_id' => $contracts['external_id'],
                'effectiveDate' => isset($contracts['effectiveDate']) ? $contracts['effectiveDate'] : '',
                'expiryDate' => isset($contracts['expiryDate']) ? $contracts['expiryDate'] : '',
                'listingEffectiveDate' => isset($contracts['listingEffectiveDate']) ? $contracts['listingEffectiveDate'] : '',
                'listingExpiryDate' => isset($contracts['listingExpiryDate']) ? $contracts['listingExpiryDate'] : ''
            ]);
            $this->contractId = $newContract->id;
        }
    }

    public function addCustomers($outbound) {
        $data = $outbound[0];
        if (!isset($data['contract'])) {
            return;
        }
        $contracts = $data['contract'];

        if (!isset($contracts['customers'])) {
            return;
        }
        $customers = $contracts['customers'];
        $customer = $customers[0];

        // Use Customer model
        $customerModel = new Customer();
        $existingCustomer = $customerModel->where('external_id', $customer['external_id'])->first();

        $env = $this->environment == 'prod' ? 'production' : 'sandbox';
        
        if ($existingCustomer) {
            // Update existing customer using model
            $existingCustomer->name = $customer['name'];
            $existingCustomer->email = $customer['email'];
            $existingCustomer->preferredCommunicationType = $customer['preferredCommunicationType'];
            $existingCustomer->source = 'frontdoor';
            $existingCustomer->env = $env;
            $existingCustomer->save();
            $this->customerId = $existingCustomer->id;
        } else {
            // Insert new customer using model
            $newCustomer = $customerModel->create([
                'external_id' => $customer['external_id'],
                'name' => $customer['name'],
                'email' => $customer['email'],
                'preferredCommunicationType' => $customer['preferredCommunicationType'],
                'source' => 'frontdoor',
                'env' => $env
            ]);
            $this->customerId = $newCustomer->id;
        }

        $property = $customer['property'];
        
        // Use CustomerProperty model
        $customerPropertyModel = new CustomerProperty();
        $existingProperty = $customerPropertyModel->where('customer_id', $this->customerId)
            ->where('external_id', $property['external_id'])
            ->first();
        
        if ($existingProperty) {
            // Update existing property using model
            $existingProperty->city = $property['address']['city'];
            $existingProperty->state = $property['address']['state'];
            $existingProperty->streetDirection = $property['address']['streetDirection'];
            $existingProperty->streetName = $property['address']['streetName'];
            $existingProperty->streetNumber = $property['address']['streetNumber'];
            $existingProperty->unitNumber = $property['address']['unitNumber'];
            $existingProperty->unitType = $property['address']['unitType'];
            $existingProperty->zip = $property['address']['zip'];
            $existingProperty->zipFour = $property['address']['zipFour'];
            $existingProperty->save();
            $this->propertyId = $existingProperty->id;
        } else {
            // Insert new property using model
            $newProperty = $customerPropertyModel->create([
                'customer_id' => $this->customerId,
                'external_id' => $property['external_id'],
                'city' => $property['address']['city'],
                'state' => $property['address']['state'],
                'streetDirection' => $property['address']['streetDirection'],
                'streetName' => $property['address']['streetName'],
                'streetNumber' => $property['address']['streetNumber'],
                'unitNumber' => $property['address']['unitNumber'],
                'unitType' => $property['address']['unitType'],
                'zip' => $property['address']['zip'],
                'zipFour' => $property['address']['zipFour']
            ]);
            $this->propertyId = $newProperty->id;
        }

        $phones = $data['dispatch']['customers'][0]['phone'];
        
        // Use CustomerPhone model
        $customerPhoneModel = new CustomerPhone();
        foreach ($phones as $phone) {
            $existingPhone = $customerPhoneModel->where('customer_id', $this->customerId)
                ->where('phone', $phone['number'])
                ->where('phone_type', $phone['type'])
                ->first();
            
            if ($existingPhone) {
                // Update existing phone using model
                $existingPhone->phone = $phone['number'];
                $existingPhone->phone_type = $phone['type'];
                $existingPhone->save();
            } else {
                // Insert new phone using model
                $customerPhoneModel->create([
                    'customer_id' => $this->customerId,
                    'phone' => $phone['number'],
                    'phone_type' => $phone['type']
                ]);
            }
        }

        // Use DispatchCustomer model for the relationship
        $dispatchCustomerModel = new DispatchCustomer();
        $existingDispatchCustomer = $dispatchCustomerModel->where('customer_id', $this->customerId)
            ->where('dispatch_id', $this->dispatchId)
            ->first();
        
        if (!$existingDispatchCustomer) {
            // Insert new record if it doesn't exist
            $dispatchCustomerModel->create([
                'customer_id' => $this->customerId,
                'dispatch_id' => $this->dispatchId
            ]);
        }

        // Use DispatchCustomerProperty model for the relationship
        $dispatchCustomerPropertyModel = new DispatchCustomerProperty();
        $existingDispatchCustomerProperty = $dispatchCustomerPropertyModel->where('customer_id', $this->customerId)
            ->where('dispatch_id', $this->dispatchId)
            ->where('property_id', $this->propertyId)
            ->first();
        
        if (!$existingDispatchCustomerProperty) {
            // Insert new record if it doesn't exist
            $dispatchCustomerPropertyModel->create([
                'customer_id' => $this->customerId,
                'dispatch_id' => $this->dispatchId,
                'property_id' => $this->propertyId
            ]);
        }

        if ($property['address']['zip']) {
            $coords = GoogleHelper::getCoordinates($property['address']['zip'], $this->tenantId);

            CustomerPropertyGeolocation::create([
                'property_id' => $this->propertyId,
                'lat' => $coords['lat'],
                'lng' => $coords['lng'],
                'zip' => $property['address']['zip']
            ]);
        }
    }

    public function addStatus() {
        if ($this->dispatchId) {
            $dispatchStatusModel = new DispatchStatus();
            $existingStatus = $dispatchStatusModel->where('dispatch_id', $this->dispatchId)->first();
            
            if ($existingStatus) {                
                $existingStatus->job_status = DispatchStatus::STATUS_JOB_ASSIGNED;
                $existingStatus->save();
            } else {                
                $dispatchStatus = new DispatchStatus();
                $dispatchStatus->job_status = DispatchStatus::STATUS_JOB_ASSIGNED;
                $dispatchStatus->dispatch_id = $this->dispatchId;
                $dispatchStatus->save();
            }
        }
    }
}










