{"name": "dipatchbutler-frontend-app", "version": "2.0.0", "private": true, "description": "New frontend application for dispatchbutler app", "repository": "https://github.com/developmentbutler/development_team.git", "license": "UNLICENSED", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "deploy": "yarn build && yarn gh-pages", "dev": "yarn start:dev", "gh-pages": "gh-pages -d dist", "postinstall": "max setup", "jest": "jest", "lint": "yarn lint:js && yarn lint:prettier && yarn tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "openapi": "max openapi", "prepare": "husky", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "yarn build && max preview --port 8000", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "serve": "umi-serve", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev PORT=3000 max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "build:sandbox": "cross-env REACT_APP_ENV=sandbox UMI_ENV=sandbox max build", "test": "jest", "test:coverage": "yarn jest -- --coverage", "test:update": "yarn jest -- -u", "tsc": "tsc --noEmit"}, "browserslist": ["defaults"], "dependencies": {"@ant-design/icons": "5.x", "@ant-design/pro-components": "^2.7.19", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@types/prop-types": "^15.7.15", "@types/react-beautiful-dnd": "^13.1.8", "@vis.gl/react-google-maps": "^1.5.4", "antd": "^5.25.4", "antd-style": "^3.7.0", "classnames": "^2.5.1", "dayjs": "^1.11.13", "prop-types": "^15.8.1", "react": "^19.1.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^19.1.0"}, "devDependencies": {"@ant-design/pro-cli": "^3.3.0", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.0.1", "@types/express": "^4.17.21", "@types/jest": "^29.5.13", "@types/lodash": "^4.17.10", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@types/react-helmet": "^6.1.11", "@umijs/lint": "^4.3.24", "@umijs/max": "^4.3.24", "cross-env": "^7.0.3", "eslint": "^8.57.1", "express": "^4.21.1", "gh-pages": "^6.1.1", "husky": "^9.1.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.10", "mockjs": "^1.1.0", "prettier": "^3.3.3", "ts-node": "^10.9.2", "typescript": "^5.6.3", "umi-presets-pro": "^2.0.3", "umi-serve": "^1.9.11"}, "engines": {"node": ">=12.0.0"}}