<?php

namespace Butler\Models;

use <PERSON>\Helpers\Func;
use Butler\Models\Dispatch\DispatchComment;
use Butler\Models\Dispatch\DispatchCoverageNote;
use Butler\Models\Dispatch\DispatchNcc;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Events\Dispatcher;

/**
 * * @method static create(array $input)
 * * @method static updateOrCreate(array $attributes, array $values)
 * * @method static findOrFail($id)
 * * @method static firstOrNew(array $attributes, array $values = [])
 * * @method static firstOrCreate(array $attributes, array $values = [])
 * * @method static Builder from(string $string)
 * * @method static insert(array $comments)
 */
class BaseModel extends Model
{
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';

    protected $hidden = array('pivot');

    public $timestamps = false;

    protected static function boot()
    {
        parent::boot();

        // NOTE: This is not done automatically by some reason.
        // So we manually create an instance of event here. 2022-11-25
        if (!isset(static::$dispatcher))
            static::setEventDispatcher(new Dispatcher());

        Relation::enforceMorphMap([
            'comment_type' => DispatchComment::class,
            'ncc_type' => DispatchNcc::class,
            'note_type' => DispatchCoverageNote::class,
            'admin_type' => DispatchComment::class,
            'text_type' => DispatchComment::class,
        ]);
    }

    protected static function booted()
    {
        parent::booted();

        // Register global model events
        /*static::creating(function(Model $model) {
            if (in_array('updated_by', $model->getFillable())) {
                $model->setAttribute('updated_by', Func::getSesUserId());
            }
            if (in_array('created_by', $model->getFillable())) {
                $model->setAttribute('created_by', Func::getSesUserId());
            }
        });

        static::updating(function(Model $model) {
            if (in_array('updated_by', $model->getFillable())) {
                $model->setAttribute('updated_by', Func::getSesUserId());
            }
        });

        static::updated(function(Model $model) {
        });*/
    }

    public static function findOrNew($id, $attributes = [])
    {
        $obj = static::find($id);
        return $obj ?: new static($attributes);
    }

    /**
     * @param mixed $item                   Input value
     * @param boolean $applyReverseCast     Need to specify this column for batch Insert or upsert.
     * @param boolean $unsetNull            Will avoid a bug for NULL set unnecessarily.
     *
     * @return array
     */
    public static function getBoundData(&$item, $applyReverseCast = false, $unsetNull = false): array
    {
        $tmpObj = new static($item);
        $attributes = $tmpObj->getFillable();

        // timestamps processing
        if ($tmpObj->timestamps) {
            $tmpObj->updateTimestamps();
        }

        // Create mode?
        if (!$tmpObj->getAttribute($tmpObj->primaryKey)) {
            if (in_array('created_by', $attributes)) {
                $tmpObj->created_by = Func::getAuthInContainer()['user_id'] ?? null;
            }
        }
        if (in_array('created_by', $attributes)) {
            $tmpObj->updated_by = Func::getAuthInContainer()['user_id'] ?? null;
        }
        if (in_array('tenant_id', $attributes)) {
            $tmpObj->tenant_id = Func::getAuthInContainer()['cur_tenant_id'] ?? null;
        }

        $data = [];
        foreach ($attributes as $key) {
            $data[$key] = $tmpObj->getAttributeValue($key);
        }

        // specific case in case CASTing fields are string type.
        if ($applyReverseCast) {
            foreach ($tmpObj->casts as $key => $type) {
                if ($type == 'array' || $type == 'json') {
                    $data[$key] = isset($data[$key]) ? json_encode($data[$key]) : null;
                }
            }
        }

        if ($unsetNull) {
            foreach ($data as $key => &$x) {
                if ($x === null) {
                    unset($data[$key]);
                }
            }
        }

        return $data;
    }

    /**
     * Useful for filtering existed attributes only for upsert/insert.
     *
     * @param $item
     * @param $applyReverseCast
     * @return array
     */
    public static function getBoundDataByExistedAttributes(&$item, $applyReverseCast = false): array
    {
        $tmpObj = new static($item);
        $attributes = $tmpObj->getFillable();

        // We filter by existed keys.
        $existedKeys = array_keys($item);

        $data = [];
        foreach ($attributes as $key) {
            if (in_array($key, $existedKeys)) {
                $data[$key] = $tmpObj->getAttributeValue($key);
            }
        }

        // specific case in case CASTing fields are string type.
        if ($applyReverseCast) {
            foreach ($tmpObj->casts as $key => $type) {
                if (key_exists($key, $data)) {
                    if ($type == 'array' || $type == 'json') {
                        $data[$key] = $data[$key] ? json_encode($data[$key]) : null;
                    }
                }
            }
        }
        return $data;
    }

    public static function getFullSql(\Illuminate\Database\Eloquent\Builder $eloquentQuery): string
    {
        $sql = $eloquentQuery->toSql();
        $bindings = $eloquentQuery->getQuery()->getBindings();

        foreach ($bindings as $binding) {
            // Escape the value depending on type
            $value = is_numeric($binding) ? $binding : "'" . addslashes($binding) . "'";
            $sql = preg_replace('/\?/', $value, $sql, 1);
        }

        return $sql;
    }

    /*public static function getDummyQuery(): \Illuminate\Database\Query\Builder {
        return Func::getDb()->query()->fromRaw("dual");
    }*/
}
