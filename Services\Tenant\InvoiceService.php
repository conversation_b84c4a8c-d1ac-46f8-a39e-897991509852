<?php

namespace Butler\Services\Tenant;

use Butler\Helpers\Func;
use Butler\Models\Dispatch\DispatchInvoice;
use Butler\Services\BaseService;
use Illuminate\Database\Eloquent\Builder;

class InvoiceService extends BaseService
{
    /**
     * @param $params
     * @return Builder
     */
    public function getQueryBuilder($params)
    {
        $qb = DispatchInvoice::query();

        if ($params) {
            $jobId = $params['dispatch_id'] ?? null;
            if ($jobId) {
                $qb->where('dispatch_id', $jobId);
            }

            if ($params['id'] ?? null) {
                $qb->where('id', $params['id']);
            }

            if ($params['tenant_id'] ?? null) {
                $qb->where('tenant_id', $params['tenant_id']);
            }

            if ($params['in_status'] ?? null) {
                $qb->whereIn('status', Func::csvToArr($params['in_status']));
            }
            if ($params['status'] ?? null) {
                $qb->where('status', $params['status']);
            }
            if ($params['dispatch_id'] ?? null) {
                $qb->where('dispatch_id', $params['dispatch_id']);
            }
            if ($params['invoice_no'] ?? null) {
                $qb->where('invoice_no', 'like', '%' . $params['invoice_no'] . '%');
            }

            if ($params['customer_zip'] ?? null) {
                $qb->where('customer_zip', $params['customer_zip']);
            }
            if ($params['customer_name'] ?? null) {
                $qb->where('customer_name', 'like', $params['customer_name'] . '%');
            }
            if ($params['customer_email'] ?? null) {
                $qb->where('customer_email', 'like', $params['customer_email'] . '%');
            }


            if (Func::keyExistsInWithParam($params, 'dispatch')) {
                $qb->with('dispatch');
            }
            if (Func::keyExistsInWithParam($params, 'customer')) {
                $qb->with('customer');
            }
            if (Func::keyExistsInWithParam($params, 'items')) {
                $qb->with('items');
            }
            if (Func::keyExistsInWithParam($params, 'logs')) {
                $qb->with('logs');
            }
        }
        return $qb;
    }
}