<?php

namespace Butler\Services\Tenant;

use Butler\Helpers\Func;
use Butler\Models\Dispatch\DispatchInvoice;
use Butler\Services\BaseService;
use Illuminate\Database\Eloquent\Builder;

class InvoiceService extends BaseService
{
    /**
     * @param $params
     * @return Builder
     */
    public function getQueryBuilder($params)
    {
        $qb = DispatchInvoice::query();

        if ($params) {
            $jobId = $params['dispatch_id'] ?? null;
            if ($jobId) {
                $qb->where('dispatch_id', $jobId);
            }

            if ($params['id'] ?? null) {
                $qb->where('id', $params['id']);
            }

            if ($params['tenant_id'] ?? null) {
                $qb->where('tenant_id', $params['tenant_id']);
            }

            if ($params['in_status'] ?? null) {
                $qb->whereIn('status', Func::csvToArr($params['in_status']));
            }
            if ($params['status'] ?? null) {
                $qb->where('status', $params['status']);
            }
            if ($params['dispatch_id'] ?? null) {
                $qb->where('dispatch_id', $params['dispatch_id']);
            }
            if ($params['customer_id'] ?? null) {
                $qb->where('customer_id', $params['customer_id']);
            }
            if ($params['dispatch_external_id'] ?? null) {
                $qb->whereHas('dispatch', function(Builder $builder) use (&$params) {
                    $builder->where('external_id', $params['dispatch_external_id']);
                });
            }

            if ($params['invoice_no'] ?? null) {
                $qb->where('invoice_no', 'like', '%' . $params['invoice_no'] . '%');
            }
            if ($ft_keyWords = $params['ft_keyWords'] ?? null) {
                $qb->where(function(Builder $qb) use(&$ft_keyWords) {
                    $ft_keyWords = '%' . $ft_keyWords . '%';
                    $qb->where('customer_name', 'like', $ft_keyWords);
                    $qb->orWhere('customer_phone', 'like', $ft_keyWords);
                    $qb->orWhere('customer_city', 'like', $ft_keyWords);
                    $qb->orWhere('customer_state', 'like', $ft_keyWords);
                    $qb->orWhere('customer_street_name', 'like', $ft_keyWords);
                    $qb->orWhere('customer_zip', 'like', $ft_keyWords);

                    $qb->where('billing_name', 'like', $ft_keyWords);
                    $qb->orWhere('billing_phone', 'like', $ft_keyWords);
                    $qb->orWhere('billing_city', 'like', $ft_keyWords);
                    $qb->orWhere('billing_state', 'like', $ft_keyWords);
                    $qb->orWhere('billing_street_name', 'like', $ft_keyWords);
                    $qb->orWhere('billing_zip', 'like', $ft_keyWords);
                });
            }


            if ($params['customer_zip'] ?? null) {
                $qb->where('customer_zip', $params['customer_zip']);
            }
            if ($params['customer_name'] ?? null) {
                $qb->where('customer_name', 'like', $params['customer_name'] . '%');
            }
            if ($params['customer_email'] ?? null) {
                $qb->where('customer_email', 'like', $params['customer_email'] . '%');
            }

            if (Func::keyExistsInWithParam($params, 'file')) {
                $qb->with('file');
            }
            if (Func::keyExistsInWithParam($params, 'dispatch')) {
                $qb->with('dispatch');
            }
            if (Func::keyExistsInWithParam($params, 'customer')) {
                $qb->with('customer');
            }
            if (Func::keyExistsInWithParam($params, 'items')) {
                $qb->with('items');
            }
            if (Func::keyExistsInWithParam($params, 'logs')) {
                $qb->with('logs');
            }
            if (Func::keyExistsInWithParam($params, 'estimate')) {
                $qb->with('estimate');
            }
        }
        return $qb;
    }

    /**
     * Apply sorting
     *
     * @param Builder $builder
     * @param array|null $params
     * @return Builder
     */
    public function applyOrderBy(Builder $builder, ?array $params): Builder
    {
        $sortDetail = $params['sort_detail'] ?? $params['sort'] ?? '';

        if ($sortDetail) {
            if (!is_array($sortDetail))
                $sortDetail = Func::safeJson($sortDetail);
            if ($sortDetail) {
                foreach ($sortDetail as $filed => $dir) {
                    $fieldName = str_replace(',', '.', $filed);
                    $dir = ($dir == 'descend') ? 'DESC' : 'ASC';
                    if ($fieldName == 'dispatch.external_id') {
                        $builder->orderBy(function (\Illuminate\Database\Query\Builder $builder) {
                            $builder->select('dispatches.external_id')
                                ->from('dispatches')
                                ->whereColumn('dispatches.id', 'dispatch_invoice.dispatch_id');
                        }, $dir);
                    } else {
                        $builder->orderBy($fieldName, $dir);
                    }
                }
            }
        }
        return $builder;
    }
}