<?php

/**
 * <PERSON>ron Script: Sync jobs from frontdoor_requests to dispatches table.
 */

use Butler\Models\FrontDoorRequest;
use Butler\Models\ButlerDB;
use Butler\Models\Dispatch\DispatchCustomer;
use Butler\Models\Dispatch\Dispatch;
use Butler\Models\System\SystemMessageTemplate;
use Butler\Models\Twilio\TwilioSmsLogs;
use Butler\Models\Dispatch\DispatchGreetingLog;

use <PERSON>\Lib\FrontDoor\DispatchOrg;
use Butler\Lib\FrontDoor\DispatchJob;
use Butler\Lib\FrontDoor\DispatchNote;
use Butler\Lib\FrontDoor\DispatchNCC;
use Butler\Lib\FrontDoor\DispatchStatus;
use Butler\Lib\FrontDoor\DispatchGreeting;
use Butler\Helpers\TwilioHelper;
use Butler\Helpers\EmployeeHelper;
use Twilio\Rest\Client as TwilioClient;

include_once '_cron_before.php';


$logFilePath = APP_PATH . DS . 'logs' . DS . 'sync_jobs_frontdoor.log';
$request_id = '';

try {
    $requests = FrontDoorRequest::query()
        ->where('status', 'received')
        ->limit(100)
        ->get();

    foreach ($requests as $request) {
        $data = $request->request_data;
        $tenantId = $request->tenant_id;        
        $request_id = $request->id;
                
        $dispatchOrg = new DispatchOrg();                
            
        // Process the request
        $orgId = $dispatchOrg->handle($data);

        $error = 0;

        if ($orgId) {
            if(isset($data[0]['operation']) && $data[0]['operation'] == "Schedule") {
                $dispatchJob = new DispatchJob($tenantId);
                
                $dispatchId = $dispatchJob->handle($data);

                if ($dispatchId) {
                    $dispatchJob->addItems($data);
                    $dispatchJob->addPayments($data);
                    $dispatchJob->addCoverages($data);
                    $dispatchJob->addHighlights($data);
                    $dispatchJob->addContracts($data);
                    $dispatchJob->addCustomers($data);
                    $dispatchJob->addStatus();

                    // Greeting Message
                    $dispatchCustomer = DispatchCustomer::where('dispatch_id', $dispatchId)->first();
                    
                    if ($dispatchCustomer) {
                        $customer = $dispatchCustomer->customer;                        
                        $customerPhones = $customer->customerPhones()->get();
                        $customerPhone = $customerPhones[0]->phone ?? null;
                        
                        $env = $_ENV['ENVIRONMENT'] === 'production' ? 'prod' : 'sandbox';
                        error_log(date('Y-m-d H:i:s') . " CRON: Env: {$env}\n", 3, $logFilePath);   
                        error_log(date('Y-m-d H:i:s') . " CRON: Customer Phones: " . json_encode($customerPhones) . "\n", 3, $logFilePath);   
                        if ($customerPhone && $env == 'prod') {
                            $data_log = json_encode($customerPhone);
                            error_log(date('Y-m-d H:i:s') . " CRON: Customer Phone found: {$data_log}\n", 3, $logFilePath);   

                            $dispatch = Dispatch::find($dispatchId); 
                            $qb = SystemMessageTemplate::query()->tenantOnly($tenantId);
                            $qb->where('code', SystemMessageTemplate::TYPE_CODE_GREETING);
                            $qb->where('is_active', 1);
                            $greeting = $qb->first();
                            if ($greeting) {

                                $qb = DispatchGreetingLog::query();
                                $qb->where('dispatch_id', $dispatch->id);
                                $qb->where('tenant_id', $tenantId);
                                $qb->where('to_phone_no', $customerPhone);
                                $qb->where('is_success', 1);
                                $qb->where('template_id', $greeting->id);
                                $greetingLog = $qb->first();

                                if (!$greetingLog || $greetingLog->is_success == 0) {
                                    // Check if current time is within business hours (9am-5pm EST, Monday-Friday)
                                    $currentTime = new \DateTime('now', new \DateTimeZone('America/New_York'));
                                    $currentHour = (int)$currentTime->format('G'); // 24-hour format without leading zeros
                                    $currentDayOfWeek = (int)$currentTime->format('N'); // 1 (Monday) through 7 (Sunday)
                                    
                                    // Use appropriate message based on time of day and day of week
                                    if ($currentHour >= 8 && $currentHour < 17 && $currentDayOfWeek <= 5) {
                                        // Business hours (9am-5pm EST, Monday-Friday)
                                        $message = $greeting->notes_in_hours;
                                    } else {
                                        // After hours or weekend
                                        $message = $greeting->notes_after_hours;
                                    }

                                    // Get Twilio keys
                                    $keys = TwilioHelper::getTwilioKeys($tenantId);
                                    
                                    // Get assigned number for the tenant
                                    $assignedNumber = $greeting->from_phone_no;
                                    $assignedNumberData = EmployeeHelper::getByNumber($assignedNumber);
                                        
                                    if ($assignedNumber && $assignedNumberData && $keys['twilio_sid'] && $keys['twilio_token']) {
                                        // Create Twilio client
                                        $client = new \Twilio\Rest\Client($keys['twilio_sid'], $keys['twilio_token']);
                                        
                                        // Send SMS
                                        try {
                                            $messageObj = $client->messages->create($customerPhone, [ // +17409570883
                                                'from' => $assignedNumber,
                                                'body' => $message
                                            ]);
                                            
                                            $user_type = $assignedNumberData['user_type']->user_type;
                                            TwilioSmsLogs::create([
                                                'tenant_id' => $tenantId,
                                                'from_number' => $assignedNumber,
                                                'to_number' => $customerPhone,
                                                'message' => $message,
                                                'direction' => 'outbound',
                                                'identity' => $user_type . '_' . $assignedNumberData['phone']->user_id,
                                                'user_id' => $assignedNumberData['phone']->user_id,
                                                'message_sid' => $messageObj->sid
                                            ]);

                                            if ($greetingLog) {
                                                $greetingLog->is_success = 1;
                                                $greetingLog->tries = (int)$greetingLog->tries + 1;
                                                $greetingLog->save();
                                            } else {
                                                DispatchGreetingLog::create([
                                                    'dispatch_id' => $dispatch->id,
                                                    'tenant_id' => $tenantId,
                                                    'template_id' => $greeting->id,
                                                    'message' => $message,
                                                    'from_phone_no' => $assignedNumber,
                                                    'to_phone_no' => $customerPhone,
                                                    'is_success' => 1,
                                                    'is_in_hour' => ($currentHour >= 8 && $currentHour < 17 && $currentDayOfWeek <= 5) ? 1 : 0,
                                                    'tries' => 1
                                                ]);
                                            }
                                            


                                        } catch (\Exception $e) {
                                            // Log SMS error but continue with job acceptance
                                            error_log("SMS sending failed: " . $e->getMessage());   
                                            if ($greetingLog) {
                                                $greetingLog->is_success = 0;
                                                $greetingLog->tries = (int)$greetingLog->tries + 1;
                                                $greetingLog->error_description = $e->getMessage();
                                                $greetingLog->save();
                                            } else {                             
                                                DispatchGreetingLog::create([
                                                    'dispatch_id' => $dispatchId,
                                                    'tenant_id' => $tenantId,
                                                    'template_id' => $greeting->id,
                                                    'message' => $message,
                                                    'from_phone_no' => $assignedNumber,
                                                    'to_phone_no' => $customerPhone,
                                                    'is_success' => 0,
                                                    'is_in_hour' => ($currentHour >= 8 && $currentHour < 17 && $currentDayOfWeek <= 5) ? 1 : 0,
                                                    'tries' => 1
                                                ]);
                                            }
                                        }
                                    }
                                }
                                
                            }    
                            error_log(date('Y-m-d H:i:s') . " CRON: sent greeting message for dispatch {$dispatchId} in request {$request->id}.\n", 3, $logFilePath);
                        }
                    }

                }                                                
            } elseif(isset($data[0]['operation']) && $data[0]['operation'] == "notes") {
                $dispatchNote = new DispatchNote($tenantId);
                $dispatchNote->handle($data);
            } elseif(isset($data[0]['operation']) && $data[0]['operation'] == "ncc") {
                $dispatchNCC = new DispatchNCC($tenantId);
                $dispatchNCC->handle($data);
            } elseif(isset($data[0]['operation']) && $data[0]['operation'] == "Status") {
                $dispatchStatus = new DispatchStatus($tenantId);
                $result = $dispatchStatus->handle($data);
                if (!$result) {
                    $request->status = 'unknown_status';    
                    $request->save();
                    $error = 1;
                    error_log(date('Y-m-d H:i:s') . " CRON: found out unknown_status in request {$request->id}.\n", 3, $logFilePath);
                }
            } else {
                $request->status = 'unknown_operation';    
                $request->save();                
                $error = 1;
                error_log(date('Y-m-d H:i:s') . " CRON: found out unknown_operation in request {$request->id}.\n", 3, $logFilePath);
            }
        }

        if (!$error) {
            $request->status = 'saved';    
            $request->save();                
            error_log(date('Y-m-d H:i:s') . " CRON: Saved request {$request->id}.\n", 3, $logFilePath);
        }
    }
    
} catch (\Exception $e) {
    // Log error
    error_log(date('Y-m-d H:i:s') . " - Frontdoor sync failed in request {$request_id}: " . $e->getMessage() . "\n", 3, $logFilePath);
}
