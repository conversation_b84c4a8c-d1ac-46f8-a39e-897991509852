<?php

namespace Butler\Services\SignNow;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class SignNowService
{
    private $apiKey;
    private $baseUrl;
    private $httpClient;

    public function __construct()
    {
        $this->apiKey = $_ENV['SIGNNOW_API_KEY'] ?? '';
        $this->baseUrl = 'https://api.signnow.com';
        $this->httpClient = new Client();
    }

    private function getHeaders()
    {
        return [
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/json'
        ];
    }

    public function uploadDocument($pdfPath, $documentName)
    {
        try {
            $response = $this->httpClient->post($this->baseUrl . '/document', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey
                ],
                'multipart' => [
                    [
                        'name' => 'file',
                        'contents' => fopen($pdfPath, 'r'),
                        'filename' => $documentName . '.pdf'
                    ]
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            return $data['id'] ?? null;
        } catch (RequestException $e) {
            $responseBody = $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : '';
            throw new \Exception('Failed to upload document to SignNow: ' . $e->getMessage() . ' Response: ' . $responseBody);
        }
    }

    public function createSigningInvite($documentId, $signerEmail, $signerName, $documentName)
    {
        try {
            // First, add a signature field to the document
            $this->addSignatureField($documentId);

            $payload = [
                'to' => [
                    [
                        'email' => $signerEmail,
                        'role_name' => 'Signer',
                        'role' => 'Signer',
                        'order' => 1,
                        'expiration_days' => 30,
                        'reminder' => 5
                    ]
                ],
                'from' => $_ENV['SIGNNOW_FROM_EMAIL'] ?? '<EMAIL>',
                'subject' => "Please sign: {$documentName}",
                'message' => "Please review and sign the attached document: {$documentName}"
            ];

            // Use the standard invite endpoint (now that we have fields)
            $response = $this->httpClient->post($this->baseUrl . "/document/{$documentId}/invite", [
                'headers' => $this->getHeaders(),
                'json' => $payload
            ]);

            return json_decode($response->getBody()->getContents(), true);
        } catch (RequestException $e) {
            $responseBody = $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : '';
            throw new \Exception('Failed to create signing invite: ' . $e->getMessage() . ' Response: ' . $responseBody);
        }
    }

    private function addSignatureField($documentId)
    {
        try {
            $payload = [
                'fields' => [
                    [
                        'type' => 'signature',
                        'page_number' => 0, // First page (0-indexed)
                        'x' => 100,         // X coordinate
                        'y' => 600,         // Y coordinate (near bottom of page)
                        'width' => 200,
                        'height' => 50,
                        'required' => true,
                        'role' => 'Signer'
                    ]
                ]
            ];

            $response = $this->httpClient->post($this->baseUrl . "/document/{$documentId}/field", [
                'headers' => $this->getHeaders(),
                'json' => $payload
            ]);

            return json_decode($response->getBody()->getContents(), true);
        } catch (RequestException $e) {
            $responseBody = $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : '';
            throw new \Exception('Failed to add signature field: ' . $e->getMessage() . ' Response: ' . $responseBody);
        }
    }

    public function getDocumentStatus($documentId)
    {
        try {
            $response = $this->httpClient->get($this->baseUrl . "/document/{$documentId}", [
                'headers' => $this->getHeaders()
            ]);

            return json_decode($response->getBody()->getContents(), true);
        } catch (RequestException $e) {
            $responseBody = $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : '';
            throw new \Exception('Failed to get document status: ' . $e->getMessage() . ' Response: ' . $responseBody);
        }
    }

    public function downloadSignedDocument($documentId)
    {
        try {
            $response = $this->httpClient->get($this->baseUrl . "/document/{$documentId}/download", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey
                ]
            ]);

            return $response->getBody()->getContents();
        } catch (RequestException $e) {
            $responseBody = $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : '';
            throw new \Exception('Failed to download signed document: ' . $e->getMessage() . ' Response: ' . $responseBody);
        }
    }
}