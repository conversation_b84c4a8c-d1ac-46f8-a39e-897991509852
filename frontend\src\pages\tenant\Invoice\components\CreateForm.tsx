import type { Dispatch, SetStateAction } from "react";
import React, { useRef, useState } from "react";
import type { ProFormInstance } from "@ant-design/pro-form";
import { ModalForm, ProFormText, ProFormTextArea, ProFormDatePicker, ProFormDigit, ProFormSelect, ProFormList } from "@ant-design/pro-form";
import { addDispatchInvoice } from "@/services/app/tenant/dispatch/dispatch-invoice";
import { message, Button, Card, Space, InputNumber, Input, Checkbox, AutoComplete } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import Util from "@/util";

import { request } from "umi";

const handleAdd = async (fields: API.DispatchInvoice) => {
  console.log(fields);
  return true;
  /* const hide = message.loading("Adding...", 0);
  const data = { ...fields };
  try {
    await addDispatchInvoice(data);
    message.success("Added successfully");
    return true;
  } catch (error: any) {
    Util.error("Adding failed, please try again!", error);
    return false;
  } finally {
    hide();
  } */
};

export type CreateFormProps = {
  values?: Partial<API.DispatchInvoice>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.DispatchInvoice) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>(null);
  const { modalVisible, handleModalVisible, onSubmit } = props;
  const [lineItems, setLineItems] = useState<API.DispatchInvoiceItem[]>([]);
  const [serviceOptions, setServiceOptions] = useState<{ value: string; label: string; data: any }[]>([]);

  // Search services from pricebook
  const searchServices = async (searchText: string) => {
    if (!searchText || searchText.length < 2) {
      setServiceOptions([]);
      return;
    }

    try {
      const response = await request(`/api/tenant/pricebook/service/search`, {
        method: "GET",
        params: {
          q: searchText,
          limit: 10,
        },
        withToken: true,
      });

      if (response && response.rows) {
        const options = response.rows.map((service: any) => ({
          value: service.name || service.service_name,
          label: `${service.name || service.service_name} - $${service.price || 0}`,
          data: service,
        }));
        setServiceOptions(options);
      }
    } catch (error) {
      console.error("Error searching services:", error);
      setServiceOptions([]);
    }
  };

  const addLineItem = () => {
    const newItem: API.DispatchInvoiceItem = {
      id: Date.now(), // temporary ID for new items
      name: "",
      description: "",
      qty: 1,
      price: 0,
      price_total: 0,
      taxable: 1,
      tax_percent: 0,
      tax_amount: 0,
      total: 0,
      sort_order: lineItems.length + 1,
    };
    setLineItems([...lineItems, newItem]);
  };

  const removeLineItem = (index: number) => {
    const newItems = lineItems.filter((_, i) => i !== index);
    setLineItems(newItems);
  };

  const updateLineItem = (index: number, field: keyof API.DispatchInvoiceItem, value: any) => {
    const newItems = [...lineItems];
    newItems[index] = { ...newItems[index], [field]: value };

    // Recalculate totals
    if (field === "qty" || field === "price") {
      const qty = field === "qty" ? value : newItems[index].qty || 0;
      const price = field === "price" ? value : newItems[index].price || 0;
      newItems[index].price_total = qty * price;
      newItems[index].total = newItems[index].price_total + (newItems[index].tax_amount || 0);
    }

    setLineItems(newItems);
  };

  const handleServiceSelect = (index: number, value: string, option: any) => {
    const newItems = [...lineItems];
    const serviceData = option.data;

    // Update the line item with service data
    newItems[index] = {
      ...newItems[index],
      service_id: serviceData.id,
      name: serviceData.name || serviceData.service_name,
      description: serviceData.description || "",
      price: serviceData.price || 0,
      price_total: (newItems[index].qty || 1) * (serviceData.price || 0),
      total: (newItems[index].qty || 1) * (serviceData.price || 0),
    };

    setLineItems(newItems);
  };

  return (
    <ModalForm
      title={"New Invoice"}
      width="1200px"
      open={modalVisible}
      onOpenChange={handleModalVisible}
      modalProps={{ maskClosable: false }}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 18 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd({
          ...value,
          items: lineItems,
        } as API.DispatchInvoice);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          setLineItems([]);
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      {/* Basic Invoice Information */}
      <ProFormText
        name="status"
        label="Status"
        placeholder="Enter invoice status"
        rules={[{ required: true, message: "Status is required" }]}
        initialValue="draft"
      />

      <ProFormDatePicker name="issue_date" label="Issue Date" rules={[{ required: true, message: "Issue date is required" }]} />

      <ProFormDatePicker name="valid_until" label="Valid Until" />

      <ProFormText name="payment_terms" label="Payment Terms" placeholder="e.g., Net 30" />

      <ProFormTextArea name="notes" label="Notes" placeholder="Additional notes for the invoice" fieldProps={{ rows: 3 }} />

      {/* Customer Information Card */}
      <Card title="Customer Information" style={{ marginTop: 16, marginBottom: 16 }}>
        <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "16px" }}>
          <ProFormText name="customer_company" label="Company" placeholder="Company name" labelCol={{ span: 24 }} wrapperCol={{ span: 24 }} />
          <ProFormText
            name="customer_name"
            label="Contact Name"
            placeholder="Contact person name"
            rules={[{ required: true, message: "Customer name is required" }]}
            labelCol={{ span: 24 }}
            wrapperCol={{ span: 24 }}
          />
          <ProFormText
            name="customer_email"
            label="Email"
            placeholder="<EMAIL>"
            rules={[{ type: "email", message: "Please enter a valid email" }]}
            labelCol={{ span: 24 }}
            wrapperCol={{ span: 24 }}
          />
          <ProFormText name="customer_phone" label="Phone" placeholder="Phone number" labelCol={{ span: 24 }} wrapperCol={{ span: 24 }} />
        </div>
      </Card>

      {/* Billing Address Card */}
      <Card title="Billing Address" style={{ marginTop: 16, marginBottom: 16 }}>
        <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "16px" }}>
          <ProFormText name="billing_company" label="Company" placeholder="Billing company name" labelCol={{ span: 24 }} wrapperCol={{ span: 24 }} />
          <ProFormText
            name="billing_name"
            label="Contact Name"
            placeholder="Billing contact name"
            labelCol={{ span: 24 }}
            wrapperCol={{ span: 24 }}
          />
          <ProFormText
            name="billing_email"
            label="Email"
            placeholder="<EMAIL>"
            rules={[{ type: "email", message: "Please enter a valid email" }]}
            labelCol={{ span: 24 }}
            wrapperCol={{ span: 24 }}
          />
          <ProFormText name="billing_phone" label="Phone" placeholder="Billing phone number" labelCol={{ span: 24 }} wrapperCol={{ span: 24 }} />
          <ProFormText name="billing_street_number" label="Street Number" placeholder="123" labelCol={{ span: 24 }} wrapperCol={{ span: 24 }} />
          <ProFormText name="billing_street_name" label="Street Name" placeholder="Main Street" labelCol={{ span: 24 }} wrapperCol={{ span: 24 }} />
          <ProFormText name="billing_city" label="City" placeholder="City name" labelCol={{ span: 24 }} wrapperCol={{ span: 24 }} />
          <ProFormText name="billing_state" label="State" placeholder="State" labelCol={{ span: 24 }} wrapperCol={{ span: 24 }} />
          <ProFormDigit
            name="billing_zip"
            label="ZIP Code"
            placeholder="12345"
            fieldProps={{ precision: 0 }}
            labelCol={{ span: 24 }}
            wrapperCol={{ span: 24 }}
          />
        </div>
      </Card>

      {/* Line Items Section */}
      <Card
        title="Line Items"
        style={{ marginTop: 16, marginBottom: 16 }}
        extra={
          <Button type="primary" icon={<PlusOutlined />} onClick={addLineItem}>
            Add Item
          </Button>
        }
      >
        <ProFormList
          name="lineItems"
          initialValue={lineItems}
          creatorButtonProps={false}
          itemRender={({ action }, { record, index }) => (
            <Card size="small" style={{ marginBottom: 8 }} styles={{ body: { padding: "12px" } }} extra={action}>
              <Space direction="vertical" style={{ width: "100%" }}>
                <AutoComplete
                  placeholder="Search and select service"
                  value={record?.name}
                  options={serviceOptions}
                  onSearch={searchServices}
                  onSelect={(value, option) => handleServiceSelect(index, value, option)}
                  onChange={(value) => updateLineItem(index, "name", value)}
                  style={{ width: "100%" }}
                  filterOption={false}
                />
                <Input placeholder="Description" value={record?.description} onChange={(e) => updateLineItem(index, "description", e.target.value)} />
                <Space wrap>
                  <InputNumber
                    placeholder="Qty"
                    min={0}
                    precision={2}
                    value={record?.qty}
                    onChange={(value) => updateLineItem(index, "qty", value || 0)}
                    style={{ width: 100 }}
                  />
                  <InputNumber
                    placeholder="Price"
                    min={0}
                    precision={2}
                    value={record?.price}
                    onChange={(value) => updateLineItem(index, "price", value || 0)}
                    style={{ width: 120 }}
                    formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")}
                    parser={(value) => parseFloat(value!.replace(/\$\s?|(,*)/g, "")) || 0}
                  />
                  <Checkbox checked={record?.taxable === 1} onChange={(e) => updateLineItem(index, "taxable", e.target.checked ? 1 : 0)}>
                    Taxable
                  </Checkbox>
                  <span style={{ minWidth: 80, textAlign: "right", fontWeight: "bold" }}>
                    ${((record?.qty || 0) * (record?.price || 0)).toFixed(2)}
                  </span>
                </Space>
              </Space>
            </Card>
          )}
        />

        {lineItems.length === 0 && (
          <div style={{ textAlign: "center", padding: "20px", color: "#999" }}>
            No line items added yet. Click &quot;Add Item&quot; to get started.
          </div>
        )}

        {lineItems.length > 0 && (
          <div style={{ marginTop: 16, textAlign: "right" }}>
            <Space direction="vertical" size="small">
              <div>
                <strong>Subtotal: ${lineItems.reduce((sum, item) => sum + (item.qty || 0) * (item.price || 0), 0).toFixed(2)}</strong>
              </div>
              <div>
                <strong>Total Items: {lineItems.length}</strong>
              </div>
            </Space>
          </div>
        )}
      </Card>
    </ModalForm>
  );
};

export default CreateForm;
