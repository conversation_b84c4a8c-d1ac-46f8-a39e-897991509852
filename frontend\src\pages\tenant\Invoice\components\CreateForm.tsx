import type { Dispatch, SetStateAction } from "react";
import React, { useEffect, useMemo, useRef, useState } from "react";
import type { ProFormInstance } from "@ant-design/pro-form";
import { ModalForm, ProFormText, ProFormTextArea, ProFormDatePicker, ProFormDigit, ProFormSelect, ProFormList } from "@ant-design/pro-form";
import { message, Button, Card, Space, InputNumber, Input, Checkbox, AutoComplete, Col, Row, Spin, Typography, Tag, Alert } from "antd";
import { DeleteOutlined, PlusOutlined, SearchOutlined } from "@ant-design/icons";
import Util, { sn } from "@/util";

import styles from "./CreateForm.less";
import { getPbServiceACList } from "@/services/app/tenant/pricebook/service";
import { getDispatchListByPage } from "@/services/app/tenant/dispatch/dispatch";
import dayjs from "dayjs";
import { addDispatchInvoice } from "@/services/app/tenant/dispatch/dispatch-invoice";

type InvoiceLineItemType = API.DispatchInvoiceItem & { uid?: any };

const handleAdd = async (fields: API.DispatchInvoice) => {
  console.log(fields);
  const hide = message.loading("Adding...", 0);
  const data = { ...fields };
  try {
    await addDispatchInvoice(data);
    message.success("Added successfully");
    return true;
  } catch (error: any) {
    Util.error("Adding failed, please try again!", error);
    return false;
  } finally {
    hide();
  }
};

export type CreateFormProps = {
  values?: Partial<API.DispatchInvoice>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.DispatchInvoice) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>(null);
  const { modalVisible, handleModalVisible, onSubmit } = props;

  // dispatch
  const [loading, setLoading] = useState<boolean>(false);
  const [dispatch, setDispatch] = useState<API.Dispatch | null>(null);

  const [discountAmount, setDiscountAmount] = useState<number>(0);
  const [taxPercent, setTaxPercent] = useState<number>(0);
  const [lineItems, setLineItems] = useState<InvoiceLineItemType[]>([]);
  const [serviceOptions, setServiceOptions] = useState<{ value?: string; label?: string; data?: any }[]>([]);

  // Search services from pricebook
  const searchServices = async (searchText: string) => {
    if (!searchText || searchText.length < 2) {
      setServiceOptions([]);
      return;
    }

    try {
      const options = await getPbServiceACList({ keyWords: searchText });
      setServiceOptions(options);
    } catch (error) {
      console.error("Error searching services:", error);
      setServiceOptions([]);
    }
  };

  const addLineItem = () => {
    const newItem: InvoiceLineItemType = {
      uid: Date.now(), // temporary ID for new items
      name: "",
      description: "",
      qty: 1,
      price: 0,
      price_total: 0,
      taxable: 1,
      tax_percent: 0,
      tax_amount: 0,
      total: 0,
      sort_order: lineItems.length + 1,
    };
    const newLineItems = [...lineItems, newItem];
    setLineItems(newLineItems);

    // Update the form field as well
    if (formRef.current) {
      formRef.current.setFieldsValue({
        lineItems: newLineItems,
      });
    }
  };

  const updateLineItem = (index: number, field: keyof InvoiceLineItemType, value: any) => {
    const newItems = [...lineItems];
    newItems[index] = { ...newItems[index], [field]: value };

    // Recalculate totals
    if (field === "qty" || field === "price") {
      const qty = field === "qty" ? value : newItems[index].qty || 0;
      const price = field === "price" ? value : newItems[index].price || 0;
      newItems[index].price_total = qty * price;
      newItems[index].total = newItems[index].price_total + (newItems[index].tax_amount || 0);
    }

    setLineItems(newItems);

    // Update the form field as well
    if (formRef.current) {
      formRef.current.setFieldsValue({
        lineItems: newItems,
      });
    }
  };

  const handleServiceSelect = (index: number, value: string, option: any) => {
    const newItems = [...lineItems];
    const serviceData = option.data;

    // Update the line item with service data
    newItems[index] = {
      ...newItems[index],
      service_id: serviceData.id,
      name: serviceData.name || serviceData.service_name,
      description: serviceData.description || "",
      price: serviceData.price || 0,
      price_total: (newItems[index].qty || 1) * (serviceData.price || 0),
      total: (newItems[index].qty || 1) * (serviceData.price || 0),
    };

    setLineItems(newItems);

    // Update the form field as well
    if (formRef.current) {
      formRef.current.setFieldsValue({
        lineItems: newItems,
      });
    }
  };

  const updateFormFieldsByDispatch = (job?: API.Dispatch) => {
    if (job) {
      const newFormValues: API.DispatchInvoice = {
        dispatch_id: job.id,
        customer_id: job.customer?.id,
        billing_company: "",
        billing_name: job.customer?.name,
        billing_email: job.customer?.email,
        billing_phone: job.customer?.customer_phones?.[0]?.phone,
        billing_street_number: job.address?.streetNumber,
        billing_street_name: job.address?.streetName,
        billing_city: job.address?.city,
        billing_state: job.address?.state,
        billing_zip: job.address?.zip,
        service_date: job.dispatch_appointment?.appointment_date ?? "",
      };
      formRef.current?.setFieldsValue(newFormValues);
    }
  };

  const handleSearchJob = () => {
    const search_dispatch_id = formRef.current?.getFieldValue("search_dispatch_id");
    const search_external_id = formRef.current?.getFieldValue("search_external_id");

    if (search_dispatch_id || search_external_id) {
      setLoading(true);
      getDispatchListByPage({ id: search_dispatch_id, external_id: search_external_id, with: "dispatchAppointment,customer.customerPhones" })
        .then((res) => {
          setDispatch(res.data?.[0]);
          updateFormFieldsByDispatch(res.data?.[0]);
        })
        .catch((err) => {
          setDispatch(null);
          console.error(err);
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      message.info("Fill Job ID# or External ID# to search job.");
    }
  };

  useEffect(() => {
    if (modalVisible) {
      formRef.current?.resetFields();
      formRef.current?.setFieldsValue({
        issue_date: dayjs().format("YYYY-MM-DD"),
        tax_percent: null,
        discount_amount: null,
      });
      setLineItems([]);
    }
  }, [modalVisible]);

  const subTotal = useMemo(() => {
    return lineItems.reduce((sum, item) => sum + (item.qty || 0) * (item.price || 0), 0).toFixed(2);
  }, [lineItems]);

  const taxTotal = useMemo(() => {
    return lineItems.reduce((sum, item) => sum + ((item.qty || 0) * (item.price || 0) * (item.taxable ? sn(taxPercent) : 0)) / 100, 0).toFixed(2);
  }, [lineItems, taxPercent]);

  const grandTotal = useMemo(() => {
    return (sn(subTotal) + sn(taxTotal) - sn(discountAmount)).toFixed(2);
  }, [discountAmount, subTotal, taxTotal]);

  console.log("dispatch", dispatch);

  return (
    <ModalForm
      title={"New Invoice"}
      width="1200px"
      open={modalVisible}
      onOpenChange={handleModalVisible}
      modalProps={{ maskClosable: false }}
      layout="horizontal"
      labelCol={{ flex: "100px" }}
      formRef={formRef}
      className={styles.createForm}
      onFinish={async (value) => {
        console.log("form values: ", value);
        if (!lineItems.length) {
          message.error("Please add line items of this invoice!");
          return Promise.reject(true);
        }

        const newValue = {
          ...value,
          dispatch_id: dispatch?.id,
          customer_id: dispatch?.customer?.id,
          items: lineItems,
        } as API.DispatchInvoice;
        delete (newValue as any).lineItems;

        console.log("new form values: ", newValue);

        const success = await handleAdd(newValue);
        if (success) {
          /* if (formRef.current) formRef.current.resetFields();
          setLineItems([]); */
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <Row gutter={8} wrap={false} style={{ alignItems: "center", marginBottom: 16 }}>
        <Col>
          <ProFormText
            name="search_dispatch_id"
            width={100}
            placeholder="Job ID"
            formItemProps={{ style: { marginBottom: 0 } }}
            fieldProps={{
              onKeyDown: (e) => {
                if (Util.isEnterKey(e)) {
                  handleSearchJob();
                }
              },
            }}
          />
        </Col>
        <Col>
          <ProFormText
            name="search_external_id"
            width={130}
            placeholder="External ID"
            formItemProps={{ style: { marginBottom: 0 } }}
            fieldProps={{
              onKeyDown: (e) => {
                if (Util.isEnterKey(e)) {
                  handleSearchJob();
                }
              },
            }}
          />
        </Col>
        <Col>
          <Button
            type="primary"
            icon={<SearchOutlined />}
            disabled={loading}
            onClick={() => {
              handleSearchJob();
            }}
          />
        </Col>
        {dispatch ? (
          <Col flex="auto">
            <Spin spinning={loading}>
              <Space size={24} style={{ marginLeft: 120, alignItems: "center" }}>
                <h4 style={{ marginBottom: 0 }}>
                  {`Job ID: `} <Tag color="blue">{`${dispatch.id}`}</Tag>
                </h4>
                <h4 style={{ marginBottom: 0 }}>
                  {`External ID: `}
                  <Tag color="geekblue">{`${dispatch.external_id}`}</Tag>
                </h4>
                <h4 style={{ marginBottom: 0 }}>{`${dispatch.customer?.name}`}</h4>
                <Typography.Text ellipsis copyable style={{ marginBottom: 0 }}>{`${dispatch.address?.full_address}`}</Typography.Text>
              </Space>
            </Spin>
          </Col>
        ) : (
          <div style={{ marginLeft: 120 }}>
            <Alert type="warning" message="No job found!" style={{ paddingTop: 4, paddingBottom: 4 }} />
          </div>
        )}
      </Row>

      <Row>
        <Col span={8}>
          <ProFormText name="id" label="Invoice No#" width="sm" disabled placeholder="" />
        </Col>

        <Col span={6}>
          <ProFormDatePicker
            name="issue_date"
            label="Issue Date"
            rules={[{ required: true, message: "Issue date is required" }]}
            format="MM/DD/YYYY"
          />
        </Col>
        <Col flex="auto">
          <ProFormText name="payment_terms" label="Payment Terms" placeholder="e.g., Net 30" />
        </Col>
      </Row>
      <Row>
        <Col span={8}>
          <ProFormText name="estimate_no" label="Estimate No#" disabled placeholder={""} width="sm" />
        </Col>
        <Col span={6}>
          <ProFormDatePicker
            name="service_date"
            label="Service Date"
            placeholder="Service Date"
            rules={[{ required: true, message: "Service date is required" }]}
            format="MM/DD/YYYY"
          />
        </Col>
        <Col span={5}>
          <ProFormDigit
            name="tax_percent"
            label="Tax %"
            placeholder="Tax %"
            fieldProps={{
              precision: 2,
              onChange(value) {
                setTaxPercent(sn(value));
              },
            }}
          />
        </Col>
      </Row>
      <Row>
        <Col span={8}>
          <ProFormSelect
            name="status"
            label="Status"
            width="sm"
            placeholder="Enter invoice status"
            rules={[{ required: true, message: "Status is required" }]}
            options={[
              { label: "Draft", value: "draft" },
              { label: "Sent", value: "sent" },
              { label: "Paid", value: "paid" },
              { label: "Cancelled", value: "cancelled" },
            ]}
            initialValue="draft"
          />
        </Col>
      </Row>

      <Row gutter={0}>
        <Col span={12}>
          <Card title="Billing Address" size="small" style={{ marginTop: 16, boxShadow: "none" }} variant="borderless">
            <ProFormText name="billing_street_number" label="Street Number" placeholder="123" width="sm" />
            <ProFormText
              name="billing_street_name"
              label="Street Name"
              placeholder="Main Street"
              rules={[{ type: "string", message: "Please enter a valid street" }]}
              required
            />
            <ProFormText
              name="billing_city"
              label="City"
              placeholder="City name"
              width="md"
              rules={[{ type: "string", message: "Please enter a valid city" }]}
              required
            />
            <Row>
              <Col span={14}>
                <ProFormText
                  name="billing_state"
                  label="State"
                  placeholder="State"
                  formItemProps={{ style: { display: "flex", flex: "0 0 40%" } }}
                  rules={[{ type: "string", message: "Please enter a valid state" }]}
                  required
                />
              </Col>
              <Col span={10}>
                <ProFormDigit
                  name="billing_zip"
                  label="ZIP Code"
                  placeholder="12345"
                  fieldProps={{ precision: 0 }}
                  rules={[{ type: "string", message: "Please enter a valid zip code" }]}
                  required
                />
              </Col>
            </Row>
          </Card>
        </Col>

        <Col span={12}>
          <Card title=" " size="small" style={{ marginTop: 16, boxShadow: "none" }} variant="borderless">
            <ProFormText name="billing_company" label="Company" placeholder="Billing company name" />
            <ProFormText name="billing_name" label="Contact Name" placeholder="Billing contact name" width="md" />
            <ProFormText name="billing_phone" label="Phone" placeholder="Billing phone number" width="sm" />
            <ProFormText
              name="billing_email"
              label="Email"
              placeholder="<EMAIL>"
              rules={[{ type: "email", message: "Please enter a valid email" }]}
              required
              width="md"
            />
          </Card>
        </Col>
      </Row>

      {/* Line Items Section */}
      <Card title="Line Items" size="small" style={{ marginTop: 16, boxShadow: "none", width: "100%" }} variant="borderless">
        <ProFormList<InvoiceLineItemType>
          name="lineItems"
          creatorButtonProps={false}
          wrapperCol={{ span: 24 }}
          actionRender={(field, action) => [
            <Button
              key="btn-delete"
              type="default"
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => {
                // Remove from form
                action.remove(field.key);

                // Also update local state
                const newItems = lineItems.filter((_, index) => index !== field.key);
                setLineItems(newItems);
              }}
            />,
          ]}
          itemRender={({ action }, { record, index }) => (
            <div style={{ marginBottom: 8, paddingBottom: 8, borderBottom: "1px solid #f0f0f0", width: "100%" }}>
              <div style={{ display: "flex", alignItems: "center", gap: "12px", marginBottom: "8px" }}>
                <div style={{ flex: 1, minWidth: "300px" }}>
                  <AutoComplete
                    placeholder="Search and select service"
                    value={record?.name}
                    options={serviceOptions}
                    onSearch={searchServices}
                    onSelect={(value, option) => handleServiceSelect(index, value, option)}
                    onChange={(value) => updateLineItem(index, "name", value)}
                    style={{ width: "100%" }}
                    filterOption={false}
                  />
                </div>
                <InputNumber
                  placeholder="Qty"
                  min={0}
                  precision={2}
                  value={record?.qty}
                  onChange={(value) => updateLineItem(index, "qty", value || 0)}
                  style={{ width: 100 }}
                />
                <InputNumber
                  placeholder="Price"
                  min={0}
                  precision={2}
                  value={record?.price}
                  onChange={(value) => updateLineItem(index, "price", value || 0)}
                  style={{ width: 120 }}
                  formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")}
                  parser={(value) => parseFloat(value!.replace(/\$\s?|(,*)/g, "")) || 0}
                />
                <Checkbox checked={record?.taxable === 1} onChange={(e) => updateLineItem(index, "taxable", e.target.checked ? 1 : 0)}>
                  Taxable
                </Checkbox>
                <span style={{ minWidth: 80, textAlign: "right", fontWeight: "bold" }}>
                  ${((record?.qty || 0) * (record?.price || 0)).toFixed(2)}
                </span>
                <div style={{ marginLeft: 32 }}>{action}</div>
              </div>

              {/* Description row - under the name */}
              <div style={{ paddingLeft: "0px" }}>
                <Input
                  placeholder="Description"
                  value={record?.description}
                  onChange={(e) => updateLineItem(index, "description", e.target.value)}
                  style={{ width: "50%" }}
                />
              </div>
            </div>
          )}
        />

        <Button icon={<PlusOutlined />} onClick={() => addLineItem()} size="small" type="primary">
          Add line item
        </Button>

        {lineItems.length > 0 && (
          <div style={{ marginTop: 16, textAlign: "right", maxWidth: 400, marginLeft: "auto" }}>
            <Row style={{ alignItems: "center" }}>
              <Col flex="auto">
                <strong>Subtotal: </strong>
              </Col>
              <Col flex={"200px"}>
                <strong>${subTotal}</strong>
              </Col>
            </Row>
            <Row style={{ alignItems: "center" }}>
              <Col flex="auto">
                <strong>Discount: </strong>
              </Col>
              <Col flex={"200px"}>
                <ProFormDigit
                  name="discount_amount"
                  width={120}
                  fieldProps={{
                    onChange(value) {
                      setDiscountAmount(sn(value));
                    },
                  }}
                />
              </Col>
            </Row>
            <Row style={{ alignItems: "center" }}>
              <Col flex="auto">
                <strong>Tax: </strong>
              </Col>
              <Col flex={"200px"}>
                <strong>${taxTotal}</strong>
              </Col>
            </Row>
            <Row style={{ alignItems: "center", marginTop: 8, paddingTop: 8, borderTop: "1px solid #f0f0f0" }}>
              <Col flex="auto">
                <strong>Total: </strong>
              </Col>
              <Col flex={"200px"}>
                <strong>${grandTotal}</strong>
              </Col>
            </Row>
            {/* <Space direction="vertical" size="small">
              <div></div>
              <div>
                <strong>Total Items: {lineItems.length}</strong>
              </div>
            </Space> */}
          </div>
        )}
      </Card>
      <ProFormTextArea name="notes" label="Notes" placeholder="Additional notes for the invoice" fieldProps={{ rows: 3 }} />
    </ModalForm>
  );
};

export default CreateForm;
