<?php

namespace Butler\Models\Tenant;

use Butler\Models\AddressTrait;
use Butler\Models\BaseModel;
use Butler\Models\Tenant;

/**
 * @property integer $id
 * @property integer $tenant_id
 * @property string $address_type
 * @property boolean $is_active
 * @property string $company
 * @property string $name
 * @property string $email
 * @property string $phone
 * @property string $city
 * @property string $state
 * @property string $street_direction
 * @property string $street_name
 * @property string $street_number
 * @property string $unit_number
 * @property string $unit_type
 * @property string $zip
 * @property string $zip_four
 * @property integer $created_by
 * @property string $created_at
 * @property integer $updated_by
 * @property string $updated_at
 *
 * @property Tenant $tenant
 *
 * // calc values
 * @property string $full_address
 */
class TenantAddress extends BaseModel
{
    use AddressTrait;

    public const ADDRESS_TYPE_BILLING = 'billing_address';
    public const ADDRESS_TYPE_SHIPPING = 'shipping_address';
    public const ADDRESS_TYPE_INVOICE_ADDRESS = 'invoice_address';

    public const ADDRESS_TYPES_KV = [
        self::ADDRESS_TYPE_BILLING => 'Billing',
        self::ADDRESS_TYPE_SHIPPING => 'Shipping',
        self::ADDRESS_TYPE_INVOICE_ADDRESS => 'Invoice',
    ];

    /**
     * @var array
     */
    protected $fillable = ['tenant_id', 'address_type', 'is_active', 'company', 'name', 'email', 'phone', 'city', 'state', 'street_direction', 'street_name', 'street_number', 'unit_number', 'unit_type', 'zip', 'zip_four', 'created_by', 'created_at', 'updated_by', 'updated_at'];


    protected $appends = [
        'full_address'
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function tenant()
    {
        return $this->belongsTo('Butler\Models\Tenant');
    }

    /**
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param $tenant_id
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeTenantOnly(\Illuminate\Database\Eloquent\Builder $query, $tenant_id = null)
    {
        if ($tenant_id) {
            $query->where('tenant_id', $tenant_id);
        }
    }
}
