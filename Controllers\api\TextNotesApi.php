<?php

namespace Butler\Controllers\api;

use <PERSON>\Lib\Auth\AuthMiddleware;
use <PERSON>\Lib\Auth\EmployeeAuthMiddleware;
use Butler\Models\ButlerDB;
use Butler\Controllers\api\ApiBaseController;
use Butler\Models\Dispatch\Dispatch;
use Butler\Models\Dispatch\DispatchUser;
use Butler\Models\Dispatch\DispatchMessage;
use Butler\Models\Dispatch\DispatchAppointment;
use Butler\Models\Dispatch\DispatchCoverageNote;
use Butler\Models\Dispatch\DispatchComment;
use Butler\Models\Dispatch\DispatchNcc;
use Butler\Models\Dispatch\DispatchCommentAttachment;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use PDO;

class TextNotesApi extends ApiBaseController
{

    public function getEmployeeTextNotes()
    {
        header('Content-Type: application/json');
        $dispatch_id = $_GET['job_id'] ?? null;

        if (!$dispatch_id) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Job ID is required']);
            return;
        }

        try {
            $auth = EmployeeAuthMiddleware::authenticate();

            $qb = DispatchMessage::query();
            $qb->where('dispatch_id', $dispatch_id);
            $qb->where('visible', 'Y');
            $qb->where('message_type', 'text_type');

            $qb->with('details', function (MorphTo $builder) use (&$auth) {
                $builder->withAttachments($auth);
            });
            
            $qb->orderByDesc('id');
            $messages = $qb->get()->toArray();

            // $messages = [];
            // foreach ($dispatchMessages as $message) {
            //     $messageDetails = null;
                
            //     if ($message['message_type'] === 'note_type') {
            //         $qb = DispatchCoverageNote::query();
            //         $qb->where('id', $message['message_id']);
            //         $messageDetails = $qb->first();
            //     } elseif ($message['message_type'] === 'comment_type') {
            //         $qb = DispatchComment::query();
            //         $qb->where('id', $message['message_id']);
            //         $qb->with('user:id,user_id,user_name');
            //         $messageDetails = $qb->first();
                    
            //         // Get attachments for each comment
            //         if (!empty($messageDetails)) {
            //             $attachmentQb = DispatchCommentAttachment::query();
            //             $attachmentQb->where('comment_id', $message['message_id']);
            //             $attachments = $attachmentQb->get();
            //             $messageDetails['attachments'] = $attachments;
            //         }
            //     } elseif ($message['message_type'] === 'text_type') {
            //         $qb = DispatchComment::query();
            //         $qb->where('id', $message['message_id']);
            //         $qb->with('user:id,user_id,user_name');
            //         $messageDetails = $qb->first();
                    
            //     } elseif ($message['message_type'] === 'ncc_type') {
            //         $qb = DispatchNcc::query();
            //         $qb->where('id', $message['message_id']);
            //         $messageDetails = $qb->first();                    
            //     }
                
            //     if ($messageDetails) {
            //         $message['details'] = $messageDetails;
            //         $messages[] = $message;
            //     }
            // }

            echo json_encode(['success' => true, 'messages' => $messages]);
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function addEmployeeTextNote()
    {
        header('Content-Type: application/json');
        $data = json_decode(file_get_contents('php://input'), true) ?? $_POST;

        try {

            $auth = EmployeeAuthMiddleware::authenticate();
            $jobId = $data['job_id'] ?? '';
            $jobComment = $data['job_comment'] ?? '';

            if (!$jobId || !$jobComment) {
                http_response_code(400);
                echo json_encode(['error' => 'Job ID and comment are required']);
                exit;
            }

            $comment = new DispatchComment();
            $comment->dispatch_id = $jobId;
            $comment->user_id = $auth['user_id'];
            $comment->dispatch_comment = $jobComment;
            $comment->shared_tech = 'Y';
            $comment->shared_frontdoor = 'N';
            $comment->save();

            $commentId = $comment->id;

            // Add entry to dispatch_messages table
            $message = new DispatchMessage();
            $message->dispatch_id = $jobId;
            
            // Check user type and set message_type accordingly
            if ($auth['user_type'] === 'tenant') {
                $message->message_type = 'admin_text_type';
            } else {
                $message->message_type = 'text_type';
            }
            
            $message->message_id = $commentId;
            $message->visible = 'Y';
            $message->save();

            $qb = DispatchComment::query();
            $qb->where('id', $commentId);
            $newComment = $qb->first();
            $newComment->message_id = $message->id;

            echo json_encode(['success' => true, 'comment' => $newComment]);

        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function updateEmployeeTextNote()
    {
        header('Content-Type: application/json');
        $data = json_decode(file_get_contents('php://input'), true) ?? $_POST;

        try {

            $auth = EmployeeAuthMiddleware::authenticate();
            $messageId = $data['message_id'] ?? '';
            $jobComment = $data['job_comment'] ?? '';

            if (!$messageId || !$jobComment) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Message ID and comment text are required']);
                exit;
            }

            $qb = DispatchMessage::query();
            $qb->where('id', $messageId);
            $message = $qb->first();

            if (!$message) {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Message not found']);
                exit;
            }

            if ($message->message_type != "text_type") {
                // Check if user is tenant, if yes continue, otherwise return error
                if ($auth['user_type'] !== 'tenant') {
                    http_response_code(403);
                    echo json_encode(['success' => false, 'message' => 'you don\'t have permissions.']);
                    exit;
                }                
            }

            $qb = DispatchComment::query();
            $qb->where('id', $message->message_id);
            $comment = $qb->first();

            if (!$comment) {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Text note not found or you do not have permission to edit it']);
                exit;
            }

            $comment->dispatch_comment = $jobComment;
            $comment->save();

            echo json_encode(['success' => true, 'comment' => $comment]);


        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }

    }

    public function deleteEmployeeTextNote()
    {
        header('Content-Type: application/json');
        $data = json_decode(file_get_contents('php://input'), true) ?? $_POST;

        try {

            $auth = EmployeeAuthMiddleware::authenticate();
            $messageId = $data['message_id'] ?? '';

            if (!$messageId) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Message ID is required']);
                exit;
            }

            $qb = DispatchMessage::query();
            $qb->where('id', $messageId);
            $message = $qb->first();

            if (!$message) {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Message not found']);
                exit;
            }

            if ($message->message_type != "text_type") {
                http_response_code(403);
                echo json_encode(['success' => false, 'message' => 'you don\'t have permissions.']);
                exit;
            }

            $qb = DispatchComment::query();
            $qb->where('id', $message->message_id);
            $comment = $qb->first();

            if (!$comment) {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Comment not found or you do not have permission to edit it']);
                exit;
            }

            if ($comment->user_id != $auth['user_id']) {
                http_response_code(403);
                echo json_encode(['success' => false, 'message' => 'you don\'t have permissions.']);
                exit;
            }
            $comment->delete();
            $message->delete();
            echo json_encode(['success' => true]);

        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }

    }
}
