import { request } from "umi";
import { RequestConfig } from "@umijs/max";
import { paramsSerializer } from "../../api";

const urlPrefix = "/api/tenant/invoice";

/**
 * Get DispatchInvoice list
 *
 * GET /api/tenant/invoice
 */
export async function getDispatchInvoiceListByPage(params: API.PageParams, sort?: any, filter?: any) {
    return request<API.ResultObject<API.PaginatedResult<API.DispatchInvoice>>>(`${urlPrefix}`, {
        method: "GET",
        params: {
            ...params,
            perPage: params.pageSize,
            page: params.current,
            sort,
            filter,
        },
        withToken: true,
        paramsSerializer,
    }).then((res) => ({
        data: res.data.rows,
        success: res.success,
        total: res.data.pagination?.total_records,
        pagination: res.data.pagination,
    }));
}

export async function getDispatchInvoice(id?: number, params?: API.DispatchInvoice & API.PageParams) {
    return getDispatchInvoiceListByPage({ id, ...params, current: 1, pageSize: 1 }).then((res) => {
        return res.data?.[0];
    });
}

/**
 * Get DispatchInvoice list
 *
 * GET api/tenant/invoice/getOnePdf
 */
export async function getDispatchInvoicePdf(id: number) {
    return request<API.ResultObject<API.Downloadable>>(`${urlPrefix}/getOnePdf`, {
        method: "GET",
        params: {
            id,
        },
        withToken: true,
        paramsSerializer,
    }).then((res) => res.data);
}

/**
 * Create an invoice.
 *
 *  POST /api/tenant/invoice/add */
export async function addDispatchInvoice(data?: API.DispatchInvoice | FormData, options?: { [key: string]: any }) {
    const config: RequestConfig = {
        method: "POST",
        data,
        ...(options || {}),
    };
    return request<API.ResultObject<API.DispatchInvoice>>(`${urlPrefix}/add`, config).then((res) => res.data);
}

/**
 * Create an invoice by Estimate ID.
 *
 *  POST /api/tenant/invoice/addByEstimate/{estimateId} */
export async function addDispatchInvoiceByEstimate(estimateId: number, params?: any, options?: { [key: string]: any }) {
    const config: RequestConfig = {
        method: "POST",
        data: params,
        ...(options || {}),
    };
    return request<API.ResultObject<API.DispatchInvoice>>(`${urlPrefix}/addByEstimate/${estimateId}`, config).then((res) => res.data);
}



/**
 * Update DispatchInvoice extension data.
 *
 *  PUT /api/tenant/invoice/{id}/update */
export async function updateDispatchInvoice(id?: number, data?: API.DispatchInvoice | FormData, options?: { [key: string]: any }) {
    const config: RequestConfig = {
        method: "PUT",
        data,
        ...(options || {}),
    };
    return request<API.ResultObject<API.DispatchInvoice>>(`${urlPrefix}/${id}/update`, config).then((res) => res.data);
}

/** delete DELETE /api/tenant/invoice/{id} */
export async function deleteDispatchInvoice(id?: string | number, options?: { [key: string]: any }) {
    return request<Record<string, any>>(`${urlPrefix}/${id}`, {
        method: "DELETE",
        ...(options || {}),
    });
}
