import { getDispatchInvoiceListByPage, getDispatchInvoicePdf } from "@/services/app/tenant/dispatch/dispatch-invoice";
import Util, { nf2, sn } from "@/util";
import { EditOutlined, FilePdfOutlined, PlusOutlined } from "@ant-design/icons";
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProForm,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProTable,
} from "@ant-design/pro-components";
import { Button, Card, message, Space, Tag, TagProps, Typography } from "antd";
import { useEffect, useRef, useState } from "react";
import CreateForm from "./components/CreateForm";
import { DEFAULT_PER_PAGE_PAGINATION, InvoiceStatus, InvoiceStatusKv } from "@/constants";
import UpdateForm from "./components/UpdateForm";

export const InvoiceStatusComp: React.FC<{ status?: InvoiceStatus | string }> = ({ status }) => {
  let color: TagProps["color"] = "default";
  switch (status) {
    case InvoiceStatus.CLOSED:
      color = "green-inverse";
      break;
    case InvoiceStatus.PAID:
      color = "success";
      break;
    case InvoiceStatus.SENT:
      color = "blue";
      break;
    case InvoiceStatus.PENDING:
      color = "orange";
      break;
    case InvoiceStatus.CANCELED:
      color = "geekblue-inverse";
      break;
    case InvoiceStatus.REFUNDED:
      color = "red";
      break;
  }

  return <Tag color={color as any}>{InvoiceStatusKv[status || "-"] ?? "-"}</Tag>;
};

type RecordType = API.DispatchInvoice;

const InvoiceListPage: React.FC = () => {
  const actionRef = useRef<ActionType>(null);
  const searchFormRef = useRef<ProFormInstance>(null);

  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const [currentRow, setCurrentRow] = useState<RecordType>();
  const [selectedRowsState, setSelectedRows] = useState<RecordType[]>([]);

  const columns: ProColumns<RecordType>[] = [
    {
      title: "Status",
      dataIndex: "status",
      sorter: true,
      width: 100,
      align: "center",
      render(dom, entity, index, action, schema) {
        return <InvoiceStatusComp status={entity.status as InvoiceStatus} />;
      },
    },
    {
      title: "Invoice No",
      dataIndex: "invoice_no",
      sorter: true,
      width: 100,
      copyable: true,
      defaultSortOrder: "descend",
    },
    {
      title: "File",
      dataIndex: ["file", "url"],
      ellipsis: true,
      width: 40,
      render(dom, entity, index, action, schema) {
        return entity.file ? (
          <Typography.Link href={`${API_URL}/${entity.file?.url}`} target="_blank">
            <FilePdfOutlined />
          </Typography.Link>
        ) : null;
      },
    },
    {
      title: "Job ID",
      dataIndex: "dispatch_id",
      sorter: true,
      width: 80,
      align: "center",
    },
    {
      title: "External ID",
      dataIndex: ["dispatch", "external_id"],
      sorter: true,
      copyable: true,
      width: 110,
      align: "center",
    },
    {
      title: "Customer",
      dataIndex: "customer_name",
      ellipsis: true,
      width: 150,
    },
    {
      title: "Issue Date",
      dataIndex: "issue_date",
      sorter: true,
      valueType: "date",
      width: 90,
      render(dom, entity, index, action, schema) {
        return Util.dtToMDY(entity.issue_date);
      },
    },
    {
      title: "Service Date",
      dataIndex: "service_date",
      sorter: true,
      valueType: "date",
      width: 90,
      render(dom, entity, index, action, schema) {
        return Util.dtToMDY(entity.service_date);
      },
    },

    {
      title: "Sub Total",
      dataIndex: "subtotal",
      width: 100,
      valueType: "money",
      align: "right",
      className: "bl-2 b-gray",
      render: (_, record) => `$${nf2(record.subtotal || 0)}`,
    },
    {
      title: "Tax %",
      dataIndex: "tax_percent",
      width: 65,
      valueType: "money",
      align: "right",
      render: (_, record) => (sn(record.tax_percent) > 0 ? `${nf2(record.tax_percent || 0)}%` : null),
    },
    {
      title: "Tax",
      dataIndex: "tax_amount",
      width: 90,
      valueType: "money",
      align: "right",
      render: (_, record) => (sn(record.tax_amount) > 0 ? `$${nf2(record.tax_amount || 0)}` : null),
    },
    {
      title: "Grand Total",
      dataIndex: "grand_total",
      width: 100,
      valueType: "money",
      align: "right",
      className: "bl-2 b-gray",
      render: (_, record) => `$${nf2(record.grand_total || 0)}`,
    },
    {
      title: "Payment Terms",
      dataIndex: "payment_terms",
      className: "bl-2 b-gray",
      ellipsis: true,
      width: 120,
    },
    {
      title: "Billing address",
      dataIndex: "billing_full_address",
      ellipsis: true,
      width: 320,
    },
    {
      title: "Created On",
      sorter: true,
      dataIndex: "created_at",
      valueType: "dateTime",
      width: 130,
      render(__, record) {
        return Util.dtToMDYHHMM(record.created_at);
      },
    },
    {
      title: "Option",
      dataIndex: "option",
      valueType: "option",
      width: 100,
      render: (_, record) => [
        <Button
          key="edit"
          size="small"
          icon={<EditOutlined />}
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        />,
        <Button
          key="pdf"
          size="small"
          title="Generate PDF"
          type={record.file ? "primary" : "default"}
          icon={<FilePdfOutlined />}
          onClick={() => {
            const hide = message.loading("Downloading PDF...", 0);
            getDispatchInvoicePdf(sn(record.id))
              .then((res) => {
                window.open(`${API_URL}/${res.url}`, "_blank");
                message.success("Downloaded PDF successfully!");
                actionRef.current?.reload();
              })
              .catch(Util.error)
              .finally(hide);
          }}
        />,
      ],
    },
  ];

  useEffect(() => {}, []);

  return (
    <PageContainer>
      <Card size="small" style={{ marginBottom: 16 }}>
        <ProForm
          formRef={searchFormRef}
          layout="inline"
          isKeyPressSubmit
          submitter={{
            resetButtonProps: { style: { marginLeft: "auto" } },
            searchConfig: { submitText: "Search" },
            submitButtonProps: { htmlType: "submit" },
            onReset(value) {
              searchFormRef.current?.resetFields();
              actionRef.current?.reload();
            },
            onSubmit(value) {
              actionRef.current?.reload();
            },
            render(props, dom) {
              return (
                <Space style={{ marginLeft: "auto" }} size={8}>
                  {dom}
                </Space>
              );
            },
          }}
        >
          <ProFormText name="invoice_no" label="Invoice No#" width={140} />
          <ProFormDigit name="dispatch_id" label="Job ID#" width="xs" />
          <ProFormDigit name="dispatch_external_id" label="External ID#" width={120} />
          <ProFormSelect
            name="status"
            label="Status"
            colProps={{ flex: "100px" }}
            placeholder={"All"}
            formItemProps={{ style: { marginBottom: 0 } }}
            valueEnum={InvoiceStatusKv}
            fieldProps={{
              popupMatchSelectWidth: false,
              onChange(value, option) {
                actionRef.current?.reload();
              },
            }}
          />
          <ProFormText name="ft_keyWords" placeholder="Search Customer / Address" />
        </ProForm>
      </Card>

      <ProTable<RecordType, API.PageParams>
        headerTitle={"Invoices list"}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        size="small"
        search={false}
        scroll={{ x: 1000 }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleCreateModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        pagination={{ defaultPageSize: DEFAULT_PER_PAGE_PAGINATION }}
        request={(params, sort, filter) => {
          // const sfValues = Util.getSfValues("invoice_list", {});
          const sfValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues("invoice_list", sfValues);

          const newParams = {
            ...sfValues,
            ...params,
            with: "items,logs,estimate,file",
          };
          return getDispatchInvoiceListByPage(newParams, sort, filter);
        }}
        onRequestError={Util.error}
        columns={columns}
        /* rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }} */
        rowSelection={false}
        tableAlertRender={false}
        columnEmptyText=""
        locale={{ emptyText: <></> }}
      />

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleCreateModalVisible}
        onSubmit={(values) => {
          handleCreateModalVisible(false);
          actionRef.current?.reload();
          return Promise.resolve(true);
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow}
        onSubmit={(values) => {
          handleUpdateModalVisible(false);
          actionRef.current?.reload();
          return Promise.resolve(true);
        }}
      />

      {/* {selectedRowsState?.length > 0 && (
        <FooterToolbar extra={<SFooterToolbarExtra title={"invoice"} selectedRowsState={selectedRowsState} actionRef={actionRef} />}>
          <BatchDeleteAction
            title="invoice"
            onConfirm={async () => {
              await deleteDispatchInvoice(selectedRowsState.map((row) => row.id)?.join(","));
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          />
        </FooterToolbar>
      )} */}
    </PageContainer>
  );
};

export default InvoiceListPage;


