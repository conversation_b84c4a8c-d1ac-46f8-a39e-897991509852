import BatchDeleteAction from "@/components/Table/BatchDeleteAction";
import SFooterToolbarExtra from "@/components/Table/SFooterToolbarExtra";
import { getDispatchInvoiceListByPage } from "@/services/app/tenant/dispatch/dispatch-invoice";
import Util from "@/util";
import { PlusOutlined } from "@ant-design/icons";
import {
  ActionType,
  FooterToolbar,
  PageContainer,
  ProColumns,
  ProForm,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProTable,
} from "@ant-design/pro-components";
import { Button, Card, Space } from "antd";
import { useEffect, useRef, useState } from "react";
import CreateForm from "./components/CreateForm";

type RecordType = API.DispatchInvoice;

const InvoiceListPage: React.FC = () => {
  const actionRef = useRef<ActionType>(null);
  const searchFormRef = useRef<ProFormInstance>(null);

  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const [currentRow, setCurrentRow] = useState<RecordType>();
  const [selectedRowsState, setSelectedRows] = useState<RecordType[]>([]);

  const columns: ProColumns<RecordType>[] = [
    {
      title: "Name",
      dataIndex: "name",
      sorter: true,
      filtered: true,
    },
    {
      title: "Initials",
      dataIndex: "initials",
      sorter: true,
      filtered: true,
    },
    {
      title: "Email",
      dataIndex: "email",
      sorter: true,
      hideInForm: true,
      renderText: (val: string) => `${val}`,
    },
    {
      title: "Status",
      dataIndex: "status",
      hideInForm: false,
      sorter: true,
      filters: true,
      valueEnum: {
        "0": {
          text: "Disabled",
        },
        "1": {
          text: "Enabled",
        },
      },
      // render: (_, record) => (record ? UserStatus(record.status as ConstVar.UserStatus) : ''),
    },
    {
      title: "Created On",
      sorter: true,
      dataIndex: "created_on",
      valueType: "dateTime",
      search: false,
      width: 130,
      render(__, record) {
        return Util.dtToMDYHHMM(record.created_at);
      },
    },
    {
      title: "Option",
      dataIndex: "option",
      valueType: "option",
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  useEffect(() => {}, []);

  return (
    <PageContainer>
      <Card size="small" style={{ marginBottom: 16 }}>
        <ProForm
          formRef={searchFormRef}
          layout="inline"
          submitter={{
            resetButtonProps: { style: { marginLeft: "auto" } },
            searchConfig: { submitText: "Search" },
            onReset(value) {
              actionRef.current?.reload();
            },
            onSubmit(value) {
              actionRef.current?.reload();
            },
            render(props, dom) {
              return (
                <Space style={{ marginLeft: "auto" }} size={8}>
                  {dom}
                </Space>
              );
            },
          }}
        >
          <ProFormDigit name="id" label="Invoice No#" />
          <ProFormDigit name="dispatch_id" label="Job ID#" />
          <ProFormSelect
            name="status"
            label="Status"
            colProps={{ flex: "100px" }}
            placeholder={"All"}
            formItemProps={{ style: { marginBottom: 4 } }}
            fieldProps={{
              onChange(value, option) {
                actionRef.current?.reload();
              },
            }}
          />
          <ProFormText name="customer_search" label="Customer Search" />
        </ProForm>
      </Card>

      <ProTable<RecordType, API.PageParams>
        headerTitle={"Invoices list"}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        size="small"
        search={false}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleCreateModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        request={getDispatchInvoiceListByPage}
        onRequestError={Util.error}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columnEmptyText=""
      />

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleCreateModalVisible}
        onSubmit={(values) => {
          return Promise.resolve(true);
        }}
      />

      {selectedRowsState?.length > 0 && (
        <FooterToolbar extra={<SFooterToolbarExtra title={"account"} selectedRowsState={selectedRowsState} actionRef={actionRef} />}>
          <BatchDeleteAction
            title="account"
            onConfirm={async () => {
              //   await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          />
        </FooterToolbar>
      )}
    </PageContainer>
  );
};

export default InvoiceListPage;
