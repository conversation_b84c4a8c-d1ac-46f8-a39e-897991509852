<?php

namespace Butler\Models\Invoice;

use Butler\Models\BaseModel;
use Butler\Models\Dispatch\DispatchInvoice;
use Butler\Models\Tenant;

/**
 * @property integer $id
 * @property integer $tenant_id
 * @property integer $invoice_id
 * @property string $document_id
 * @property string $signer_email
 * @property string $signer_name
 * @property string $status
 * @property string $invite_id
 * @property string $signed_at
 * @property string $signed_document_path
 * @property string $error_message
 * @property string $created_at
 * @property string $updated_at
 *
 * @property DispatchInvoice $invoice
 * @property Tenant $tenant
 */
class InvoiceSignNowSession extends BaseModel
{
    protected $table = 'invoice_signnow_sessions';

    protected $fillable = [
        'tenant_id', 'invoice_id', 'document_id', 'signer_email', 'signer_name',
        'status', 'invite_id', 'signed_at', 'signed_document_path', 'error_message'
    ];

    public function invoice()
    {
        return $this->belongsTo(DispatchInvoice::class, 'invoice_id');
    }

    public function tenant()
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }
}