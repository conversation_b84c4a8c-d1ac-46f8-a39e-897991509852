<?php

namespace Butler\Controllers\tenants\Invoice;

use Butler\Controllers\api\ApiBaseController;
use Butler\Models\Dispatch\DispatchInvoice;
use Butler\Models\Invoice\InvoiceSignNowSession;
use Butler\Services\SignNow\SignNowService;

class InvoiceSignNowApiController extends ApiBaseController
{
    public function startSignNow()
    {
        try {
            $invoiceId = $this->data['invoice_id'] ?? null;

            if (!$invoiceId) {
                $this->jsonWithError(null, 400, 'Invoice ID is required');
            }

            /** @var DispatchInvoice $invoice */
            $invoice = DispatchInvoice::with(['file', 'dispatch.customer'])->find($invoiceId);

            if (!$invoice) {
                $this->jsonWithError(null, 404, 'Invoice not found');
            }

            if (!$invoice->file) {
                $this->jsonWithError(null, 400, 'Invoice PDF not found. Please generate PDF first.');
            }

            $customer = $invoice->dispatch->customer;
            $pdfPath = UPLOAD_PATH . '/' . $invoice->file->path;
            $documentName = "Invoice #{$invoice->invoice_no}";

            $signNowService = new SignNowService();

            // Upload document
            $documentId = $signNowService->uploadDocument($pdfPath, $documentName);

            if (!$documentId) {
                $this->jsonWithError(null, 500, 'Failed to upload document to SignNow');
            }

            $signerEmail = ($_ENV['ENVIRONMENT'] === 'dev') ? '<EMAIL>' : $customer->email;

            // Create SignNow session record immediately after upload
            $signNowSession = $signNowService->createSignNowSession(
                $invoice->id,
                $documentId,
                $signerEmail,
                $customer->name,
                $this->tenant_id
            );

            try {
                // Create signing invite
                $invite = $signNowService->createSigningInvite(
                    $documentId,
                    $signerEmail,
                    $customer->name,
                    $documentName
                );

                // Update session with invite details
                $signNowService->updateSignNowSession($signNowSession->id, [
                    'status' => 'PENDING',
                    'invite_id' => $invite['id'] ?? null
                ]);

            } catch (\Exception $inviteException) {
                // If invite fails, update session status but don't fail completely
                $signNowService->updateSignNowSession($signNowSession->id, [
                    'status' => 'INVITE_FAILED',
                    'error_message' => $inviteException->getMessage()
                ]);

                throw $inviteException; // Re-throw to handle in outer catch
            }

            // Refresh session to get latest data
            $signNowSession->refresh();

            $this->jsonWithSuccess([
                'document_id' => $documentId,
                'invite_id' => $signNowSession->invite_id,
                'session_id' => $signNowSession->id,
                'status' => $signNowSession->status
            ]);

        } catch (\Exception $exception) {
            $this->jsonWithError($exception);
        }
    }

    public function getSignNowStatus()
    {
        try {
            $sessionId = $this->data['session_id'] ?? null;

            if (!$sessionId) {
                $this->jsonWithError(null, 400, 'Session ID is required');
            }

            $session = InvoiceSignNowSession::find($sessionId);

            if (!$session) {
                $this->jsonWithError(null, 404, 'SignNow session not found');
            }

            // Get document status from SignNow
            $signNowService = new SignNowService();
            $documentStatus = $signNowService->getDocumentStatus($session->document_id);

            // Update session status based on document status
            $newStatus = $this->mapSignNowStatus($documentStatus);
            if ($newStatus !== $session->status) {
                $session->status = $newStatus;
                if ($newStatus === 'SIGNED') {
                    $session->signed_at = now();
                }
                $session->save();
            }

            $this->jsonWithSuccess($session);

        } catch (\Exception $exception) {
            $this->jsonWithError($exception);
        }
    }

    private function mapSignNowStatus($documentStatus)
    {
        // Map SignNow document status to our internal status
        $signNowStatus = $documentStatus['status'] ?? 'pending';
        
        switch (strtolower($signNowStatus)) {
            case 'completed':
                return 'SIGNED';
            case 'cancelled':
                return 'CANCELLED';
            case 'expired':
                return 'EXPIRED';
            default:
                return 'PENDING';
        }
    }
}