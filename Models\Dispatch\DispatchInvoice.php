<?php

namespace Butler\Models\Dispatch;

use Butler\Models\BaseModel;
use Butler\Models\Customer\Customer;
use Butler\Models\File;
use Butler\Models\Tenant;

/**
 * @property integer $id
 * @property integer $dispatch_id
 * @property integer $estimate_id
 * @property integer $customer_id
 * @property integer $tenant_id
 * @property integer $file_id
 * @property string $invoice_no
 * @property string $status
 * @property string $issue_date
 * @property string $service_date
 * @property float $grand_total
 * @property float $subtotal
 * @property float $tax_percent
 * @property float $tax_amount
 * @property float $discount_amount
 * @property integer $total_item_count
 * @property string $notes
 * @property string $payment_terms
 * @property string $valid_until
 * @property string $billing_company
 * @property string $billing_name
 * @property string $billing_email
 * @property string $billing_phone
 * @property string $billing_city
 * @property string $billing_state
 * @property string $billing_street_direction
 * @property string $billing_street_name
 * @property string $billing_street_number
 * @property string $billing_unit_number
 * @property string $billing_unit_type
 * @property string $billing_zip
 * @property string $billing_zip_four
 * @property string $customer_company
 * @property string $customer_name
 * @property string $customer_email
 * @property string $customer_phone
 * @property string $customer_city
 * @property string $customer_state
 * @property string $customer_street_direction
 * @property string $customer_street_name
 * @property string $customer_street_number
 * @property string $customer_unit_number
 * @property string $customer_unit_type
 * @property string $customer_zip
 * @property string $customer_zip_four
 * @property integer $created_by
 * @property string $created_at
 * @property integer $updated_by
 * @property string $updated_at
 *
 * @property Tenant $tenant
 * @property File $file
 * @property Customer $customer
 * @property DispatchEstimate estimate
 * @property Dispatch $dispatch
 * @property DispatchInvoiceItem[] $items
 * @property DispatchInvoiceLog[] $logs
 * @property DispatchInvoicePayment[] $payments
 */
class DispatchInvoice extends BaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'dispatch_invoice';

    /**
     * @var array
     */
    protected $fillable = ['dispatch_id', 'estimate_id', 'tenant_id', 'customer_id', 'invoice_no', 'file_id', 'status', 'issue_date', 'service_date', 'grand_total', 'subtotal', 'tax_percent', 'tax_amount', 'discount_amount', 'total_item_count', 'notes', 'payment_terms', 'valid_until', 'billing_company', 'billing_name', 'billing_email', 'billing_phone', 'billing_city', 'billing_state', 'billing_street_direction', 'billing_street_name', 'billing_street_number', 'billing_unit_number', 'billing_unit_type', 'billing_zip', 'billing_zip_four', 'customer_company', 'customer_name', 'customer_email', 'customer_phone', 'customer_city', 'customer_state', 'customer_street_direction', 'customer_street_name', 'customer_street_number', 'customer_unit_number', 'customer_unit_type', 'customer_zip', 'customer_zip_four', 'created_by', 'created_at', 'updated_by', 'updated_at'];

    public $timestamps = true;

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function file()
    {
        return $this->belongsTo('Butler\Models\File');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer()
    {
        return $this->belongsTo('Butler\Models\Customer\Customer');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function estimate()
    {
        return $this->belongsTo('Butler\Models\Dispatch\DispatchEstimate', 'estimate_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function dispatch()
    {
        return $this->belongsTo('Butler\Models\Dispatch\Dispatch');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function items()
    {
        return $this->hasMany('Butler\Models\Dispatch\DispatchInvoiceItem');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function logs()
    {
        return $this->hasMany('Butler\Models\Dispatch\DispatchInvoiceLog');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function payments()
    {
        return $this->hasMany('Butler\Models\Dispatch\DispatchInvoicePayment');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function tenant()
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }

    /**
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param $tenant_id
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeTenantOnly(\Illuminate\Database\Eloquent\Builder $query, $tenant_id)
    {
        $query->where('tenant_id', $tenant_id);
    }
}
