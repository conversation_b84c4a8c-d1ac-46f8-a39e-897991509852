<?php

/** @var \Butler\Models\Dispatch\DispatchInvoice $invoice */
/** @var \Butler\Models\Tenant\TenantAddress $tenantAddress */

?>
<!-- Header Section with Company Logo/Name and Invoice Details Box -->
<div class="header-section">
    <table style="width: 100%; margin-bottom: 30px;">
        <tr>
            <td style="vertical-align: top;width: 60%">
                <img src="<?php echo BASE_URL; ?>assets/img/original-fast-response-logo-1-768x424.png"
                    alt="Fast Response Heating & Cooling"
                    style="max-width: 250px; height: auto; margin-bottom: 10px;" />
                <p class="company-subtitle" style="font-size: 17px; color: #333; font-weight: bold;">
                    Fast Response Heating & Cooling
                </p>
            </td>
            <td style="vertical-align: top; width: 40%; text-align: right">
                <!-- Invoice Details Box -->
                <table class="invoice-detail-box">
                    <tr>
                        <td class="label">INVOICE</td>
                        <td class="value">#<?php echo $invoice->invoice_no ?? '' ?></td>
                    </tr>
                    <tr>
                        <td class="label">SERVICE DATE</td>
                        <td class="value">
                            <?php echo $invoice->service_date ? date('M d, Y', strtotime($invoice->service_date)) : '' ?>
                        </td>
                    </tr>
                    <tr>
                        <td class="label" style="padding-bottom: 8px;">DUE</td>
                        <td class="value" style="padding-bottom: 8px;">
                            <?php echo $invoice->payment_terms ?? 'Upon receipt' ?></td>
                    </tr>
                    <tr class="amount-due-row">
                        <td class="label">AMOUNT DUE</td>
                        <td class="value" style="color: #276BAA;font-size: 16px; font-weight: bold;">
                            $<?php echo number_format($invoice->grand_total ?? 0, 2) ?>
                        </td>
                    </tr>
                </table>

            </td>
        </tr>
    </table>
</div>

<!-- Customer and Contact Information Section -->
<div class="info-section">
    <table class="invoice-info">
        <tr>
            <td style="width: 60%;vertical-align: top;">
                <table class="billing-info" style="width: 100%">
                    <tbody>
                        <tr>
                            <td><span style="font-weight: bold;"><?php echo $invoice->customer_name ?? '' ?></span></td>
                        </tr>
                        <tr>
                            <td>
                                <span style="">
                                    <?php echo $invoice->billing_street_number ?? '' ?>
                                    <?php echo $invoice->billing_street_name ?? '' ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <span style="">
                                    <?php echo $invoice->billing_city ?? '' ?>,
                                    <?php echo $invoice->billing_state ?? '' ?>
                                    <?php echo $invoice->billing_zip ?? '' ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding-top: 40px; display: flex; align-items: center;">
                                <span class="phone-icon" style="color: #666; margin-right: 8px; display: inline-flex;">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M3 5a2 2 0 0 1 2-2h3.28a1 1 0 0 1 .948.684l1.498 4.493a1 1 0 0 1-.502 1.21l-2.257 1.13a11.042 11.042 0 0 0 5.516 5.516l1.13-2.257a1 1 0 0 1 1.21-.502l4.493 1.498a1 1 0 0 1 .684.949V19a2 2 0 0 1-2 2h-1C9.716 21 3 14.284 3 6V5z"
                                            fill="#666" />
                                    </svg>
                                </span>
                                <span>
                                    <?php echo $invoice->customer_phone ?? '' ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td style="display: flex; align-items: center;">
                                <span class="email-icon" style="color: #666; margin-right: 8px; display: inline-flex;">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                                            stroke="#666" stroke-width="2" fill="none" />
                                        <polyline points="22,6 12,13 2,6" stroke="#666" stroke-width="2" fill="none" />
                                    </svg>
                                </span>
                                <span>
                                    <?php echo $invoice->customer_email ?? '' ?>
                                </span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
            <td style="width: 40%;vertical-align: top;text-align: right">
                <table class="billing-info" style="width: 100%;">
                    <tbody>
                        <tr>
                            <td><span style="font-weight: bold; border-bottom: #B5B5B5;padding-bottom: 3px">CONTACT
                                    US</span></td>
                        </tr>
                        <tr>
                            <td style="padding-top: 3px">
                                <span style="">
                                    <?php echo $tenantAddress->street_number ?? '' ?>
                                    <?php echo $tenantAddress->street_name ?? '' ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <span style="">
                                    <?php echo $tenantAddress->city ?? '' ?>, <?php echo $tenantAddress->state ?? '' ?>
                                    <?php echo $tenantAddress->zip ?? '' ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding-top: 40px; display: flex; align-items: center;">
                                <span class="phone-icon" style="color: #666; margin-right: 8px; display: inline-flex;">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M3 5a2 2 0 0 1 2-2h3.28a1 1 0 0 1 .948.684l1.498 4.493a1 1 0 0 1-.502 1.21l-2.257 1.13a11.042 11.042 0 0 0 5.516 5.516l1.13-2.257a1 1 0 0 1 1.21-.502l4.493 1.498a1 1 0 0 1 .684.949V19a2 2 0 0 1-2 2h-1C9.716 21 3 14.284 3 6V5z"
                                            fill="#666" />
                                    </svg>
                                </span>
                                <span>
                                    <?php echo $tenantAddress->phone ?? '' ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td style="display: flex; align-items: center;">
                                <span class="email-icon" style="color: #666; margin-right: 8px; display: inline-flex;">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                                            stroke="#666" stroke-width="2" fill="none" />
                                        <polyline points="22,6 12,13 2,6" stroke="#666" stroke-width="2" fill="none" />
                                    </svg>
                                </span>
                                <span>
                                    <?php echo $tenantAddress->email ?? '' ?>
                                </span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    </table>
</div>

<div class="line-items">
    <table>
        <thead>
            <tr>
                <th width="50%">Description</th>
                <th width="15%" style="text-align: center;">Qty</th>
                <th width="15%" style="text-align: right;">Price</th>
                <th width="20%" style="text-align: right;">Taxable</th>
                <th width="20%" style="text-align: right;">Amount</th>
            </tr>
        </thead>
        <tbody>
            <?php
            foreach ($invoice->items as $item) {
                ?>
            <tr>
                <td style="text-align: left;"><?php echo $item->name ?></td>
                <td style="text-align: center;"><?php echo number_format($item->qty) ?></td>
                <td style="text-align: right;">$<?php echo number_format($item->price, 2) ?></td>
                <td style="text-align: right;">
                    <?php echo $item->tax_percent > 0 ? number_format($item->tax_percent, 2) . '%' : '' ?></td>
                <td style="text-align: right;">$<?php echo number_format($item->price_total) ?></td>
            </tr>
            <?php
            }
            ?>
        </tbody>
    </table>
</div>

<div class="totals">
    <table>
        <tr>
            <td>Subtotal:</td>
            <td style="text-align: right;">$<?php echo number_format($invoice->subtotal, 2) ?></td>
        </tr>
        <?php
            if (floatval($invoice->tax_amount) > 0) {
                ?>
        <tr>
            <td>Tax:</td>
            <td style="text-align: right;">$<?php echo number_format($invoice->tax_amount, 2) ?></td>
        </tr>
        <?php } ?>
        <?php
            if (floatval($invoice->discount_amount) > 0) {
                ?>
        <tr>
            <td>Discount:</td>
            <td style="text-align: right;">$<?php echo number_format($invoice->discount_amount, 2) ?></td>
        </tr>
        <?php } ?>
        <tr class="total-row">
            <td>Total:</td>
            <td style="text-align: right;">$<?php echo number_format($invoice->grand_total, 2) ?></td>
        </tr>
    </table>
</div>
<?php
$html = '';
if (!empty($invoice->notes)) {
    $html .= '
<div class="notes">
    <span class="label">Notes:</span><br>
    ' . nl2br(htmlspecialchars($invoice->notes)) . '
</div>';
}

$html .= '
<div style="margin-top: 40px; text-align: center; color: #666; font-size: 9px;">
    Thank you for your business!
</div>';

echo $html;