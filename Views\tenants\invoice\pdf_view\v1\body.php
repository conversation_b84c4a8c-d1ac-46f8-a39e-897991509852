<?php

/** @var \Butler\Models\Dispatch\DispatchInvoice $invoice */

?>
<!-- Header Section with Company Logo/Name and Invoice Details Box -->
<div class="header-section">
    <table style="width: 100%; margin-bottom: 30px;">
        <tr>
            <td width="60%" style="vertical-align: top;">
                <!-- Company Logo/Name Section -->
                <div class="company-logo">
                    <div class="company-name"
                        style="font-size: 24px; font-weight: bold; color: #2c5aa0; margin-bottom: 5px;">
                        Fast Response
                    </div>
                    <div class="company-tagline"
                        style="font-size: 18px; font-style: italic; color: #333; margin-bottom: 5px;">
                        Heating & Cooling
                    </div>
                    <div class="company-subtitle" style="font-size: 14px; color: #666;">
                        Fast Response Heating & Cooling
                    </div>
                </div>
            </td>
            <td width="40%" style="vertical-align: top; text-align: right;">
                <!-- Invoice Details Box -->
                <div class="invoice-box" style="border: 2px solid #ccc; padding: 15px; background-color: #f9f9f9;">
                    <table style="width: 100%;">
                        <tr>
                            <td style="font-weight: bold; color: #666; font-size: 12px;">INVOICE</td>
                            <td style="text-align: right; font-weight: bold; font-size: 12px;">
                                #<?php echo $invoice->invoice_no ?? '8' ?></td>
                        </tr>
                        <tr>
                            <td style="color: #666; font-size: 11px; padding-top: 5px;">SERVICE DATE</td>
                            <td style="text-align: right; font-size: 11px; padding-top: 5px;">
                                <?php echo $invoice->service_date ? date('M d, Y', strtotime($invoice->service_date)) : 'Mar 08, 2024' ?>
                            </td>
                        </tr>
                        <tr>
                            <td style="color: #666; font-size: 11px;">DUE</td>
                            <td style="text-align: right; font-size: 11px;">
                                <?php echo $invoice->payment_terms ?? 'Upon receipt' ?></td>
                        </tr>
                        <tr>
                            <td colspan="2" style="padding-top: 10px; border-top: 1px solid #ddd;">
                                <div style="color: #666; font-size: 11px;">AMOUNT DUE</div>
                                <div style="font-size: 18px; font-weight: bold; color: #2c5aa0; text-align: right;">
                                    $<?php echo number_format($invoice->grand_total ?? 1840.00, 2) ?>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
            </td>
        </tr>
    </table>
</div>

<!-- Customer and Contact Information Section -->
<div class="info-section">
    <table style="width: 100%; margin-bottom: 30px;">
        <tr>
            <td width="50%" style="vertical-align: top;">
                <!-- Billing Information -->
                <div class="billing-info">
                    <div style="font-weight: bold; margin-bottom: 10px;">
                        <?php echo $invoice->customer_name ?? 'Demo Customer' ?></div>
                    <div style="margin-bottom: 5px;"><?php echo $invoice->billing_street_number ?? '6870' ?>
                        <?php echo $invoice->billing_street_name ?? 'Lake Devonwood Dr' ?></div>
                    <div style="margin-bottom: 15px;"><?php echo $invoice->billing_city ?? 'Fort Myers' ?>,
                        <?php echo $invoice->billing_state ?? 'FL' ?> <?php echo $invoice->billing_zip ?? '33908' ?>
                    </div>

                    <div style="margin-bottom: 5px;">
                        <span style="color: #666;">📞</span> <?php echo $invoice->customer_phone ?? '(*************' ?>
                    </div>
                    <div>
                        <span style="color: #666;">✉</span>
                        <?php echo $invoice->customer_email ?? '<EMAIL>' ?>
                    </div>
                </div>
            </td>
            <td width="50%" style="vertical-align: top;">
                <!-- Contact Information -->
                <div class="contact-info">
                    <div style="font-weight: bold; color: #666; font-size: 12px; margin-bottom: 10px;">CONTACT US</div>
                    <div style="margin-bottom: 5px;">3653 Big Run South Rd</div>
                    <div style="margin-bottom: 15px;">Grove City, OH 43123</div>

                    <div style="margin-bottom: 5px;">
                        <span style="color: #666;">📞</span> (*************
                    </div>
                    <div>
                        <span style="color: #666;">✉</span> <EMAIL>
                    </div>
                </div>
            </td>
        </tr>
    </table>
</div>

<div class="line-items">
    <table>
        <thead>
            <tr>
                <th width="50%">Description</th>
                <th width="15%" style="text-align: center;">Qty</th>
                <th width="15%" style="text-align: right;">Price</th>
                <th width="20%" style="text-align: right;">Taxable</th>
                <th width="20%" style="text-align: right;">Amount</th>
            </tr>
        </thead>
        <tbody>
            <?php
            foreach ($invoice->items as $item) {
                ?>
            <tr>
                <td style="text-align: left;"><?php echo $item->name ?></td>
                <td style="text-align: center;"><?php echo number_format($item->qty) ?></td>
                <td style="text-align: right;">$<?php echo number_format($item->price, 2) ?></td>
                <td style="text-align: right;">
                    <?php echo $item->tax_percent > 0 ? number_format($item->tax_percent, 2) . '%' : '' ?></td>
                <td style="text-align: right;">$<?php echo number_format($item->price_total) ?></td>
            </tr>
            <?php
            }
            ?>
        </tbody>
    </table>
</div>

<div class="totals">
    <table>
        <tr>
            <td>Subtotal:</td>
            <td style="text-align: right;">$<?php echo number_format($invoice->subtotal, 2) ?></td>
        </tr>
        <?php
            if (floatval($invoice->tax_amount) > 0) {
                ?>
        <tr>
            <td>Tax:</td>
            <td style="text-align: right;">$<?php echo number_format($invoice->tax_amount, 2) ?></td>
        </tr>
        <?php } ?>
        <?php
            if (floatval($invoice->discount_amount) > 0) {
                ?>
        <tr>
            <td>Discount:</td>
            <td style="text-align: right;">$<?php echo number_format($invoice->discount_amount, 2) ?></td>
        </tr>
        <?php } ?>
        <tr class="total-row">
            <td>Total:</td>
            <td style="text-align: right;">$<?php echo number_format($invoice->grand_total, 2) ?></td>
        </tr>
    </table>
</div>
<?php
$html = '';
if (!empty($invoice->notes)) {
    $html .= '
<div class="notes">
    <span class="label">Notes:</span><br>
    ' . nl2br(htmlspecialchars($invoice->notes)) . '
</div>';
}

$html .= '
<div style="margin-top: 40px; text-align: center; color: #666; font-size: 9px;">
    Thank you for your business!
</div>';

echo $html;