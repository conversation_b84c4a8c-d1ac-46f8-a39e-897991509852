/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/jsx-props-no-spreading */
import React from 'react';
import ReactDOM from "react-dom";
import { Droppable, Draggable, DroppableProvided, DroppableStateSnapshot, DraggableProvided, DraggableStateSnapshot } from 'react-beautiful-dnd';
import { Card, Space } from 'antd';
import DispatchItem from './item';
import { grid } from './constants';
import Title from './title';
import reorder, { groupEmployeeDispatchByDate, groupDispatchByZip, groupSimpleDispatchByZip } from "../../reorder";
import { string } from 'prop-types';
import dayjs from 'dayjs';

interface InnerDispatchListProps {
  dispatches: API.Dispatch[];
  listType?: string;
  listId?: string;
}

interface InnerListProps {
  dispatches: API.Dispatch[];
  title?: string;
  dropProvided: DroppableProvided;
  listType?: string;
  listId?: string;
}

interface DispatchListProps {
  ignoreContainerClipping?: boolean;
  internalScroll?: boolean;
  scrollContainerStyle?: React.CSSProperties;
  isDropDisabled?: boolean;
  isCombineEnabled?: boolean;
  listId?: string;
  listType?: string;
  style?: React.CSSProperties;
  dispatches: API.Dispatch[];
  title?: string;
  useClone?: boolean;
}

export const getBackgroundColor = (
  isDraggingOver: boolean, 
  isDraggingFrom: boolean
): string => {
  if (isDraggingOver) {
    return '#FFEBE6';
  }
  if (isDraggingFrom) {
    return '#E6FCFF';
  }
  return '#EBECF0';
};

const scrollContainerHeight = 600;

const InnerDispatchList = React.memo(function InnerDispatchList(props: InnerDispatchListProps) {  
  const { dispatches, listType, listId } = props;

  if (listType == "Zone") {
    const dispatchMap = groupSimpleDispatchByZip(dispatches);
    const zones = Object.keys(dispatchMap['zone_dispatch_map']);
    return zones.map((zone, index) => (
      <Droppable
        droppableId={`zone_${zone}_${index}`}
        type={"Dispatch"}      
        direction="vertical"
        key={`office_dispatch_zone_${zone}`}
        ignoreContainerClipping={true}
        isDropDisabled={false}
        isCombineEnabled={false}
        renderClone={undefined}
      >
        {(dropProvided: DroppableProvided, dropSnapshot: DroppableStateSnapshot) => (
          <div
            style={{
              backgroundColor: getBackgroundColor(
                dropSnapshot.isDraggingOver, 
                Boolean(dropSnapshot.draggingFromThisWith)
              ),
              display: 'flex',
              flexDirection: 'column',
              opacity: 'inherit',
              padding: `${grid}px`,
              border: `${grid}px`,
              paddingBottom: 0,
              transition: 'background-color 0.2s ease, opacity 0.1s ease',
              userSelect: 'none',
              width: '200px',              
            }}        
            ref={dropProvided.innerRef} 
            {...dropProvided.droppableProps}   
          >
            <Card               
              key={`zone_card_${index}`} 
              title={`${zone} - ${dispatchMap['zone_dispatch_map'][zone].length} calls`}
              styles={{ 
                header: {
                  backgroundColor: '#5997EF',
                  minHeight: '20px'
                },
                body: { 
                  padding: '0px 2px',         
                } 
              }}
            >
              {
                dispatchMap['zone_dispatch_map'][zone].map((dispatch, index) => (
                  <Draggable key={dispatch.id} draggableId={String(`dispatch_${dispatch.id}` || `dispatch_${index}`)} index={index}>
                    {(dragProvided: DraggableProvided, dragSnapshot: DraggableStateSnapshot) => (
                      <DispatchItem
                        key={dispatch.id}
                        dispatch={dispatch}
                        isDragging={dragSnapshot.isDragging}
                        isGroupedOver={Boolean(dragSnapshot.combineTargetFor)}
                        provided={dragProvided}
                      />
                    )}
                  </Draggable>
                ))
              }
              {dropProvided.placeholder}
            </Card>
          </div>
        )}
      </Droppable>
      
    ));
    
  } else if (listType == "Date") {
    const employeeId = Number(listId?.replaceAll('field_list_', ''));
    let employeeDispatchMap: API.DispatchByDate = {};
    if (!isNaN(employeeId) && employeeId > 0) {
      employeeDispatchMap = groupEmployeeDispatchByDate(dispatches, employeeId);
    }
    
    const app_dates = Object.keys(employeeDispatchMap['employee_dispatch_map']);
    return app_dates.map((app_date, index) => (
      <Droppable
        droppableId={`employee_${employeeId}_${app_date}`}
        type={"Dispatch"}      
        direction="vertical"
        key={`field_app_${employeeId}_${app_date}`}
        ignoreContainerClipping={true}
        isDropDisabled={false}
        isCombineEnabled={false}
        renderClone={undefined}
      >
        {(dropProvided: DroppableProvided, dropSnapshot: DroppableStateSnapshot) => (
          <div
            style={{
              backgroundColor: getBackgroundColor(
                dropSnapshot.isDraggingOver, 
                Boolean(dropSnapshot.draggingFromThisWith)
              ),
              display: 'flex',
              flexDirection: 'column',
              opacity: 'inherit',
              padding: `${grid}px`,
              border: `${grid}px`,
              paddingBottom: 0,
              transition: 'background-color 0.2s ease, opacity 0.1s ease',
              userSelect: 'none',
              width: '200px',              
            }}        
            ref={dropProvided.innerRef} 
            {...dropProvided.droppableProps}   
          >
            <Card               
              key={`date_card_${index}`} 
              title={`${dayjs(app_date).format('MMM DD')} (${dayjs(app_date).format('ddd')}) - ${employeeDispatchMap['employee_dispatch_map'][app_date].length} calls`}
              styles={{ 
                header: {
                  backgroundColor: '#5997EF',
                  minHeight: '20px'
                },
                body: {                   
                  padding: '0px 2px',         
                } 
              }}
            >
              {
                employeeDispatchMap['employee_dispatch_map'][app_date].map((dispatch, index) => (
                  <Draggable key={dispatch.id} draggableId={String(`dispatch_${dispatch.id}` || `dispatch_${index}`)} index={index}>
                    {(dragProvided: DraggableProvided, dragSnapshot: DraggableStateSnapshot) => (
                      <DispatchItem
                        key={dispatch.id}
                        dispatch={dispatch}
                        isDragging={dragSnapshot.isDragging}
                        isGroupedOver={Boolean(dragSnapshot.combineTargetFor)}
                        provided={dragProvided}
                      />
                    )}
                  </Draggable>
                ))
              }
              {dropProvided.placeholder}
            </Card>
          </div>
        )}
      </Droppable>
    ));
  } else {
    return dispatches.map((dispatch, index) => (
      <Draggable key={dispatch.id} draggableId={String(`dispatch_${dispatch.id}` || `dispatch_${index}`)} index={index}>
        {(dragProvided: DraggableProvided, dragSnapshot: DraggableStateSnapshot) => (
          <DispatchItem
            key={dispatch.id}
            dispatch={dispatch}
            isDragging={dragSnapshot.isDragging}
            isGroupedOver={Boolean(dragSnapshot.combineTargetFor)}
            provided={dragProvided}
          />
        )}
      </Draggable>
    ));
  }
  
});

function InnerList(props: InnerListProps): React.ReactElement {
  const { dispatches, dropProvided, listType, listId } = props;
  const title = props.title ? <Title>{props.title}</Title> : null;
  
  return (
    <div>
      {title}      
      <div 
        ref={dropProvided.innerRef}        
        {...dropProvided.droppableProps}
        style={{
          minHeight: `${scrollContainerHeight}px`,
          paddingBottom: `${grid}px`
        }}
      >
        <InnerDispatchList dispatches={dispatches} listType={listType} listId={listId} />        
        {dropProvided.placeholder}
      </div>
    </div>
  );
}

export default function DispatchList(props: DispatchListProps): React.ReactElement {
  const {
    ignoreContainerClipping,
    internalScroll,
    scrollContainerStyle,
    isDropDisabled,
    isCombineEnabled,
    listId = 'LIST',
    listType,
    style,
    dispatches,
    title,
    useClone,
  } = props;  
  
  return (
    <Droppable
      droppableId={listId}
      type={listType}      
      direction="vertical"
      // mode='virtual'
      ignoreContainerClipping={Boolean(ignoreContainerClipping)}
      isDropDisabled={Boolean(isDropDisabled)}
      isCombineEnabled={Boolean(isCombineEnabled)}
      renderClone={
        useClone
          ? (provided: DraggableProvided, snapshot: DraggableStateSnapshot, descriptor: any) => (
              <DispatchItem
                dispatch={dispatches[descriptor.source.index]}
                provided={provided}
                isDragging={snapshot.isDragging}
                isClone
              />
            )
          : undefined
      }
    >
      {(dropProvided: DroppableProvided, dropSnapshot: DroppableStateSnapshot) => (
        <div
          style={{
            backgroundColor: getBackgroundColor(
              dropSnapshot.isDraggingOver, 
              Boolean(dropSnapshot.draggingFromThisWith)
            ),
            display: 'flex',
            flexDirection: 'column',
            opacity: isDropDisabled ? 0.5 : 'inherit',
            padding: `${grid}px`,
            border: `${grid}px`,
            paddingBottom: 0,
            transition: 'background-color 0.2s ease, opacity 0.1s ease',
            userSelect: 'none',
            width: '220px',
            ...style
          }}          
        >
          {internalScroll ? (
            <div 
              style={{
                overflowX: 'hidden',
                overflowY: 'auto',
                maxHeight: `${scrollContainerHeight}px`,
                ...scrollContainerStyle
              }}
            >
              <InnerList dispatches={dispatches} title={title} dropProvided={dropProvided} listType={listType} listId={listId} />
            </div>
          ) : (
            <InnerList dispatches={dispatches} title={title} dropProvided={dropProvided} listType={listType} listId={listId} />
          )}
        </div>
      )}
    </Droppable>
  );
}
