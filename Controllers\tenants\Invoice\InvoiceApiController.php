<?php

namespace Butler\Controllers\tenants\Invoice;

use Butler\Controllers\api\ApiBaseController;
use Butler\Controllers\tenants\TenantBaseController;
use Butler\Helpers\Func;
use Butler\Helpers\FuncModel;
use Butler\Models\ButlerDB;
use Butler\Models\Customer\CustomerProperty;
use Butler\Models\Dispatch\Dispatch;
use Butler\Models\Dispatch\DispatchInvoice;
use Butler\Models\Dispatch\DispatchInvoiceItem;
use Butler\Models\PriceBook\PbService;
use Butler\Models\Sequence\SequenceInvoiceNo;
use Butler\Models\Sequence\SequencePurchaseNo;
use Butler\Services\Tenant\ExportPdfInvoiceService;
use Butler\Services\Tenant\InvoiceService;
use Illuminate\Database\Eloquent\Builder;

class InvoiceApiController extends ApiBaseController
{
    private InvoiceService $invoiceService;

    public function __construct()
    {
        parent::__construct();

        $this->controllerBaseUrl = BASE_API_URL . 'tenant/invoice';
        $this->invoiceService = new InvoiceService();
    }

    /**
     * Perform Update or Create
     *
     * @return void
     */
    public function createOrUpdate()
    {
        $data = $this->data;
        $id = $data['id'] ?? null;

        /** @var DispatchInvoice|null $row */
        if (!$id) {
            $data['purchase_no'] = SequencePurchaseNo::generateNo();
            $dataMain = DispatchInvoice::getBoundData($data, true, true);
            $row = new DispatchInvoice($dataMain);
        } else {
            /** @var DispatchInvoice $row */
            $row = DispatchInvoice::find($id);
            if (!$row) {
                $this->jsonWithError(null, 404, 'Invoice #' . $id . ' does not exist!');
            }
        }

        /** @var Dispatch $dispatch */
        $dispatch = $row->dispatch()->with('customer', 'addresses')->first();
        $customer = $dispatch->customer;
        /** @var CustomerProperty $addr */
        $addr = $dispatch->addresses[0] ?? null;

        $db = ButlerDB::getDb();
        try {
            $db->beginTransaction();

            if ($customer) {
                $row->customer_name = $dispatch->customer->name;
                $row->customer_email = $dispatch->customer->email;
                $data['customer_phone'] = $customer->customerPhones()->first()?->phone;
            }

            if ($addr) {
                $data['customer_city'] = $addr->city;
                $data['customer_state'] = $addr->state;
                $data['customer_street_direction'] = $addr->streetDirection;
                $data['customer_street_name'] = $addr->streetName;
                $data['customer_street_number'] = $addr->streetNumber;
                $data['customer_unit_number'] = $addr->unitNumber;
                $data['customer_unit_type'] = $addr->unitType;
                $data['customer_zip'] = $addr->zip;
                $data['customer_zip_four'] = $addr->zipFour;
            }

            $row->fill($data);

            $row->save();

            if (key_exists('items', $data)) {
                $row->items()->delete();

                if ($data['items'] ?? null) {
                    $ind = 1;
                    $items = [];
                    foreach ($data['items'] as $x) {
                        if ($x['service_id']) {
                            /** @var PbService $service */
                            $service = PbService::find($x['service_id']);

                            $item = new DispatchInvoiceItem($x);
                            $item->dispatch_estimate_id = $row->id;
                            $item->name = $service->name;
                            $item->cost = $service->cost;
                            $item->price_total = $item->price * $item->qty;

                            if ($item->taxable) {
                                $item->tax_percent = floatval($row->tax_percent);
                            } else {
                                $item->tax_percent = 0;
                            }
                            $item->tax_amount = $item->tax_percent * $item->price_total / 100;
                            $item->total = $item->price_total + $item->tax_amount;
                            $item->sort_order = $ind++;
                            $items[] = $item;
                        }
                    }

                    // Bulk saving
                    $row->items()->saveMany($items);

                    // Aggregation column updates
                    $row->subtotal = 0;
                    $row->tax_amount = 0;
                    foreach ($row->items as $item) {
                        $row->subtotal += $item->price_total;
                        $row->tax_amount += $item->tax_amount;
                    }
                    $row->total_item_count = count($row->items);
                    $row->grand_total = $row->subtotal + $row->tax_amount - $row->discount_amount;

                    $row->save();
                }
            }

            $db->commit();
        } catch (\Exception $exception) {
            $db->rollBack();
            $this->jsonWithError($exception);
        }

        if ('pdf' == ($this->data['format'] ?? '')) {
            $this->getPdf();
        }

        $this->json($row);;
    }

    /**
     * Perform Update
     *
     * @return void
     */
    public function update()
    {
        $data = $this->data;
        $id = $data['id'] ?? null;

        /** @var DispatchInvoice $row */
        $row = DispatchInvoice::findOrFail($id);

        /** @var Dispatch $dispatch */
        $dispatch = $row->dispatch()->with('customer', 'addresses')->first();
        $customer = $dispatch->customer;

        $db = ButlerDB::getDb();
        try {
            $db->beginTransaction();

            /*if ($customer) {
                $row->customer_name = $dispatch->customer->name;
                $row->customer_email = $dispatch->customer->email;
                $data['customer_phone'] = $customer->customerPhones()->first()?->phone;
            }*/

            $row->fill($data);

            $row->save();

            if (key_exists('items', $data)) {
                $row->items()->delete();

                if ($data['items'] ?? null) {
                    $ind = 1;
                    $items = [];
                    foreach ($data['items'] as $x) {
                        if ($x['service_id']) {
                            /** @var PbService $service */
                            $service = PbService::find($x['service_id']);

                            $item = new DispatchInvoiceItem($x);
                            $item->dispatch_estimate_id = $row->id;
                            $item->name = $service->name;
                            $item->cost = $service->cost;
                            $item->price_total = $item->price * $item->qty;

                            if ($item->taxable) {
                                $item->tax_percent = floatval($row->tax_percent);
                            } else {
                                $item->tax_percent = 0;
                            }
                            $item->tax_amount = $item->tax_percent * $item->price_total / 100;
                            $item->total = $item->price_total + $item->tax_amount;
                            $item->sort_order = $ind++;
                            $items[] = $item;
                        }
                    }

                    // Bulk saving
                    $row->items()->saveMany($items);

                    // Aggregation column updates
                    $row->subtotal = 0;
                    $row->tax_amount = 0;
                    foreach ($row->items as $item) {
                        $row->subtotal += $item->price_total;
                        $row->tax_amount += $item->tax_amount;
                    }
                    $row->total_item_count = count($row->items);
                    $row->grand_total = $row->subtotal + $row->tax_amount - $row->discount_amount;

                    $row->save();
                }
            }

            $db->commit();
        } catch (\Exception $exception) {
            $db->rollBack();
            $this->jsonWithError($exception);
        }

        if ('pdf' == $this->data['format'] ?? '') {
            $this->getPdf();
        }

        $this->json($row);;
    }

    public function getPdf()
    {
        $id = $this->data['id'] ?? null;

        /** @var DispatchInvoice|null $row */
        if (!$id) {
            $this->jsonWithError(null, 500, 'Invalid request! ID is required!');
        }

        /** @var DispatchInvoice $row */
        $row = DispatchInvoice::findOrFail($id);
        $row->load('items');

        $service = new ExportPdfInvoiceService();
        $result = $service->exportPdf(array_replace($this->data, ['estimate' => $row]));

        $this->jsonWithSuccess($result);
    }

    public function getOne()
    {
        $params = $this->data;

        if (!($params['dispatch_id'] ?? null)) {
            $this->jsonWithError(null, 400, 'Job ID is required!');
        }

        $qb = $this->invoiceService->getQueryBuilder($params);
        $row = $qb->first();

        try {
            if (!$row) {
                if (($params['creation_mode'] ?? false)) {
                    $newRow = $params;
                    $newRow['purchase_no'] = SequencePurchaseNo::generateNo();
                    $newRow = DispatchInvoice::getBoundData($newRow, true, true);
                    $row = DispatchInvoice::create($newRow);
                } else {
                    $this->jsonWithError(null, 400, 'creation_mode is required!');
                }
            }
        } catch (\Exception $exception) {
            $this->renderWithError($exception);
        }

        $this->render(null, $row);
    }

    public function index()
    {
        $params = $this->data;
        $params['with'] = ($params['with'] ?? '') . ',dispatch,customer';
        $params['tenant_id'] = $this->tenant_id;

        $qb = $this->invoiceService->getQueryBuilder($params);

        $qb->orderByDesc('id');

        $result = FuncModel::getResultsWithPagination($qb, $this->page, $this->perPage, $qb->count());

        $this->jsonWithSuccess($result);
    }

    /**
     * Edit page
     *
     * @return void
     */
    public function edit() {

        $params = $this->data;
        $params['with'] = ($params['with'] ?? '') . ',dispatch,customer,items';
        $qb = $this->invoiceService->getQueryBuilder($params);

        $result = null;
        try {
            $result = $qb->firstOrFail();
        } catch (\Exception $exception) {
            $this->jsonWithError($exception);
        }
        $this->title = 'Invoice Detail - #' . $result->id;

        $this->render('estimate_edit', ['dbRow' => $result]);
    }

    /**
     * Create a new invoice
     *
     * @return void
     */
    public function add()
    {
        $data = $this->data;
        $id = $data['id'] ?? null;

        /** @var DispatchInvoice|null $row */
        if (!$id) {
            $data['invoice_no'] = SequenceInvoiceNo::generateNo();
            $dataMain = DispatchInvoice::getBoundData($data, true, true);
            $row = new DispatchInvoice($dataMain);
        } else {
            /** @var DispatchInvoice $row */
            $row = DispatchInvoice::find($id);
            if (!$row) {
                $this->jsonWithError(null, 404, 'Invoice #' . $id . ' does not exist!');
            }
        }

        /** @var Dispatch $dispatch */
        $dispatch = $row->dispatch()->with('customer', 'address', 'customer.customerPhones')->first();
        $customer = $dispatch->customer;
        /** @var CustomerProperty $addr */
        $addr = $dispatch->address ?? null;

        $db = ButlerDB::getDb();
        try {
            $db->beginTransaction();

            if ($customer) {
                $row->customer_name = $dispatch->customer->name;
                $row->customer_email = $dispatch->customer->email;
                $data['customer_phone'] = $customer->customerPhones ? $customer->customerPhones[0]?->phone : null;
            }

            if ($addr) {
                $data['customer_city'] = $addr->city;
                $data['customer_state'] = $addr->state;
                $data['customer_street_direction'] = $addr->streetDirection;
                $data['customer_street_name'] = $addr->streetName;
                $data['customer_street_number'] = $addr->streetNumber;
                $data['customer_unit_number'] = $addr->unitNumber;
                $data['customer_unit_type'] = $addr->unitType;
                $data['customer_zip'] = $addr->zip;
                $data['customer_zip_four'] = $addr->zipFour;
            }

            $row->fill($data);

            $row->save();

            if (key_exists('items', $data)) {
                $row->items()->delete();

                if ($data['items'] ?? null) {
                    $ind = 1;
                    $items = [];
                    foreach ($data['items'] as $x) {
                        if ($x['service_id']) {
                            /** @var PbService $service */
                            $service = PbService::find($x['service_id']);

                            $item = new DispatchInvoiceItem($x);
                            $item->dispatch_invoice_id = $row->id;
                            $item->name = $x['name'] ?? $service->name;
                            $item->cost = $service->cost;
                            $item->price_total = $item->price * $item->qty;

                            if ($item->taxable) {
                                $item->tax_percent = floatval($row->tax_percent);
                            } else {
                                $item->tax_percent = 0;
                            }
                            $item->tax_amount = round($item->tax_percent * $item->price_total / 100, 2);
                            $item->total = $item->price_total + $item->tax_amount;
                            $item->sort_order = $ind++;
                            $items[] = $item;
                        }
                    }

                    // Bulk saving
                    $row->items()->saveMany($items);

                    // Aggregation column updates
                    $row->subtotal = 0;
                    $row->tax_amount = 0;
                    foreach ($row->items as $item) {
                        $row->subtotal += $item->price_total;
                        $row->tax_amount += $item->tax_amount;
                    }
                    $row->total_item_count = count($row->items);
                    $row->grand_total = $row->subtotal + $row->tax_amount - $row->discount_amount;

                    $row->save();
                }
            }

            $db->commit();
        } catch (\Exception $exception) {
            $db->rollBack();
            $this->jsonWithError($exception);
        }

        if ('pdf' == ($this->data['format'] ?? '')) {
            $this->getPdf();
        }

        $this->json($row);;
    }

}