<?php

namespace Butler\Controllers\tenants\Invoice;

use Butler\Controllers\api\ApiBaseController;
use Butler\Controllers\tenants\TenantBaseController;
use Butler\Helpers\Func;
use Butler\Helpers\FuncModel;
use Butler\Lib\FileLib;
use Butler\Models\ButlerDB;
use Butler\Models\Customer\CustomerProperty;
use Butler\Models\Dispatch\Dispatch;
use Butler\Models\Dispatch\DispatchEstimate;
use Butler\Models\Dispatch\DispatchInvoice;
use Butler\Models\Dispatch\DispatchInvoiceItem;
use Butler\Models\File;
use Butler\Models\PriceBook\PbService;
use Butler\Models\Sequence\SequenceInvoiceNo;
use Butler\Models\Sequence\SequencePurchaseNo;
use Butler\Services\FileService;
use Butler\Services\Tenant\ExportPdfInvoiceService;
use Butler\Services\Tenant\InvoiceService;

class InvoiceApiController extends ApiBaseController
{
    private InvoiceService $invoiceService;

    public function __construct()
    {
        parent::__construct();

        $this->controllerBaseUrl = BASE_API_URL . 'tenant/invoice';
        $this->invoiceService = new InvoiceService();
    }

    protected function updateRelations(DispatchInvoice $row, $lineItems)
    {
        $row->items()->delete();

        if ($lineItems) {
            $ind = 1;
            $items = [];
            foreach ($lineItems as $x) {
                if ($x['service_id']) {
                    /** @var PbService $service */
                    $service = PbService::find($x['service_id']);

                    $item = new DispatchInvoiceItem($x);
                    $item->dispatch_invoice_id = $row->id;
                    $item->name = $service->name;
                    $item->cost = $service->cost;
                    $item->price_total = $item->price * $item->qty;

                    if ($item->taxable) {
                        $item->tax_percent = floatval($row->tax_percent);
                    } else {
                        $item->tax_percent = 0;
                    }
                    $item->tax_amount = $item->tax_percent * $item->price_total / 100;
                    $item->total = $item->price_total + $item->tax_amount;
                    $item->sort_order = $ind++;
                    $items[] = $item;
                }
            }

            // Bulk saving
            $row->items()->saveMany($items);
        }

        // Aggregation column updates
        $row->subtotal = 0;
        $row->tax_amount = 0;
        foreach ($row->items as $item) {
            $row->subtotal += $item->price_total;
            $row->tax_amount += $item->tax_amount;
        }
        $row->total_item_count = count($row->items);
        $row->grand_total = $row->subtotal + $row->tax_amount - $row->discount_amount;

        $row->save();
    }

    /**
     * Perform Update
     *
     * @return void
     */
    public function update($args)
    {
        $data = $this->data;
        $id = $args['id'] ?? null;

        try {
            /** @var DispatchInvoice $row */
            $row = DispatchInvoice::findOrFail($id);

            $db = ButlerDB::getDb();
            try {
                $db->beginTransaction();

                $row->fill($data);

                $row->save();

                if (key_exists('items', $data)) {
                    $this->updateRelations($row, $data['items']);
                }

                $db->commit();
            } catch (\Exception $exception) {
                $db->rollBack();
                $this->jsonWithError($exception);
            }

            if ('pdf' == ($this->data['format'] ?? '')) {
                $this->getPdf();
            }
        } catch (\Exception $exception) {
            $this->jsonWithError($exception);
        }

        $this->jsonWithSuccess($row);;
    }

    /**
     * @param $return
     * @return void
     */
    public function getPdf($return = false)
    {
        $id = $this->data['id'] ?? null;

        /** @var DispatchInvoice|null $row */
        if (!$id) {
            if ($return !== true)
                $this->jsonWithError(null, 500, 'Invalid request! ID is required!');
        }

        /** @var DispatchInvoice $row */
        $row = DispatchInvoice::findOrFail($id);
        $row->load('items');
        $row->load('file');
        if ($row->file) {
            FileService::removeFile($row->file);
        }

        $service = new ExportPdfInvoiceService();
        $result = $service->exportPdf(array_replace($this->data, ['invoice' => $row]));

        // Making file relation
        $pathInfo = pathinfo($result['key']);
        $file = File::create([
            'category' => File::CAT_ESTIMATE_PDF,
            'file_name' => $pathInfo['filename'],
            'clean_file_name' => $pathInfo['filename'],
            'path' => $result['key'],
            'org_path' => $result['key'],
            'type' => FileLib::mime2ext($pathInfo['extension'], true),
        ]);
        $row->file()->associate($file);
        $row->save();

        if ($return === true) {
            return $file;
        } else {
            $this->jsonWithSuccess($file);
        }
    }

    public function getOne()
    {
        $params = $this->data;

        if (!($params['id'] ?? null)) {
            $this->jsonWithError(null, 400, 'ID is required!');
        }

        $qb = $this->invoiceService->getQueryBuilder($params);
        $row = $qb->first();
        if (!$row) {
            $this->jsonWithError(null, 404, 'Invoice does not exist!');
        }

        $this->jsonWithSuccess($row);
    }

    public function index()
    {
        $params = $this->data;
        $params['with'] = ($params['with'] ?? '') . ',dispatch,customer';
        $params['tenant_id'] = $this->tenant_id;

        $qb = $this->invoiceService->getQueryBuilder($params);

        $this->invoiceService->applyOrderBy($qb, $params);

        $result = FuncModel::getResultsWithPagination($qb, $this->page, $this->perPage, $qb->count());

        $this->jsonWithSuccess($result);
    }

    /**
     * Create a new invoice
     *
     * @return void
     */
    public function add()
    {
        $data = $this->data;
        $id = $data['id'] ?? null;

        /** @var DispatchInvoice|null $row */
        if (!$id) {
            $data['invoice_no'] = SequenceInvoiceNo::generateNo();
            $dataMain = DispatchInvoice::getBoundData($data, true, true);
            $row = new DispatchInvoice($dataMain);
        } else {
            /** @var DispatchInvoice $row */
            $row = DispatchInvoice::find($id);
            if (!$row) {
                $this->jsonWithError(null, 404, 'Invoice #' . $id . ' does not exist!');
            }
        }

        /** @var Dispatch $dispatch */
        $dispatch = $row->dispatch()->with('customer', 'address', 'customer.customerPhones')->first();
        $customer = $dispatch->customer;
        /** @var CustomerProperty $addr */
        $addr = $dispatch->address ?? null;

        $db = ButlerDB::getDb();
        try {
            $db->beginTransaction();

            if ($customer) {
                $row->customer_name = $dispatch->customer->name;
                $row->customer_email = $dispatch->customer->email;
                $data['customer_phone'] = $customer->customerPhones ? $customer->customerPhones[0]?->phone : null;
            }

            if ($addr) {
                $data['customer_city'] = $addr->city;
                $data['customer_state'] = $addr->state;
                $data['customer_street_direction'] = $addr->streetDirection;
                $data['customer_street_name'] = $addr->streetName;
                $data['customer_street_number'] = $addr->streetNumber;
                $data['customer_unit_number'] = $addr->unitNumber;
                $data['customer_unit_type'] = $addr->unitType;
                $data['customer_zip'] = $addr->zip;
                $data['customer_zip_four'] = $addr->zipFour;
            }

            $row->fill($data);

            $row->save();

            if (key_exists('items', $data)) {
                $this->updateRelations($row, $data['items']);
            }

            $db->commit();
        } catch (\Exception $exception) {
            $db->rollBack();
            $this->jsonWithError($exception);
        }

        if ('pdf' == ($this->data['format'] ?? '')) {
            $this->getPdf();
        }

        $this->jsonWithSuccess($row);;
    }

    public function addByEstimate($args)
    {
        $estimateId = $args['estimateId'] ?? null;
        if (!$estimateId) {
            $this->jsonWithInvalidRequest('estimateId is required!');
        }

        $db = ButlerDB::getDb();
        try {
            /** @var DispatchEstimate $estimate */
            $estimate = DispatchEstimate::query()
                ->where('id', $estimateId)
                ->with(['items', 'dispatch', 'customer'])
                ->firstOrFail();

            $invoiceData = $estimate->toArray();
            unset($invoiceData['file_id']);
            unset($invoiceData['created_at']);
            unset($invoiceData['updated_at']);
            unset($invoiceData['created_by']);
            unset($invoiceData['updated_by']);

            $invoiceData['invoice_no'] = SequenceInvoiceNo::generateNo();
            $dataMain = DispatchInvoice::getBoundData($invoiceData, true, true);
            $row = new DispatchInvoice($dataMain);
            $row->estimate_id = $estimate->id;
            $row->service_date = $estimate->work_start_date;

            // transation
            $db->beginTransaction();

            $row->fill($invoiceData);
            $row->save();
            if (key_exists('items', $invoiceData)) {
                foreach ($invoiceData['items'] as &$x) {
                    unset($x['id']);
                }
                $this->updateRelations($row, $invoiceData['items']);
            }
            $db->commit();

            $this->data['id'] = $row->id;
            $fileObj = $this->getPdf(true);

            $row->refresh();
            $row->load('file');
            $this->jsonWithSuccess($row);;
        } catch (\Exception $exception) {
            $db->rollBack();
            $this->jsonWithError($exception);
        }
    }

}