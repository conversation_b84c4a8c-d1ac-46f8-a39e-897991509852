{"cwd": "F:\\htdocs\\development_team\\frontend", "pkg": {"name": "dipatchbutler-frontend-app", "version": "2.0.0", "private": true, "description": "New frontend application for dispatchbutler app", "repository": "https://github.com/developmentbutler/development_team.git", "license": "UNLICENSED", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "deploy": "yarn build && yarn gh-pages", "dev": "yarn start:dev", "gh-pages": "gh-pages -d dist", "postinstall": "max setup", "jest": "jest", "lint": "yarn lint:js && yarn lint:prettier && yarn tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "openapi": "max openapi", "prepare": "husky", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "yarn build && max preview --port 8000", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "serve": "umi-serve", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev PORT=3000 max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "build:sandbox": "cross-env REACT_APP_ENV=sandbox UMI_ENV=sandbox max build", "test": "jest", "test:coverage": "yarn jest -- --coverage", "test:update": "yarn jest -- -u", "tsc": "tsc --noEmit"}, "browserslist": ["defaults"], "dependencies": {"@ant-design/icons": "5.x", "@ant-design/pro-components": "^2.7.19", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@react-google-maps/api": "^2.20.7", "@types/prop-types": "^15.7.15", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-window": "^1.8.8", "@vis.gl/react-google-maps": "^1.5.4", "antd": "^5.25.4", "antd-style": "^3.7.0", "classnames": "^2.5.1", "dayjs": "^1.11.13", "prop-types": "^15.8.1", "react": "^19.1.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^19.1.0", "react-virtuoso": "^4.13.0", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10"}, "devDependencies": {"@ant-design/pro-cli": "^3.3.0", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.0.1", "@types/express": "^4.17.21", "@types/jest": "^29.5.13", "@types/lodash": "^4.17.10", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@types/react-helmet": "^6.1.11", "@umijs/lint": "^4.3.24", "@umijs/max": "^4.3.24", "cross-env": "^7.0.3", "eslint": "^8.57.1", "express": "^4.21.1", "gh-pages": "^6.1.1", "husky": "^9.1.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.10", "mockjs": "^1.1.0", "prettier": "^3.3.3", "ts-node": "^10.9.2", "typescript": "^5.6.3", "umi-presets-pro": "^2.0.3", "umi-serve": "^1.9.11"}, "engines": {"node": ">=12.0.0"}}, "pkgPath": "F:\\htdocs\\development_team\\frontend\\package.json", "plugins": {"./node_modules/@umijs/core/dist/service/servicePlugin": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "preset", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/core/dist/service/servicePlugin.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/core/dist/service/servicePlugin", "key": "servicePlugin"}, "@umijs/preset-umi": {"config": {}, "time": {"hooks": {}, "register": 32}, "enableBy": "register", "type": "preset", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/index.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "@umijs/preset-umi", "key": "umi"}, "./node_modules/@umijs/max/dist/preset": {"config": {}, "time": {"hooks": {}, "register": 11}, "enableBy": "register", "type": "preset", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/max/dist/preset.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/max/dist/preset", "key": "preset"}, "umi-presets-pro": {"config": {}, "time": {"hooks": {"onStart": [81]}, "register": 5}, "enableBy": "register", "type": "preset", "path": "F:/htdocs/development_team/frontend/node_modules/umi-presets-pro/dist/index.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "umi-presets-pro", "key": "umiPresetsPro"}, "./node_modules/@umijs/preset-umi/dist/registerMethods": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 7}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/registerMethods.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/registerMethods", "key": "registerMethods"}, "@umijs/did-you-know": {"config": {}, "time": {"hooks": {"onStart": [92]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/did-you-know/dist/plugin.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "@umijs/did-you-know", "key": "umijsDidYouKnow"}, "./node_modules/@umijs/preset-umi/dist/features/404/404": {"config": {}, "time": {"hooks": {"modifyRoutes": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/404/404.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/404/404", "key": "404"}, "./node_modules/@umijs/preset-umi/dist/features/appData/appData": {"config": {}, "time": {"hooks": {"modifyAppData": [363]}, "register": 53}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/appData/appData.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/appData", "key": "appData"}, "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/appData/umiInfo.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo", "key": "umiInfo"}, "./node_modules/@umijs/preset-umi/dist/features/check/check": {"config": {}, "time": {"hooks": {"onCheckConfig": [0], "onCheck": [0]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/check/check.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/check/check", "key": "check"}, "./node_modules/@umijs/preset-umi/dist/features/check/babel722": {"config": {}, "time": {"hooks": {"onCheck": [1]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/check/babel722.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/check/babel722", "key": "babel722"}, "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting", "key": "codeSplitting"}, "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 16}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins", "key": "configPlugins"}, "virtual: config-title": {"id": "virtual: config-title", "key": "title", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styles": {"id": "virtual: config-styles", "key": "styles", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-scripts": {"id": "virtual: config-scripts", "key": "scripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routes": {"id": "virtual: config-routes", "key": "routes", "config": {"onChange": "regenerateTmpFiles"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routeLoader": {"id": "virtual: config-routeLoader", "key": "routeLoader", "config": {"default": {"moduleType": "esm"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-reactRouter5Compat": {"id": "virtual: config-reactRouter5Compat", "key": "reactRouter5Compat", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-presets": {"id": "virtual: config-presets", "key": "presets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-plugins": {"id": "virtual: config-plugins", "key": "plugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-npmClient": {"id": "virtual: config-npmClient", "key": "npmClient", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mountElementId": {"id": "virtual: config-mountElementId", "key": "mountElementId", "config": {"default": "root"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-metas": {"id": "virtual: config-metas", "key": "metas", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-links": {"id": "virtual: config-links", "key": "links", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-historyWithQuery": {"id": "virtual: config-historyWithQuery", "key": "historyWithQuery", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-history": {"id": "virtual: config-history", "key": "history", "config": {"default": {"type": "browser"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-headScripts": {"id": "virtual: config-headScripts", "key": "headScripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esbuildMinifyIIFE": {"id": "virtual: config-esbuildMinifyIIFE", "key": "esbuildMinifyIIFE", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionRoutes": {"id": "virtual: config-conventionRoutes", "key": "conventionRoutes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionLayout": {"id": "virtual: config-conventionLayout", "key": "conventionLayout", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-base": {"id": "virtual: config-base", "key": "base", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-analyze": {"id": "virtual: config-analyze", "key": "analyze", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-writeToDisk": {"id": "virtual: config-writeToDisk", "key": "writeToDisk", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-transformRuntime": {"id": "virtual: config-transformRuntime", "key": "transformRuntime", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-theme": {"id": "virtual: config-theme", "key": "theme", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-targets": {"id": "virtual: config-targets", "key": "targets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgr": {"id": "virtual: config-svgr", "key": "svgr", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgo": {"id": "virtual: config-svgo", "key": "svgo", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-stylusLoader": {"id": "virtual: config-stylusLoader", "key": "stylus<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styleLoader": {"id": "virtual: config-style<PERSON>oader", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspilerOptions": {"id": "virtual: config-srcTranspilerOptions", "key": "srcTranspilerOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspiler": {"id": "virtual: config-srcTranspiler", "key": "srcTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-sassLoader": {"id": "virtual: config-sassLoader", "key": "sass<PERSON><PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-runtimePublicPath": {"id": "virtual: config-runtimePublicPath", "key": "runtimePublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-purgeCSS": {"id": "virtual: config-purgeCSS", "key": "purgeCSS", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-publicPath": {"id": "virtual: config-publicPath", "key": "publicPath", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-proxy": {"id": "virtual: config-proxy", "key": "proxy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-postcssLoader": {"id": "virtual: config-postcssLoader", "key": "postcss<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-outputPath": {"id": "virtual: config-outputPath", "key": "outputPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-normalCSSLoaderModules": {"id": "virtual: config-normalCSSLoaderModules", "key": "normalCSSLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mfsu": {"id": "virtual: config-mfsu", "key": "mfsu", "config": {"default": {"strategy": "eager"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mdx": {"id": "virtual: config-mdx", "key": "mdx", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-manifest": {"id": "virtual: config-manifest", "key": "manifest", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-lessLoader": {"id": "virtual: config-less<PERSON><PERSON>der", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifierOptions": {"id": "virtual: config-jsMinifierOptions", "key": "jsMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifier": {"id": "virtual: config-jsMinifier", "key": "jsMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-inlineLimit": {"id": "virtual: config-inlineLimit", "key": "inlineLimit", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-ignoreMomentLocale": {"id": "virtual: config-ignoreMomentLocale", "key": "ignoreMomentLocale", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-https": {"id": "virtual: config-https", "key": "https", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-hash": {"id": "virtual: config-hash", "key": "hash", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-forkTSChecker": {"id": "virtual: config-fork<PERSON><PERSON><PERSON><PERSON>", "key": "forkTSChecker", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-fastRefresh": {"id": "virtual: config-fastRefresh", "key": "fastRefresh", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraPostCSSPlugins": {"id": "virtual: config-extraPostCSSPlugins", "key": "extraPostCSSPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPresets": {"id": "virtual: config-extraBabelPresets", "key": "extraBabelPresets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPlugins": {"id": "virtual: config-extraBabelPlugins", "key": "extraBabelPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelIncludes": {"id": "virtual: config-extraBabelIncludes", "key": "extraBabelIncludes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-externals": {"id": "virtual: config-externals", "key": "externals", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esm": {"id": "virtual: config-esm", "key": "esm", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-devtool": {"id": "virtual: config-devtool", "key": "devtool", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-depTranspiler": {"id": "virtual: config-depTranspiler", "key": "depTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-define": {"id": "virtual: config-define", "key": "define", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-deadCode": {"id": "virtual: config-deadCode", "key": "deadCode", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssPublicPath": {"id": "virtual: config-cssPublicPath", "key": "cssPublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifierOptions": {"id": "virtual: config-cssMinifierOptions", "key": "cssMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifier": {"id": "virtual: config-cssMinifier", "key": "cssMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoaderModules": {"id": "virtual: config-cssLoaderModules", "key": "cssLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoader": {"id": "virtual: config-cssLoader", "key": "cssL<PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-copy": {"id": "virtual: config-copy", "key": "copy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-checkDepCssModules": {"id": "virtual: config-checkDepCssModules", "key": "checkDepCssModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-chainWebpack": {"id": "virtual: config-chainWebpack", "key": "chainWebpack", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cacheDirectoryPath": {"id": "virtual: config-cacheDirectoryPath", "key": "cacheDirectoryPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-babelLoaderCustomize": {"id": "virtual: config-babelLoaderCustomize", "key": "babelLoaderCustomize", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoprefixer": {"id": "virtual: config-autoprefixer", "key": "autoprefixer", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoCSSModules": {"id": "virtual: config-autoCSSModules", "key": "autoCSSModules", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-alias": {"id": "virtual: config-alias", "key": "alias", "config": {"default": {"umi": "@@/exports", "react": "F:\\htdocs\\development_team\\frontend\\node_modules\\react", "react-dom": "F:\\htdocs\\development_team\\frontend\\node_modules\\react-dom", "react-router": "F:\\htdocs\\development_team\\frontend\\node_modules\\react-router", "react-router-dom": "F:\\htdocs\\development_team\\frontend\\node_modules\\react-router-dom"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin", "key": "crossorigin"}, "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand", "key": "deps<PERSON>n<PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/devTool/devTool.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool", "key": "devTool"}, "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker": {"config": {}, "time": {"hooks": {}, "register": 137}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker", "key": "esbuildHelperChecker"}, "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi": {"config": {}, "time": {"hooks": {}, "register": 182}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/esmi/esmi.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi", "key": "esmi"}, "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic": {"config": {}, "time": {"hooks": {}, "register": 35}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic", "key": "exportStatic"}, "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons": {"config": {}, "time": {"hooks": {"modifyAppData": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/favicons/favicons.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons", "key": "favicons"}, "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/helmet/helmet.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet", "key": "helmet"}, "./node_modules/@umijs/preset-umi/dist/features/icons/icons": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/icons/icons.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/icons/icons", "key": "icons"}, "./node_modules/@umijs/preset-umi/dist/features/mock/mock": {"config": {}, "time": {"hooks": {}, "register": 60}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/mock/mock.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/mock/mock", "key": "mock"}, "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/mpa/mpa.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa", "key": "mpa"}, "./node_modules/@umijs/preset-umi/dist/features/okam/okam": {"config": {}, "time": {"hooks": {"modifyAppData": [0], "onCheck": [0]}, "register": 2}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/okam/okam.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/okam/okam", "key": "okam"}, "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/overrides/overrides.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides", "key": "overrides"}, "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency", "key": "phantomDependency"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill": {"config": {}, "time": {"hooks": {"modifyConfig": [1]}, "register": 3}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill", "key": "polyfill"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill", "key": "publicPathPolyfill"}, "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/prepare/prepare.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare", "key": "prepare"}, "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch", "key": "routePrefetch"}, "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/terminal/terminal.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal", "key": "terminal"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles": {"config": {}, "time": {"hooks": {}, "register": 10}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles", "key": "tmpFiles"}, "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader", "key": "clientLoader"}, "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps", "key": "routeProps"}, "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/ssr/ssr.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr", "key": "ssr"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes": {"config": {}, "time": {"hooks": {}, "register": 6}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes", "key": "configTypes"}, "./node_modules/@umijs/preset-umi/dist/features/transform/transform": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/transform/transform.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/transform/transform", "key": "transform"}, "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport", "key": "lowImport"}, "./node_modules/@umijs/preset-umi/dist/features/vite/vite": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/vite/vite.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/vite/vite", "key": "vite"}, "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute": {"config": {}, "time": {"hooks": {}, "register": 13}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute", "key": "apiRoute"}, "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect": {"config": {}, "time": {"hooks": {}, "register": 35}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/monorepo/redirect.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect", "key": "monorepoRedirect"}, "./node_modules/@umijs/preset-umi/dist/features/test/test": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/test/test.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/test/test", "key": "test"}, "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent", "key": "clickToComponent"}, "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/legacy/legacy.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy", "key": "legacy"}, "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose", "key": "classPropertiesLoose"}, "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack": {"config": {}, "time": {"hooks": {}, "register": 4}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/webpack/webpack.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack", "key": "preset-umi:webpack"}, "./node_modules/@umijs/preset-umi/dist/features/swc/swc": {"config": {}, "time": {"hooks": {"addOnDemandDeps": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/swc/swc.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/swc/swc", "key": "swc"}, "./node_modules/@umijs/preset-umi/dist/features/ui/ui": {"config": {}, "time": {"hooks": {}, "register": 6}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/ui/ui.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/ui/ui", "key": "ui"}, "./node_modules/@umijs/preset-umi/dist/features/mako/mako": {"config": {}, "time": {"hooks": {"modifyConfig": [1], "onStart": [120]}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/mako/mako.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/mako/mako", "key": "mako"}, "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian": {"config": {}, "time": {"hooks": {}, "register": 4}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian", "key": "hm<PERSON><PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad": {"config": {}, "time": {"hooks": {}, "register": 4}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad", "key": "routePreloadOnLoad"}, "./node_modules/@umijs/preset-umi/dist/features/forget/forget": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/forget/forget.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/forget/forget", "key": "forget"}, "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/bundler/bundler.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler", "key": "preset-umi:bundler"}, "./node_modules/@umijs/preset-umi/dist/commands/build": {"config": {}, "time": {"hooks": {}, "register": 12}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/build.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/build", "key": "build"}, "./node_modules/@umijs/preset-umi/dist/commands/config/config": {"config": {}, "time": {"hooks": {}, "register": 65}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/config/config.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/config/config", "key": "config"}, "./node_modules/@umijs/preset-umi/dist/commands/dev/dev": {"config": {}, "time": {"hooks": {"modifyAppData": [108], "onStart": [0]}, "register": 70}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/dev/dev.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/dev/dev", "key": "dev"}, "./node_modules/@umijs/preset-umi/dist/commands/help": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/help.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/help", "key": "help"}, "./node_modules/@umijs/preset-umi/dist/commands/lint": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/lint.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/lint", "key": "lint"}, "./node_modules/@umijs/preset-umi/dist/commands/setup": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/setup.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/setup", "key": "setup"}, "./node_modules/@umijs/preset-umi/dist/commands/deadcode": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/deadcode.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/deadcode", "key": "deadcode"}, "./node_modules/@umijs/preset-umi/dist/commands/version": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/version.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/version", "key": "version"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/page": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/page.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/page", "key": "generator:page"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/prettier.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier", "key": "generator:prettier"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig", "key": "generator:tsconfig"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/jest": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/jest.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/jest", "key": "generator:jest"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss", "key": "generator:tailwindcss"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/dva": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/dva.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/dva", "key": "generator:dva"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/component": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/component.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/component", "key": "generator:component"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/mock": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/mock.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/mock", "key": "generator:mock"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/cypress.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress", "key": "generator:cypress"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/api": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/api.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/api", "key": "generator:api"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/precommit.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit", "key": "generator:precommit"}, "./node_modules/@umijs/preset-umi/dist/commands/plugin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/plugin.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/plugin", "key": "command:plugin"}, "./node_modules/@umijs/preset-umi/dist/commands/verify-commit": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/verify-commit.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/verify-commit", "key": "verifyCommit"}, "./node_modules/@umijs/preset-umi/dist/commands/preview": {"config": {}, "time": {"hooks": {}, "register": 37}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/preview.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/preview", "key": "preview"}, "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu", "key": "mfsu-cli"}, "@umijs/plugin-run": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugin-run/dist/index.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "@umijs/plugin-run", "key": "run"}, "./node_modules/@umijs/plugins/dist/access": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/access.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/access", "key": "access"}, "./node_modules/@umijs/plugins/dist/analytics": {"config": {"onChange": "reload"}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/analytics.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/analytics", "key": "analytics"}, "./node_modules/@umijs/plugins/dist/antd": {"config": {}, "time": {"hooks": {"modifyConfig": [16], "modifyAppData": [0]}, "register": 7}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/antd.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/antd", "key": "antd"}, "./node_modules/@umijs/plugins/dist/dva": {"config": {}, "time": {"hooks": {}, "register": 12}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/dva.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/dva", "key": "dva"}, "./node_modules/@umijs/plugins/dist/initial-state": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/initial-state.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/initial-state", "key": "initialState"}, "./node_modules/@umijs/plugins/dist/layout": {"config": {"onChange": "regenerateTmpFiles"}, "time": {"hooks": {"modifyConfig": [0], "addLayouts": [1], "modifyAppData": [1]}, "register": 5}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/layout.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/layout", "key": "layout"}, "./node_modules/@umijs/plugins/dist/locale": {"config": {}, "time": {"hooks": {}, "register": 6}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/locale.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/locale", "key": "locale"}, "./node_modules/@umijs/plugins/dist/mf": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/mf.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/mf", "key": "mf"}, "./node_modules/@umijs/plugins/dist/model": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/model.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/model", "key": "model"}, "./node_modules/@umijs/plugins/dist/moment2dayjs": {"config": {}, "time": {"hooks": {"modifyConfig": [1]}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/moment2dayjs.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/moment2dayjs", "key": "moment2dayjs"}, "./node_modules/@umijs/plugins/dist/qiankun": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/qiankun.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/qiankun", "key": "qiankun"}, "./node_modules/@umijs/plugins/dist/qiankun/master": {"config": {}, "time": {"hooks": {}, "register": 4}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/qiankun/master.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/qiankun/master", "key": "<PERSON><PERSON><PERSON>n-master"}, "./node_modules/@umijs/plugins/dist/qiankun/slave": {"config": {}, "time": {"hooks": {}, "register": 5}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/qiankun/slave.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/qiankun/slave", "key": "qiankun-slave"}, "./node_modules/@umijs/plugins/dist/react-query": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/react-query.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/react-query", "key": "reactQuery"}, "./node_modules/@umijs/plugins/dist/request": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/request.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/request", "key": "request"}, "./node_modules/@umijs/plugins/dist/styled-components": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/styled-components.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/styled-components", "key": "styledComponents"}, "./node_modules/@umijs/plugins/dist/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/tailwindcss.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/tailwindcss", "key": "tailwindcss"}, "./node_modules/@umijs/plugins/dist/valtio": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/valtio.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/valtio", "key": "valtio"}, "./node_modules/@umijs/max/dist/plugins/maxAlias": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/max/dist/plugins/maxAlias.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/max/dist/plugins/maxAlias", "key": "max<PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/max/dist/plugins/maxAppData": {"config": {}, "time": {"hooks": {"modifyAppData": [1]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/max/dist/plugins/maxAppData.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/max/dist/plugins/maxAppData", "key": "maxAppData"}, "./node_modules/@umijs/max/dist/plugins/maxChecker": {"config": {}, "time": {"hooks": {"onCheckPkgJSON": [1]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/max/dist/plugins/maxChecker.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/max/dist/plugins/maxChecker", "key": "max<PERSON><PERSON><PERSON>"}, "./node_modules/umi-presets-pro/dist/features/proconfig": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/umi-presets-pro/dist/features/proconfig.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/umi-presets-pro/dist/features/proconfig", "key": "proconfig"}, "./node_modules/umi-presets-pro/dist/features/maxtabs": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/umi-presets-pro/dist/features/maxtabs.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/umi-presets-pro/dist/features/maxtabs", "key": "maxtabs"}, "@umijs/max-plugin-openapi": {"config": {}, "time": {"hooks": {}, "register": 972}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/max-plugin-openapi/dist/index.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "@umijs/max-plugin-openapi", "key": "openAPI"}, "./node_modules/@alita/plugins/dist/keepalive": {"config": {}, "time": {"hooks": {}, "register": 182}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@alita/plugins/dist/keepalive.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@alita/plugins/dist/keepalive", "key": "keepalive"}, "./node_modules/@alita/plugins/dist/tabs-layout": {"config": {"onChange": "regenerateTmpFiles"}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@alita/plugins/dist/tabs-layout.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@alita/plugins/dist/tabs-layout", "key": "tabsLayout"}, "@umijs/request-record": {"config": {"default": {"mock": {"outputDir": "./mock", "fileName": "requestRecord.mock.js", "usingRole": "default"}, "outputDir": "./types"}}, "time": {"hooks": {}, "register": 491}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/request-record/dist/cjs/index.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "@umijs/request-record", "key": "requestRecord"}, "./node_modules/@umijs/core/dist/service/generatePlugin": {"config": {}, "time": {"hooks": {}, "register": 6}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/core/dist/service/generatePlugin.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/core/dist/service/generatePlugin", "key": "generatePlugin"}}, "presets": [], "name": "dev", "args": {"_": []}, "userConfig": {"hash": true, "base": "/development_team/v2/", "publicPath": "/development_team/v2/", "outputPath": "../v2", "routes": [{"path": "/user", "layout": false, "routes": [{"name": "<PERSON><PERSON>", "path": "/user/login", "component": "./User/Login"}]}, {"key": "dispatch", "path": "/tenant/dashboard", "name": "Dispatch", "routes": [{"path": "/tenant/dashboard/edb", "name": "EDB", "component": "./tenant/dispatch/DispatchDashboard"}, {"path": "/tenant/dashboard/dispatch-list", "name": "Dispatch List", "component": "./tenant/dispatch/DispatchList"}, {"path": "/tenant/dashboard/radius-zone-mgmt", "name": "Zone Map", "component": "./tenant/dispatch/RadiusZone"}]}, {"key": "customer", "path": "/tenant/customer", "name": "Customer", "routes": [{"path": "/tenant/customer/list", "name": "Customer List", "component": "./tenant/Customer/CustomerList"}]}, {"path": "/tenant/invoice", "name": "Estimates & Invoices", "routes": [{"path": "/tenant/invoice/estimate/list", "name": "Estimates", "component": "./tenant/Estimate"}, {"path": "/tenant/invoice/list", "name": "Invoices", "component": "./tenant/Invoice"}, {"path": "/tenant/invoice/coupon", "name": "Coupons", "component": "./tenant/Coupon"}, {"path": "/tenant/invoice/customer", "name": "Top Customers", "component": "./tenant/Invoice/CustomerListPage"}]}, {"path": "/tenant/pricebook", "name": "Pricebook", "routes": [{"path": "/tenant/pricebook/service", "name": "Services", "external": true}, {"path": "/tenant/pricebook/service-category", "name": "Service Category", "external": true}]}, {"path": "tenant/employee", "name": "Employees", "routes": [{"path": "/tenant/employee/list", "name": "Employees List", "external": true}, {"path": "/tenant/employee/add", "name": "New Employees", "external": true}, {"path": "/tenant/employee/roles", "name": "Employee Roles List", "external": true}]}, {"path": "/tenant/driver", "name": "Driver / Device", "routes": [{"path": "/tenant/driver/device/list", "name": "Devices List", "component": "./tenant/driver/DeviceList"}]}, {"path": "/tenant/integration", "name": "Integration", "routes": [{"path": "/tenant/integration/twilio", "name": "<PERSON><PERSON><PERSON>", "external": true}]}, {"path": "/tenant/gps", "name": "GPS", "routes": [{"path": "/tenant/gps", "redirect": "/tenant/gps/job-board"}, {"path": "/tenant/gps/job-board", "name": "GPS Job Board", "component": "./tenant/gps/GpsJobBoard"}, {"path": "/tenant/gps/job-notes-management", "name": "Job Notes Management", "component": "./tenant/gps/GpsDispatchNoteList"}, {"path": "/tenant/gps/statuses", "name": "GPS Job Statuses", "component": "./tenant/gps/GpsDispatchStatusList"}]}, {"path": "/tenant/settings", "name": "Settings", "routes": [{"path": "/tenant/settings/system-message-template/edit", "name": "Automatic SMS Template", "external": true}, {"path": "/tenant/settings/dispatch/greetings", "name": "Automatic SMS Logs", "external": true}, {"path": "/tenant/settings/address", "name": "Tenant Addresses", "external": true}, {"path": "/tenant/settings/tenant-info", "name": "Tenant Info", "component": "./tenant/TenantInfoPage"}, {"path": "/tenant/settings/dispatch/text-note-template", "name": "Text Note Template", "component": "./tenant/TextNoteTemplate"}]}, {"path": "/tenant/call_board", "name": "Call Board", "external": true}, {"path": "/tenant/tenant_profile", "name": "My Profile", "external": true}, {"path": "/admin", "name": "Admin", "icon": "crown", "access": "canAdmin", "routes": [{"path": "/admin", "redirect": "/admin/sub-page"}, {"path": "/admin/sub-page", "name": "Sub page", "component": "./Admin"}]}, {"path": "/", "redirect": "/tenant/dashboard"}, {"path": "*", "layout": false, "component": "./404"}], "theme": {"root-entry-name": "variable"}, "ignoreMomentLocale": true, "fastRefresh": true, "model": {}, "initialState": {}, "title": "Fast Response", "layout": {"locale": false, "navTheme": "light", "colorPrimary": "#00796b", "layout": "top", "contentWidth": "Fluid", "fixedHeader": false, "fixSiderbar": true, "colorWeak": false, "title": "Fast Response", "pwa": true, "iconfontUrl": "", "token": {"header": {"colorHeaderTitle": "#ffffff", "colorBgHeader": "#00796b", "colorBgMenuItemSelected": "#00695c", "colorTextRightActionsItem": "#ffffff", "colorTextMenuSelected": "#ffffff"}, "sider": {"colorTextMenuTitle": "white"}, "pageContainer": {"paddingBlockPageContainerContent": 8, "paddingInlinePageContainerContent": 16}}, "favicon": "/v2/favicon.ico"}, "moment2dayjs": {"preset": "antd", "plugins": ["duration"]}, "locale": {"default": "en-US", "antd": true, "baseNavigator": false, "baseSeparator": "-"}, "antd": {"theme": {"components": {"Card": {"colorBorderSecondary": "#eeeeee"}, "Table": {"borderColor": "#dcdcdc", "rowSelectedBg": "rgb(216,241,238)"}, "Select": {"optionSelectedBg": "rgb(216,241,238)"}}, "token": {"colorBorder": "#dcdcdc"}}, "compact": false}, "request": {}, "access": {}, "headScripts": [{"src": "\\development_team\\v2\\scripts\\loading.js", "async": true}], "presets": ["umi-presets-pro"], "mako": {}, "esbuildMinifyIIFE": true, "requestRecord": {}, "define": {"API_URL": "http://localhost/development_team", "PUBLIC_PATH": "/development_team/v2/"}, "devtool": "eval"}, "mainConfigFile": "F:\\htdocs\\development_team\\frontend\\config\\config.ts", "config": {"routeLoader": {"moduleType": "esm"}, "mountElementId": "root", "history": {"type": "browser"}, "base": "/development_team/v2/", "svgr": {}, "publicPath": "/development_team/v2/", "mfsu": false, "ignoreMomentLocale": true, "externals": {}, "autoCSSModules": true, "alias": {"umi": "@@/exports", "react": "F:\\htdocs\\development_team\\frontend\\node_modules\\react", "react-dom": "F:\\htdocs\\development_team\\frontend\\node_modules\\react-dom", "react-router": "F:\\htdocs\\development_team\\frontend\\node_modules\\react-router", "react-router-dom": "F:\\htdocs\\development_team\\frontend\\node_modules\\react-router-dom", "@": "F:/htdocs/development_team/frontend/src", "@@": "F:/htdocs/development_team/frontend/src/.umi", "regenerator-runtime": "F:\\htdocs\\development_team\\frontend\\node_modules\\regenerator-runtime", "antd": "F:\\htdocs\\development_team\\frontend\\node_modules\\antd", "moment": "F:\\htdocs\\development_team\\frontend\\node_modules\\dayjs", "@umijs/max": "@@/exports"}, "requestRecord": {"mock": {"outputDir": "./mock", "fileName": "requestRecord.mock.js", "usingRole": "default"}, "outputDir": "./types"}, "hash": true, "outputPath": "../v2", "routes": [{"path": "/user", "layout": false, "routes": [{"name": "<PERSON><PERSON>", "path": "/user/login", "component": "./User/Login"}]}, {"key": "dispatch", "path": "/tenant/dashboard", "name": "Dispatch", "routes": [{"path": "/tenant/dashboard/edb", "name": "EDB", "component": "./tenant/dispatch/DispatchDashboard"}, {"path": "/tenant/dashboard/dispatch-list", "name": "Dispatch List", "component": "./tenant/dispatch/DispatchList"}, {"path": "/tenant/dashboard/radius-zone-mgmt", "name": "Zone Map", "component": "./tenant/dispatch/RadiusZone"}]}, {"key": "customer", "path": "/tenant/customer", "name": "Customer", "routes": [{"path": "/tenant/customer/list", "name": "Customer List", "component": "./tenant/Customer/CustomerList"}]}, {"path": "/tenant/invoice", "name": "Estimates & Invoices", "routes": [{"path": "/tenant/invoice/estimate/list", "name": "Estimates", "component": "./tenant/Estimate"}, {"path": "/tenant/invoice/list", "name": "Invoices", "component": "./tenant/Invoice"}, {"path": "/tenant/invoice/coupon", "name": "Coupons", "component": "./tenant/Coupon"}, {"path": "/tenant/invoice/customer", "name": "Top Customers", "component": "./tenant/Invoice/CustomerListPage"}]}, {"path": "/tenant/pricebook", "name": "Pricebook", "routes": [{"path": "/tenant/pricebook/service", "name": "Services", "external": true}, {"path": "/tenant/pricebook/service-category", "name": "Service Category", "external": true}]}, {"path": "tenant/employee", "name": "Employees", "routes": [{"path": "/tenant/employee/list", "name": "Employees List", "external": true}, {"path": "/tenant/employee/add", "name": "New Employees", "external": true}, {"path": "/tenant/employee/roles", "name": "Employee Roles List", "external": true}]}, {"path": "/tenant/driver", "name": "Driver / Device", "routes": [{"path": "/tenant/driver/device/list", "name": "Devices List", "component": "./tenant/driver/DeviceList"}]}, {"path": "/tenant/integration", "name": "Integration", "routes": [{"path": "/tenant/integration/twilio", "name": "<PERSON><PERSON><PERSON>", "external": true}]}, {"path": "/tenant/gps", "name": "GPS", "routes": [{"path": "/tenant/gps", "redirect": "/tenant/gps/job-board"}, {"path": "/tenant/gps/job-board", "name": "GPS Job Board", "component": "./tenant/gps/GpsJobBoard"}, {"path": "/tenant/gps/job-notes-management", "name": "Job Notes Management", "component": "./tenant/gps/GpsDispatchNoteList"}, {"path": "/tenant/gps/statuses", "name": "GPS Job Statuses", "component": "./tenant/gps/GpsDispatchStatusList"}]}, {"path": "/tenant/settings", "name": "Settings", "routes": [{"path": "/tenant/settings/system-message-template/edit", "name": "Automatic SMS Template", "external": true}, {"path": "/tenant/settings/dispatch/greetings", "name": "Automatic SMS Logs", "external": true}, {"path": "/tenant/settings/address", "name": "Tenant Addresses", "external": true}, {"path": "/tenant/settings/tenant-info", "name": "Tenant Info", "component": "./tenant/TenantInfoPage"}, {"path": "/tenant/settings/dispatch/text-note-template", "name": "Text Note Template", "component": "./tenant/TextNoteTemplate"}]}, {"path": "/tenant/call_board", "name": "Call Board", "external": true}, {"path": "/tenant/tenant_profile", "name": "My Profile", "external": true}, {"path": "/admin", "name": "Admin", "icon": "crown", "access": "canAdmin", "routes": [{"path": "/admin", "redirect": "/admin/sub-page"}, {"path": "/admin/sub-page", "name": "Sub page", "component": "./Admin"}]}, {"path": "/", "redirect": "/tenant/dashboard"}, {"path": "*", "layout": false, "component": "./404"}], "theme": {"blue-base": "#1890ff", "blue-1": "#e6f7ff", "blue-2": "#bae7ff", "blue-3": "#91d5ff", "blue-4": "#69c0ff", "blue-5": "#40a9ff", "blue-6": "#1890ff", "blue-7": "#096dd9", "blue-8": "#0050b3", "blue-9": "#003a8c", "blue-10": "#002766", "purple-base": "#722ed1", "purple-1": "#f9f0ff", "purple-2": "#efdbff", "purple-3": "#d3adf7", "purple-4": "#b37feb", "purple-5": "#9254de", "purple-6": "#722ed1", "purple-7": "#531dab", "purple-8": "#391085", "purple-9": "#22075e", "purple-10": "#120338", "cyan-base": "#13c2c2", "cyan-1": "#e6fffb", "cyan-2": "#b5f5ec", "cyan-3": "#87e8de", "cyan-4": "#5cdbd3", "cyan-5": "#36cfc9", "cyan-6": "#13c2c2", "cyan-7": "#08979c", "cyan-8": "#006d75", "cyan-9": "#00474f", "cyan-10": "#002329", "green-base": "#52c41a", "green-1": "#f6ffed", "green-2": "#d9f7be", "green-3": "#b7eb8f", "green-4": "#95de64", "green-5": "#73d13d", "green-6": "#52c41a", "green-7": "#389e0d", "green-8": "#237804", "green-9": "#135200", "green-10": "#092b00", "magenta-base": "#eb2f96", "magenta-1": "#fff0f6", "magenta-2": "#ffd6e7", "magenta-3": "#ffadd2", "magenta-4": "#ff85c0", "magenta-5": "#f759ab", "magenta-6": "#eb2f96", "magenta-7": "#c41d7f", "magenta-8": "#9e1068", "magenta-9": "#780650", "magenta-10": "#520339", "pink-base": "#eb2f96", "pink-1": "#fff0f6", "pink-2": "#ffd6e7", "pink-3": "#ffadd2", "pink-4": "#ff85c0", "pink-5": "#f759ab", "pink-6": "#eb2f96", "pink-7": "#c41d7f", "pink-8": "#9e1068", "pink-9": "#780650", "pink-10": "#520339", "red-base": "#f5222d", "red-1": "#fff1f0", "red-2": "#ffccc7", "red-3": "#ffa39e", "red-4": "#ff7875", "red-5": "#ff4d4f", "red-6": "#f5222d", "red-7": "#cf1322", "red-8": "#a8071a", "red-9": "#820014", "red-10": "#5c0011", "orange-base": "#fa8c16", "orange-1": "#fff7e6", "orange-2": "#ffe7ba", "orange-3": "#ffd591", "orange-4": "#ffc069", "orange-5": "#ffa940", "orange-6": "#fa8c16", "orange-7": "#d46b08", "orange-8": "#ad4e00", "orange-9": "#873800", "orange-10": "#612500", "yellow-base": "#fadb14", "yellow-1": "#feffe6", "yellow-2": "#ffffb8", "yellow-3": "#fffb8f", "yellow-4": "#fff566", "yellow-5": "#ffec3d", "yellow-6": "#fadb14", "yellow-7": "#d4b106", "yellow-8": "#ad8b00", "yellow-9": "#876800", "yellow-10": "#614700", "volcano-base": "#fa541c", "volcano-1": "#fff2e8", "volcano-2": "#ffd8bf", "volcano-3": "#ffbb96", "volcano-4": "#ff9c6e", "volcano-5": "#ff7a45", "volcano-6": "#fa541c", "volcano-7": "#d4380d", "volcano-8": "#ad2102", "volcano-9": "#871400", "volcano-10": "#610b00", "geekblue-base": "#2f54eb", "geekblue-1": "#f0f5ff", "geekblue-2": "#d6e4ff", "geekblue-3": "#adc6ff", "geekblue-4": "#85a5ff", "geekblue-5": "#597ef7", "geekblue-6": "#2f54eb", "geekblue-7": "#1d39c4", "geekblue-8": "#10239e", "geekblue-9": "#061178", "geekblue-10": "#030852", "lime-base": "#a0d911", "lime-1": "#fcffe6", "lime-2": "#f4ffb8", "lime-3": "#eaff8f", "lime-4": "#d3f261", "lime-5": "#bae637", "lime-6": "#a0d911", "lime-7": "#7cb305", "lime-8": "#5b8c00", "lime-9": "#3f6600", "lime-10": "#254000", "gold-base": "#faad14", "gold-1": "#fffbe6", "gold-2": "#fff1b8", "gold-3": "#ffe58f", "gold-4": "#ffd666", "gold-5": "#ffc53d", "gold-6": "#faad14", "gold-7": "#d48806", "gold-8": "#ad6800", "gold-9": "#874d00", "gold-10": "#613400", "preset-colors": "pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,", "theme": "default", "ant-prefix": "ant", "html-selector": "html", "primary-color": "#1890ff", "primary-color-hover": "#40a9ff", "primary-color-active": "#096dd9", "primary-color-outline": "rgba(24, 144, 255, 0.2)", "processing-color": "#1890ff", "info-color": "#1890ff", "info-color-deprecated-bg": "#e6f7ff", "info-color-deprecated-border": "#91d5ff", "success-color": "#52c41a", "success-color-hover": "#73d13d", "success-color-active": "#389e0d", "success-color-outline": "rgba(82, 196, 26, 0.2)", "success-color-deprecated-bg": "#f6ffed", "success-color-deprecated-border": "#b7eb8f", "warning-color": "#faad14", "warning-color-hover": "#ffc53d", "warning-color-active": "#d48806", "warning-color-outline": "rgba(250, 173, 20, 0.2)", "warning-color-deprecated-bg": "#fffbe6", "warning-color-deprecated-border": "#ffe58f", "error-color": "#ff4d4f", "error-color-hover": "#ff7875", "error-color-active": "#d9363e", "error-color-outline": "rgba(255, 77, 79, 0.2)", "error-color-deprecated-bg": "#fff2f0", "error-color-deprecated-border": "#ffccc7", "highlight-color": "#ff4d4f", "normal-color": "#d9d9d9", "white": "#fff", "black": "#000", "primary-1": "#e6f7ff", "primary-2": "#bae7ff", "primary-3": "#91d5ff", "primary-4": "#69c0ff", "primary-5": "#40a9ff", "primary-6": "#1890ff", "primary-7": "#096dd9", "primary-8": "#0050b3", "primary-9": "#003a8c", "primary-10": "#002766", "component-background": "#fff", "popover-background": "#fff", "popover-customize-border-color": "#f0f0f0", "font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "code-family": "'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', Menlo, Courier, monospace", "text-color": "rgba(0, 0, 0, 0.85)", "text-color-secondary": "rgba(0, 0, 0, 0.45)", "text-color-inverse": "#fff", "icon-color": "inherit", "icon-color-hover": "rgba(0, 0, 0, 0.75)", "heading-color": "rgba(0, 0, 0, 0.85)", "text-color-dark": "rgba(255, 255, 255, 0.85)", "text-color-secondary-dark": "rgba(255, 255, 255, 0.65)", "text-selection-bg": "#1890ff", "font-variant-base": "tabular-nums", "font-feature-settings-base": "tnum", "font-size-base": "14px", "font-size-lg": "16px", "font-size-sm": "12px", "heading-1-size": "38px", "heading-2-size": "30px", "heading-3-size": "24px", "heading-4-size": "20px", "heading-5-size": "16px", "line-height-base": "1.5715", "border-radius-base": "2px", "border-radius-sm": "2px", "control-border-radius": "2px", "arrow-border-radius": "2px", "padding-lg": "24px", "padding-md": "16px", "padding-sm": "12px", "padding-xs": "8px", "padding-xss": "4px", "control-padding-horizontal": "12px", "control-padding-horizontal-sm": "8px", "margin-lg": "24px", "margin-md": "16px", "margin-sm": "12px", "margin-xs": "8px", "margin-xss": "4px", "height-base": "32px", "height-lg": "40px", "height-sm": "24px", "item-active-bg": "#e6f7ff", "item-hover-bg": "#f5f5f5", "iconfont-css-prefix": "anticon", "link-color": "#1890ff", "link-hover-color": "#40a9ff", "link-active-color": "#096dd9", "link-decoration": "none", "link-hover-decoration": "none", "link-focus-decoration": "none", "link-focus-outline": "0", "ease-base-out": "cubic-bezier(0.7, 0.3, 0.1, 1)", "ease-base-in": "cubic-bezier(0.9, 0, 0.3, 0.7)", "ease-out": "cubic-bezier(0.215, 0.61, 0.355, 1)", "ease-in": "cubic-bezier(0.55, 0.055, 0.675, 0.19)", "ease-in-out": "cubic-bezier(0.645, 0.045, 0.355, 1)", "ease-out-back": "cubic-bezier(0.12, 0.4, 0.29, 1.46)", "ease-in-back": "cubic-bezier(0.71, -0.46, 0.88, 0.6)", "ease-in-out-back": "cubic-bezier(0.71, -0.46, 0.29, 1.46)", "ease-out-circ": "cubic-bezier(0.08, 0.82, 0.17, 1)", "ease-in-circ": "cubic-bezier(0.6, 0.04, 0.98, 0.34)", "ease-in-out-circ": "cubic-bezier(0.78, 0.14, 0.15, 0.86)", "ease-out-quint": "cubic-bezier(0.23, 1, 0.32, 1)", "ease-in-quint": "cubic-bezier(0.755, 0.05, 0.855, 0.06)", "ease-in-out-quint": "cubic-bezier(0.86, 0, 0.07, 1)", "border-color-base": "#d9d9d9", "border-color-split": "#f0f0f0", "border-color-inverse": "#fff", "border-width-base": "1px", "border-style-base": "solid", "outline-blur-size": "0", "outline-width": "2px", "outline-color": "#1890ff", "outline-fade": "20%", "background-color-light": "#fafafa", "background-color-base": "#f5f5f5", "disabled-color": "rgba(0, 0, 0, 0.25)", "disabled-bg": "#f5f5f5", "disabled-active-bg": "#e6e6e6", "disabled-color-dark": "rgba(255, 255, 255, 0.35)", "shadow-color": "rgba(0, 0, 0, 0.15)", "shadow-color-inverse": "#fff", "box-shadow-base": "0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)", "shadow-1-up": "0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-down": "0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-left": "-6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-right": "6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03)", "shadow-2": "0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)", "btn-font-weight": "400", "btn-border-radius-base": "2px", "btn-border-radius-sm": "2px", "btn-border-width": "1px", "btn-border-style": "solid", "btn-shadow": "0 2px 0 rgba(0, 0, 0, 0.015)", "btn-primary-shadow": "0 2px 0 rgba(0, 0, 0, 0.045)", "btn-text-shadow": "0 -1px 0 rgba(0, 0, 0, 0.12)", "btn-primary-color": "#fff", "btn-primary-bg": "#1890ff", "btn-default-color": "rgba(0, 0, 0, 0.85)", "btn-default-bg": "#fff", "btn-default-border": "#d9d9d9", "btn-danger-color": "#fff", "btn-danger-bg": "#ff4d4f", "btn-danger-border": "#ff4d4f", "btn-disable-color": "rgba(0, 0, 0, 0.25)", "btn-disable-bg": "#f5f5f5", "btn-disable-border": "#d9d9d9", "btn-default-ghost-color": "#fff", "btn-default-ghost-bg": "transparent", "btn-default-ghost-border": "#fff", "btn-font-size-lg": "16px", "btn-font-size-sm": "14px", "btn-padding-horizontal-base": "15px", "btn-padding-horizontal-lg": "15px", "btn-padding-horizontal-sm": "7px", "btn-height-base": "32px", "btn-height-lg": "40px", "btn-height-sm": "24px", "btn-line-height": "1.5715", "btn-circle-size": "32px", "btn-circle-size-lg": "40px", "btn-circle-size-sm": "24px", "btn-square-size": "32px", "btn-square-size-lg": "40px", "btn-square-size-sm": "24px", "btn-square-only-icon-size": "16px", "btn-square-only-icon-size-sm": "14px", "btn-square-only-icon-size-lg": "18px", "btn-group-border": "#40a9ff", "btn-link-hover-bg": "transparent", "btn-text-hover-bg": "rgba(0, 0, 0, 0.018)", "checkbox-size": "16px", "checkbox-color": "#1890ff", "checkbox-check-color": "#fff", "checkbox-check-bg": "#fff", "checkbox-border-width": "1px", "checkbox-border-radius": "2px", "checkbox-group-item-margin-right": "8px", "descriptions-bg": "#fafafa", "descriptions-title-margin-bottom": "20px", "descriptions-default-padding": "16px 24px", "descriptions-middle-padding": "12px 24px", "descriptions-small-padding": "8px 16px", "descriptions-item-padding-bottom": "16px", "descriptions-item-trailing-colon": "true", "descriptions-item-label-colon-margin-right": "8px", "descriptions-item-label-colon-margin-left": "2px", "descriptions-extra-color": "rgba(0, 0, 0, 0.85)", "divider-text-padding": "1em", "divider-orientation-margin": "5%", "divider-color": "rgba(0, 0, 0, 0.06)", "divider-vertical-gutter": "8px", "dropdown-selected-color": "#1890ff", "dropdown-menu-submenu-disabled-bg": "#fff", "dropdown-selected-bg": "#e6f7ff", "empty-font-size": "14px", "radio-size": "16px", "radio-top": "0.2em", "radio-border-width": "1px", "radio-dot-size": "8px", "radio-dot-color": "#1890ff", "radio-dot-disabled-color": "rgba(0, 0, 0, 0.2)", "radio-solid-checked-color": "#fff", "radio-button-bg": "#fff", "radio-button-checked-bg": "#fff", "radio-button-color": "rgba(0, 0, 0, 0.85)", "radio-button-hover-color": "#40a9ff", "radio-button-active-color": "#096dd9", "radio-button-padding-horizontal": "15px", "radio-disabled-button-checked-bg": "#e6e6e6", "radio-disabled-button-checked-color": "rgba(0, 0, 0, 0.25)", "radio-wrapper-margin-right": "8px", "screen-xs": "480px", "screen-xs-min": "480px", "screen-sm": "576px", "screen-sm-min": "576px", "screen-md": "768px", "screen-md-min": "768px", "screen-lg": "992px", "screen-lg-min": "992px", "screen-xl": "1200px", "screen-xl-min": "1200px", "screen-xxl": "1600px", "screen-xxl-min": "1600px", "screen-xs-max": "575px", "screen-sm-max": "767px", "screen-md-max": "991px", "screen-lg-max": "1199px", "screen-xl-max": "1599px", "grid-columns": "24", "layout-header-background": "#001529", "layout-header-height": "64px", "layout-header-padding": "0 50px", "layout-header-color": "rgba(0, 0, 0, 0.85)", "layout-footer-padding": "24px 50px", "layout-footer-background": "#f0f2f5", "layout-sider-background": "#001529", "layout-trigger-height": "48px", "layout-trigger-background": "#002140", "layout-trigger-color": "#fff", "layout-zero-trigger-width": "36px", "layout-zero-trigger-height": "42px", "layout-sider-background-light": "#fff", "layout-trigger-background-light": "#fff", "layout-trigger-color-light": "rgba(0, 0, 0, 0.85)", "zindex-badge": "auto", "zindex-table-fixed": "2", "zindex-affix": "10", "zindex-back-top": "10", "zindex-picker-panel": "10", "zindex-popup-close": "10", "zindex-modal": "1000", "zindex-modal-mask": "1000", "zindex-message": "1010", "zindex-notification": "1010", "zindex-popover": "1030", "zindex-dropdown": "1050", "zindex-picker": "1050", "zindex-popoconfirm": "1060", "zindex-tooltip": "1070", "zindex-image": "1080", "animation-duration-slow": "0.3s", "animation-duration-base": "0.2s", "animation-duration-fast": "0.1s", "collapse-panel-border-radius": "2px", "dropdown-menu-bg": "#fff", "dropdown-vertical-padding": "5px", "dropdown-edge-child-vertical-padding": "4px", "dropdown-font-size": "14px", "dropdown-line-height": "22px", "label-required-color": "#ff4d4f", "label-color": "rgba(0, 0, 0, 0.85)", "form-warning-input-bg": "#fff", "form-item-margin-bottom": "24px", "form-item-trailing-colon": "true", "form-vertical-label-padding": "0 0 8px", "form-vertical-label-margin": "0", "form-item-label-font-size": "14px", "form-item-label-height": "32px", "form-item-label-colon-margin-right": "8px", "form-item-label-colon-margin-left": "2px", "form-error-input-bg": "#fff", "input-height-base": "32px", "input-height-lg": "40px", "input-height-sm": "24px", "input-padding-horizontal": "11px", "input-padding-horizontal-base": "11px", "input-padding-horizontal-sm": "7px", "input-padding-horizontal-lg": "11px", "input-padding-vertical-base": "4px", "input-padding-vertical-sm": "0px", "input-padding-vertical-lg": "6.5px", "input-placeholder-color": "#bfbfbf", "input-color": "rgba(0, 0, 0, 0.85)", "input-icon-color": "rgba(0, 0, 0, 0.85)", "input-border-color": "#d9d9d9", "input-bg": "#fff", "input-number-hover-border-color": "#40a9ff", "input-number-handler-active-bg": "#f4f4f4", "input-number-handler-hover-bg": "#40a9ff", "input-number-handler-bg": "#fff", "input-number-handler-border-color": "#d9d9d9", "input-addon-bg": "#fafafa", "input-hover-border-color": "#40a9ff", "input-disabled-bg": "#f5f5f5", "input-outline-offset": "0 0", "input-icon-hover-color": "rgba(0, 0, 0, 0.85)", "input-disabled-color": "rgba(0, 0, 0, 0.25)", "mentions-dropdown-bg": "#fff", "mentions-dropdown-menu-item-hover-bg": "#fff", "select-border-color": "#d9d9d9", "select-item-selected-color": "rgba(0, 0, 0, 0.85)", "select-item-selected-font-weight": "600", "select-dropdown-bg": "#fff", "select-item-selected-bg": "#e6f7ff", "select-item-active-bg": "#f5f5f5", "select-dropdown-vertical-padding": "5px", "select-dropdown-font-size": "14px", "select-dropdown-line-height": "22px", "select-dropdown-height": "32px", "select-background": "#fff", "select-clear-background": "#fff", "select-selection-item-bg": "#f5f5f5", "select-selection-item-border-color": "#f0f0f0", "select-single-item-height-lg": "40px", "select-multiple-item-height": "24px", "select-multiple-item-height-lg": "32px", "select-multiple-item-spacing-half": "2px", "select-multiple-disabled-background": "#f5f5f5", "select-multiple-item-disabled-color": "#bfbfbf", "select-multiple-item-disabled-border-color": "#d9d9d9", "cascader-bg": "#fff", "cascader-item-selected-bg": "#e6f7ff", "cascader-menu-bg": "#fff", "cascader-menu-border-color-split": "#f0f0f0", "cascader-dropdown-vertical-padding": "5px", "cascader-dropdown-edge-child-vertical-padding": "4px", "cascader-dropdown-font-size": "14px", "cascader-dropdown-line-height": "22px", "anchor-bg": "transparent", "anchor-border-color": "#f0f0f0", "anchor-link-top": "4px", "anchor-link-left": "16px", "anchor-link-padding": "4px 0 4px 16px", "tooltip-max-width": "250px", "tooltip-color": "#fff", "tooltip-bg": "rgba(0, 0, 0, 0.75)", "tooltip-arrow-width": "11.3137085px", "tooltip-distance": "14.3137085px", "tooltip-arrow-color": "rgba(0, 0, 0, 0.75)", "tooltip-border-radius": "2px", "popover-bg": "#fff", "popover-color": "rgba(0, 0, 0, 0.85)", "popover-min-width": "177px", "popover-min-height": "32px", "popover-arrow-width": "11.3137085px", "popover-arrow-color": "#fff", "popover-arrow-outer-color": "#fff", "popover-distance": "15.3137085px", "popover-padding-horizontal": "16px", "modal-header-padding-vertical": "16px", "modal-header-padding-horizontal": "24px", "modal-header-bg": "#fff", "modal-header-padding": "16px 24px", "modal-header-border-width": "1px", "modal-header-border-style": "solid", "modal-header-title-line-height": "22px", "modal-header-title-font-size": "16px", "modal-header-border-color-split": "#f0f0f0", "modal-header-close-size": "54px", "modal-content-bg": "#fff", "modal-heading-color": "rgba(0, 0, 0, 0.85)", "modal-close-color": "rgba(0, 0, 0, 0.45)", "modal-footer-bg": "transparent", "modal-footer-border-color-split": "#f0f0f0", "modal-footer-border-style": "solid", "modal-footer-padding-vertical": "10px", "modal-footer-padding-horizontal": "16px", "modal-footer-border-width": "1px", "modal-mask-bg": "rgba(0, 0, 0, 0.45)", "modal-confirm-title-font-size": "16px", "modal-border-radius": "2px", "progress-default-color": "#1890ff", "progress-remaining-color": "#f5f5f5", "progress-info-text-color": "rgba(0, 0, 0, 0.85)", "progress-radius": "100px", "progress-steps-item-bg": "#f3f3f3", "progress-text-font-size": "1em", "progress-text-color": "rgba(0, 0, 0, 0.85)", "progress-circle-text-font-size": "1em", "menu-inline-toplevel-item-height": "40px", "menu-item-height": "40px", "menu-item-group-height": "1.5715", "menu-collapsed-width": "80px", "menu-bg": "#fff", "menu-popup-bg": "#fff", "menu-item-color": "rgba(0, 0, 0, 0.85)", "menu-inline-submenu-bg": "#fafafa", "menu-highlight-color": "#1890ff", "menu-highlight-danger-color": "#ff4d4f", "menu-item-active-bg": "#e6f7ff", "menu-item-active-danger-bg": "#fff1f0", "menu-item-active-border-width": "3px", "menu-item-group-title-color": "rgba(0, 0, 0, 0.45)", "menu-item-vertical-margin": "4px", "menu-item-font-size": "14px", "menu-item-boundary-margin": "8px", "menu-item-padding-horizontal": "20px", "menu-item-padding": "0 20px", "menu-horizontal-line-height": "46px", "menu-icon-margin-right": "10px", "menu-icon-size": "14px", "menu-icon-size-lg": "16px", "menu-item-group-title-font-size": "14px", "menu-dark-color": "rgba(255, 255, 255, 0.65)", "menu-dark-danger-color": "#ff4d4f", "menu-dark-bg": "#001529", "menu-dark-arrow-color": "#fff", "menu-dark-inline-submenu-bg": "#000c17", "menu-dark-highlight-color": "#fff", "menu-dark-item-active-bg": "#1890ff", "menu-dark-item-active-danger-bg": "#ff4d4f", "menu-dark-selected-item-icon-color": "#fff", "menu-dark-selected-item-text-color": "#fff", "menu-dark-item-hover-bg": "transparent", "spin-dot-size-sm": "14px", "spin-dot-size": "20px", "spin-dot-size-lg": "32px", "table-bg": "#fff", "table-header-bg": "#fafafa", "table-header-color": "rgba(0, 0, 0, 0.85)", "table-header-sort-bg": "#f5f5f5", "table-row-hover-bg": "#fafafa", "table-selected-row-color": "inherit", "table-selected-row-bg": "#e6f7ff", "table-selected-row-hover-bg": "#dcf4ff", "table-expanded-row-bg": "#fbfbfb", "table-padding-vertical": "16px", "table-padding-horizontal": "16px", "table-padding-vertical-md": "12px", "table-padding-horizontal-md": "8px", "table-padding-vertical-sm": "8px", "table-padding-horizontal-sm": "8px", "table-border-color": "#f0f0f0", "table-border-radius-base": "2px", "table-footer-bg": "#fafafa", "table-footer-color": "rgba(0, 0, 0, 0.85)", "table-header-bg-sm": "#fafafa", "table-font-size": "14px", "table-font-size-md": "14px", "table-font-size-sm": "14px", "table-header-cell-split-color": "rgba(0, 0, 0, 0.06)", "table-header-sort-active-bg": "rgba(0, 0, 0, 0.04)", "table-fixed-header-sort-active-bg": "#f5f5f5", "table-header-filter-active-bg": "rgba(0, 0, 0, 0.04)", "table-filter-btns-bg": "inherit", "table-filter-dropdown-bg": "#fff", "table-expand-icon-bg": "#fff", "table-selection-column-width": "32px", "table-sticky-scroll-bar-bg": "rgba(0, 0, 0, 0.35)", "table-sticky-scroll-bar-radius": "4px", "tag-border-radius": "2px", "tag-default-bg": "#fafafa", "tag-default-color": "rgba(0, 0, 0, 0.85)", "tag-font-size": "12px", "tag-line-height": "20px", "picker-bg": "#fff", "picker-basic-cell-hover-color": "#f5f5f5", "picker-basic-cell-active-with-range-color": "#e6f7ff", "picker-basic-cell-hover-with-range-color": "#cbe6ff", "picker-basic-cell-disabled-bg": "rgba(0, 0, 0, 0.04)", "picker-border-color": "#f0f0f0", "picker-date-hover-range-border-color": "#7ec1ff", "picker-date-hover-range-color": "#cbe6ff", "picker-time-panel-column-width": "56px", "picker-time-panel-column-height": "224px", "picker-time-panel-cell-height": "28px", "picker-panel-cell-height": "24px", "picker-panel-cell-width": "36px", "picker-text-height": "40px", "picker-panel-without-time-cell-height": "66px", "calendar-bg": "#fff", "calendar-input-bg": "#fff", "calendar-border-color": "#fff", "calendar-item-active-bg": "#e6f7ff", "calendar-column-active-bg": "rgba(230, 247, 255, 0.2)", "calendar-full-bg": "#fff", "calendar-full-panel-bg": "#fff", "carousel-dot-width": "16px", "carousel-dot-height": "3px", "carousel-dot-active-width": "24px", "badge-height": "20px", "badge-height-sm": "14px", "badge-dot-size": "6px", "badge-font-size": "12px", "badge-font-size-sm": "12px", "badge-font-weight": "normal", "badge-status-size": "6px", "badge-text-color": "#fff", "badge-color": "#ff4d4f", "rate-star-color": "#fadb14", "rate-star-bg": "#f0f0f0", "rate-star-size": "20px", "rate-star-hover-scale": "scale(1.1)", "card-head-color": "rgba(0, 0, 0, 0.85)", "card-head-background": "transparent", "card-head-font-size": "16px", "card-head-font-size-sm": "14px", "card-head-padding": "16px", "card-head-padding-sm": "8px", "card-head-height": "48px", "card-head-height-sm": "36px", "card-inner-head-padding": "12px", "card-padding-base": "24px", "card-padding-base-sm": "12px", "card-actions-background": "#fff", "card-actions-li-margin": "12px 0", "card-skeleton-bg": "#cfd8dc", "card-background": "#fff", "card-shadow": "0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09)", "card-radius": "2px", "card-head-tabs-margin-bottom": "-17px", "card-head-extra-color": "rgba(0, 0, 0, 0.85)", "comment-bg": "inherit", "comment-padding-base": "16px 0", "comment-nest-indent": "44px", "comment-font-size-base": "14px", "comment-font-size-sm": "12px", "comment-author-name-color": "rgba(0, 0, 0, 0.45)", "comment-author-time-color": "#ccc", "comment-action-color": "rgba(0, 0, 0, 0.45)", "comment-action-hover-color": "#595959", "comment-actions-margin-bottom": "inherit", "comment-actions-margin-top": "12px", "comment-content-detail-p-margin-bottom": "inherit", "tabs-card-head-background": "#fafafa", "tabs-card-height": "40px", "tabs-card-active-color": "#1890ff", "tabs-card-horizontal-padding": "8px 16px", "tabs-card-horizontal-padding-sm": "6px 16px", "tabs-card-horizontal-padding-lg": "7px 16px 6px", "tabs-title-font-size": "14px", "tabs-title-font-size-lg": "16px", "tabs-title-font-size-sm": "14px", "tabs-ink-bar-color": "#1890ff", "tabs-bar-margin": "0 0 16px 0", "tabs-horizontal-gutter": "32px", "tabs-horizontal-margin": "0 0 0 32px", "tabs-horizontal-margin-rtl": "0 0 0 32px", "tabs-horizontal-padding": "12px 0", "tabs-horizontal-padding-lg": "16px 0", "tabs-horizontal-padding-sm": "8px 0", "tabs-vertical-padding": "8px 24px", "tabs-vertical-margin": "16px 0 0 0", "tabs-scrolling-size": "32px", "tabs-highlight-color": "#1890ff", "tabs-hover-color": "#40a9ff", "tabs-active-color": "#096dd9", "tabs-card-gutter": "2px", "tabs-card-tab-active-border-top": "2px solid transparent", "back-top-color": "#fff", "back-top-bg": "rgba(0, 0, 0, 0.45)", "back-top-hover-bg": "rgba(0, 0, 0, 0.85)", "avatar-size-base": "32px", "avatar-size-lg": "40px", "avatar-size-sm": "24px", "avatar-font-size-base": "18px", "avatar-font-size-lg": "24px", "avatar-font-size-sm": "14px", "avatar-bg": "#ccc", "avatar-color": "#fff", "avatar-border-radius": "2px", "avatar-group-overlapping": "-8px", "avatar-group-space": "3px", "avatar-group-border-color": "#fff", "switch-height": "22px", "switch-sm-height": "16px", "switch-min-width": "44px", "switch-sm-min-width": "28px", "switch-disabled-opacity": "0.4", "switch-color": "#1890ff", "switch-bg": "#fff", "switch-shadow-color": "rgba(0, 35, 11, 0.2)", "switch-padding": "2px", "switch-inner-margin-min": "7px", "switch-inner-margin-max": "25px", "switch-sm-inner-margin-min": "5px", "switch-sm-inner-margin-max": "18px", "pagination-item-bg": "#fff", "pagination-item-size": "32px", "pagination-item-size-sm": "24px", "pagination-font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "pagination-font-weight-active": "500", "pagination-item-bg-active": "#fff", "pagination-item-link-bg": "#fff", "pagination-item-disabled-color-active": "rgba(0, 0, 0, 0.25)", "pagination-item-disabled-bg-active": "#e6e6e6", "pagination-item-input-bg": "#fff", "pagination-mini-options-size-changer-top": "0px", "page-header-padding": "24px", "page-header-padding-vertical": "16px", "page-header-padding-breadcrumb": "12px", "page-header-content-padding-vertical": "12px", "page-header-back-color": "#000", "page-header-ghost-bg": "inherit", "page-header-heading-title": "20px", "page-header-heading-sub-title": "14px", "page-header-tabs-tab-font-size": "16px", "breadcrumb-base-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-last-item-color": "rgba(0, 0, 0, 0.85)", "breadcrumb-font-size": "14px", "breadcrumb-icon-font-size": "14px", "breadcrumb-link-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-link-color-hover": "rgba(0, 0, 0, 0.85)", "breadcrumb-separator-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-separator-margin": "0 8px", "slider-margin": "10px 6px 10px", "slider-rail-background-color": "#f5f5f5", "slider-rail-background-color-hover": "#e1e1e1", "slider-track-background-color": "#91d5ff", "slider-track-background-color-hover": "#69c0ff", "slider-handle-border-width": "2px", "slider-handle-background-color": "#fff", "slider-handle-color": "#91d5ff", "slider-handle-color-hover": "#69c0ff", "slider-handle-color-focus": "#46a6ff", "slider-handle-color-focus-shadow": "rgba(24, 144, 255, 0.12)", "slider-handle-color-tooltip-open": "#1890ff", "slider-handle-size": "14px", "slider-handle-margin-top": "-5px", "slider-handle-shadow": "0", "slider-dot-border-color": "#f0f0f0", "slider-dot-border-color-active": "#8cc8ff", "slider-disabled-color": "rgba(0, 0, 0, 0.25)", "slider-disabled-background-color": "#fff", "tree-bg": "#fff", "tree-title-height": "24px", "tree-child-padding": "18px", "tree-directory-selected-color": "#fff", "tree-directory-selected-bg": "#1890ff", "tree-node-hover-bg": "#f5f5f5", "tree-node-selected-bg": "#bae7ff", "collapse-header-padding": "12px 16px", "collapse-header-padding-extra": "40px", "collapse-header-bg": "#fafafa", "collapse-content-padding": "16px", "collapse-content-bg": "#fff", "collapse-header-arrow-left": "16px", "skeleton-color": "rgba(190, 190, 190, 0.2)", "skeleton-to-color": "rgba(129, 129, 129, 0.24)", "skeleton-paragraph-margin-top": "28px", "skeleton-paragraph-li-margin-top": "16px", "skeleton-paragraph-li-height": "16px", "skeleton-title-height": "16px", "skeleton-title-paragraph-margin-top": "24px", "transfer-header-height": "40px", "transfer-item-height": "32px", "transfer-disabled-bg": "#f5f5f5", "transfer-list-height": "200px", "transfer-item-hover-bg": "#f5f5f5", "transfer-item-selected-hover-bg": "#dcf4ff", "transfer-item-padding-vertical": "6px", "transfer-list-search-icon-top": "12px", "message-notice-content-padding": "10px 16px", "message-notice-content-bg": "#fff", "wave-animation-width": "6px", "alert-success-border-color": "#b7eb8f", "alert-success-bg-color": "#f6ffed", "alert-success-icon-color": "#52c41a", "alert-info-border-color": "#91d5ff", "alert-info-bg-color": "#e6f7ff", "alert-info-icon-color": "#1890ff", "alert-warning-border-color": "#ffe58f", "alert-warning-bg-color": "#fffbe6", "alert-warning-icon-color": "#faad14", "alert-error-border-color": "#ffccc7", "alert-error-bg-color": "#fff2f0", "alert-error-icon-color": "#ff4d4f", "alert-message-color": "rgba(0, 0, 0, 0.85)", "alert-text-color": "rgba(0, 0, 0, 0.85)", "alert-close-color": "rgba(0, 0, 0, 0.45)", "alert-close-hover-color": "rgba(0, 0, 0, 0.75)", "alert-no-icon-padding-vertical": "8px", "alert-with-description-no-icon-padding-vertical": "15px", "alert-with-description-padding-vertical": "15px", "alert-with-description-padding": "15px 15px 15px 24px", "alert-icon-top": "12.0005px", "alert-with-description-icon-size": "24px", "list-header-background": "transparent", "list-footer-background": "transparent", "list-empty-text-padding": "16px", "list-item-padding": "12px 0", "list-item-padding-sm": "8px 16px", "list-item-padding-lg": "16px 24px", "list-item-meta-margin-bottom": "16px", "list-item-meta-avatar-margin-right": "16px", "list-item-meta-title-margin-bottom": "12px", "list-customize-card-bg": "#fff", "list-item-meta-description-font-size": "14px", "statistic-title-font-size": "14px", "statistic-content-font-size": "24px", "statistic-unit-font-size": "24px", "statistic-font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "drawer-header-padding": "16px 24px", "drawer-bg": "#fff", "drawer-footer-padding-vertical": "10px", "drawer-footer-padding-horizontal": "16px", "drawer-header-close-size": "56px", "drawer-title-font-size": "16px", "drawer-title-line-height": "22px", "timeline-width": "2px", "timeline-color": "#f0f0f0", "timeline-dot-border-width": "2px", "timeline-dot-color": "#1890ff", "timeline-dot-bg": "#fff", "timeline-item-padding-bottom": "20px", "typography-title-font-weight": "600", "typography-title-margin-top": "1.2em", "typography-title-margin-bottom": "0.5em", "upload-actions-color": "rgba(0, 0, 0, 0.45)", "process-tail-color": "#f0f0f0", "steps-nav-arrow-color": "rgba(0, 0, 0, 0.25)", "steps-background": "#fff", "steps-icon-size": "32px", "steps-icon-custom-size": "32px", "steps-icon-custom-top": "0px", "steps-icon-custom-font-size": "24px", "steps-icon-top": "-0.5px", "steps-icon-font-size": "16px", "steps-icon-margin": "0 8px 0 0", "steps-title-line-height": "32px", "steps-small-icon-size": "24px", "steps-small-icon-margin": "0 8px 0 0", "steps-dot-size": "8px", "steps-dot-top": "2px", "steps-current-dot-size": "10px", "steps-description-max-width": "140px", "steps-nav-content-max-width": "auto", "steps-vertical-icon-width": "16px", "steps-vertical-tail-width": "16px", "steps-vertical-tail-width-sm": "12px", "notification-bg": "#fff", "notification-padding-vertical": "16px", "notification-padding-horizontal": "24px", "result-title-font-size": "24px", "result-subtitle-font-size": "14px", "result-icon-font-size": "72px", "result-extra-margin": "24px 0 0 0", "image-size-base": "48px", "image-font-size-base": "24px", "image-bg": "#f5f5f5", "image-color": "#fff", "image-mask-font-size": "16px", "image-preview-operation-size": "18px", "image-preview-operation-color": "rgba(255, 255, 255, 0.85)", "image-preview-operation-disabled-color": "rgba(255, 255, 255, 0.25)", "segmented-bg": "rgba(0, 0, 0, 0.04)", "segmented-hover-bg": "rgba(0, 0, 0, 0.06)", "segmented-selected-bg": "#fff", "segmented-label-color": "rgba(0, 0, 0, 0.65)", "segmented-label-hover-color": "#262626", "root-entry-name": "variable"}, "fastRefresh": true, "model": {}, "initialState": {}, "title": "Fast Response", "layout": {"locale": false, "navTheme": "light", "colorPrimary": "#00796b", "layout": "top", "contentWidth": "Fluid", "fixedHeader": false, "fixSiderbar": true, "colorWeak": false, "title": "Fast Response", "pwa": true, "iconfontUrl": "", "token": {"header": {"colorHeaderTitle": "#ffffff", "colorBgHeader": "#00796b", "colorBgMenuItemSelected": "#00695c", "colorTextRightActionsItem": "#ffffff", "colorTextMenuSelected": "#ffffff"}, "sider": {"colorTextMenuTitle": "white"}, "pageContainer": {"paddingBlockPageContainerContent": 8, "paddingInlinePageContainerContent": 16}}, "favicon": "/v2/favicon.ico"}, "moment2dayjs": {"preset": "antd", "plugins": ["duration"]}, "locale": {"default": "en-US", "antd": true, "baseNavigator": false, "baseSeparator": "-"}, "antd": {"theme": {"components": {"Card": {"colorBorderSecondary": "#eeeeee"}, "Table": {"borderColor": "#dcdcdc", "rowSelectedBg": "rgb(216,241,238)"}, "Select": {"optionSelectedBg": "rgb(216,241,238)"}}, "token": {"colorBorder": "#dcdcdc"}}, "compact": false, "configProvider": {"theme": {"components": {"Card": {"colorBorderSecondary": "#eeeeee"}, "Table": {"borderColor": "#dcdcdc", "rowSelectedBg": "rgb(216,241,238)"}, "Select": {"optionSelectedBg": "rgb(216,241,238)"}}, "token": {"colorBorder": "#dcdcdc"}}}}, "request": {}, "access": {}, "headScripts": [{"src": "\\development_team\\v2\\scripts\\loading.js", "async": true}], "presets": ["umi-presets-pro"], "mako": {"plugins": [{"name": "UmiHtmlGenerationMako"}]}, "esbuildMinifyIIFE": true, "define": {"API_URL": "http://localhost/development_team", "PUBLIC_PATH": "/development_team/v2/", "ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION": "", "REACT_APP_ENV": "dev"}, "devtool": "eval", "targets": {"chrome": 80}, "hmrGuardian": false}, "routes": {"1": {"path": "/user", "layout": false, "id": "1", "absPath": "/user"}, "2": {"name": "<PERSON><PERSON>", "path": "/user/login", "file": "@/pages/User/Login/index.tsx", "parentId": "1", "id": "2", "absPath": "/user/login", "__content": "import { LockOutlined, UserOutlined } from '@ant-design/icons';\r\nimport { Alert, message } from 'antd';\r\nimport React, { useState } from 'react';\r\nimport { ProFormText, LoginForm } from '@ant-design/pro-form';\r\nimport { useModel, IRoute } from 'umi';\r\nimport Footer from '@/components/Footer';\r\nimport { login } from '@/services/app/login';\r\nimport styles from './index.less';\r\nimport { LS_TOKEN_NAME } from '@/constants';\r\nimport { __getRoot } from 'umi';\r\nimport { getQueryParamInUrl, urlFull } from '@/util';\r\n\r\nconst LoginMessage: React.FC<{\r\n  content: string;\r\n}> = ({ content }) => (\r\n  <Alert\r\n    style={{\r\n      marginBottom: 24,\r\n    }}\r\n    message={content}\r\n    type=\"error\"\r\n    showIcon\r\n  />\r\n);\r\n\r\nconst Login: React.FC<IRoute> = () => {\r\n  const [errorMsg, setErrorMsg] = useState('');\r\n  const { initialState, setInitialState } = useModel('@@initialState');\r\n\r\n  const fetchUserInfo = async () => {\r\n    const userInfo = await initialState?.fetchUserInfo?.();\r\n\r\n    if (userInfo) {\r\n      await setInitialState((s: any) => ({ ...s, currentUser: userInfo }));\r\n    }\r\n\r\n    return userInfo;\r\n  };\r\n\r\n  const handleSubmit = async (values: API.LoginParams) => {\r\n    try {\r\n      const res = await login({ ...values, user_type: 'tenant' });\r\n      console.log('Login Response:', res);\r\n\r\n      const defaultLoginSuccessMessage = 'Login successful!';\r\n      message.success(defaultLoginSuccessMessage);\r\n      localStorage.setItem(LS_TOKEN_NAME, res.token || '');\r\n\r\n      if (!location) return;\r\n\r\n      const redirect = getQueryParamInUrl(location.href, 'redirect');\r\n\r\n      /* if (userInfo?.role === UserRole.EDITOR) {\r\n        // location.href = redirect || '/item/ean-detail';\r\n      } else if (userInfo?.role === UserRole.WAREHOUSE) {\r\n        // location.href = redirect || '/orders/order-detail';\r\n      } else {\r\n        location.href = redirect || '/';\r\n    } */\r\n      location.href = redirect || '/';\r\n\r\n      return;\r\n    } catch (error: any) {\r\n      console.error(error);\r\n      setErrorMsg('Incorrect username or password');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={styles.container}>\r\n      <div className={styles.content}>\r\n        <LoginForm\r\n          logo={<img src={`${PUBLIC_PATH}logo.png`} />}\r\n          title=\"Fast Response\"\r\n          subTitle={' '}\r\n          initialValues={{\r\n            autoLogin: true,\r\n          }}\r\n          onFinish={async (values) => {\r\n            await handleSubmit(values as API.LoginParams);\r\n          }}\r\n          submitter={{ searchConfig: { submitText: 'Login' } }}\r\n        >\r\n          {errorMsg && <LoginMessage content={errorMsg} />}\r\n\r\n          <ProFormText\r\n            name=\"email\"\r\n            fieldProps={{\r\n              size: 'large',\r\n              prefix: <UserOutlined className={styles.prefixIcon} />,\r\n            }}\r\n            placeholder={'Email'}\r\n            rules={[\r\n              {\r\n                required: true,\r\n                message: 'Please input your email!',\r\n              },\r\n            ]}\r\n          />\r\n          <ProFormText.Password\r\n            name=\"password\"\r\n            fieldProps={{\r\n              size: 'large',\r\n              prefix: <LockOutlined className={styles.prefixIcon} />,\r\n            }}\r\n            placeholder={'Password'}\r\n            rules={[\r\n              {\r\n                required: true,\r\n                message: 'Please input your password!',\r\n              },\r\n            ]}\r\n          />\r\n        </LoginForm>\r\n      </div>\r\n      <Footer />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Login;\r\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/User/Login/index.tsx"}, "3": {"key": "dispatch", "path": "/tenant/dashboard", "name": "Dispatch", "parentId": "ant-design-pro-layout", "id": "3", "absPath": "/tenant/dashboard"}, "4": {"path": "/tenant/dashboard/edb", "name": "EDB", "file": "@/pages/tenant/dispatch/DispatchDashboard/index.tsx", "parentId": "3", "id": "4", "absPath": "/tenant/dashboard/edb", "__content": "import React, { useState, useCallback, useEffect, useMemo } from 'react';\r\nimport { PageContainer } from '@ant-design/pro-components';\r\nimport { Flex, Splitter, Typography, Card, theme, Spin, message as antdMessage, DatePicker, Select, Space, Button, Modal, Input, Form, Checkbox, Descriptions } from 'antd';\r\nimport { PlusOutlined, SettingOutlined } from '@ant-design/icons';\r\nimport {\r\n  DndContext,\r\n  DragEndEvent,\r\n  DragOverEvent,\r\n  DragStartEvent,\r\n  PointerSensor,\r\n  useSensor,\r\n  useSensors,\r\n  closestCenter,\r\n  DragOverlay,\r\n} from '@dnd-kit/core';\r\nimport {\r\n  SortableContext,\r\n  verticalListSortingStrategy,\r\n  horizontalListSortingStrategy,\r\n} from '@dnd-kit/sortable';\r\nimport { arrayMove } from '@dnd-kit/sortable';\r\nimport Column from \"./components/Column\";\r\nimport RouteMap from \"./components/RouteMap\";\r\nimport DispatchItem from \"./components/styles/item\";\r\nimport DispatchColorModal from './components/DispatchColorModal';\r\nimport { searchDispatch, assignDispatch, updateJob, updateJobSequence, updateDispatchTech } from \"@/services/app/tenant/dispatch/dispatch\";\r\nimport { getTenantEmployees } from \"@/services/app/employee/employee\";\r\nimport { getFullZoneList } from \"@/services/app/tenant/zone/zone\";\r\nimport { CalendarDayIcon, CalendarDayNumberIcon, CalendarWeekIcon, CalendarMonthIcon } from '@/components/ButlerIcon';\r\nimport { history, useModel } from '@umijs/max';\r\nimport dayjs from 'dayjs';\r\nimport timezone from 'dayjs/plugin/timezone';\r\nimport utc from 'dayjs/plugin/utc';\r\nimport { DispatchProvider } from './context/DispatchContext';\r\nimport { EmployeeProvider } from './context/EmployeeContext';\r\nimport { groupSimpleDispatchByZone, groupEmployeeDispatchByDateRange } from './reorder';\r\nimport app from '@/services/app';\r\nimport { addCustomView, updateCustomView, deleteCustomView, getCustomViewListByPage } from '@/services/app/tenant/dispatch/custom-view';\r\nimport { getJobComments, updateJobStatus } from '@/services/app/tenant/dispatch/dispatch';\r\nimport { getFormData } from '@/util';\r\nimport { DispatchContextMenuItems } from '@/constants';\r\nimport Detail from \"./components/Detail\";\r\n\r\ndayjs.extend(utc);\r\ndayjs.extend(timezone);\r\n\r\ninterface ColumnData {\r\n  id: string;\r\n  title: string;\r\n  type: 'Zone' | 'Date' | 'Dispatch';\r\n  items: Array<{\r\n    id: string;\r\n    dispatchId: number;\r\n    sortKey: string; // zone or date\r\n  }>;\r\n}\r\n\r\ninterface DispatchMap {\r\n  [key: string]: API.Dispatch;\r\n}\r\n\r\nconst dispatchIdMap: DispatchMap = {};\r\n\r\ntype ViewMode = 'daily' | 'weekly' | 'monthly';\r\n\r\nconst DispatchDashboard: React.FC = () => {\r\n  const [employees, setEmployees] = useState<API.Employee[]>([]);\r\n  const [allEmployees, setAllEmployees] = useState<API.Employee[]>([]);\r\n  const [allDispatches, setAllDispatches] = useState<API.Dispatch[]>([]);\r\n  const [columnData, setColumnData] = useState<ColumnData[]>([]);  \r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [message, setMessage] = useState('');\r\n  const [activeId, setActiveId] = useState<string | null>(null);\r\n  const [activeDispatch, setActiveDispatch] = useState<API.Dispatch | null>(null);  \r\n  const [allZones, setAllZones] = React.useState<API.DispatchZone[]>([]);\r\n  const [colorModalOpen, setColorModalOpen] = useState(false);\r\n  \r\n  // New state for view controls\r\n  const [viewMode, setViewMode] = useState<ViewMode>('daily');\r\n  const [selectedDate, setSelectedDate] = useState(dayjs());\r\n  const [dateRange, setDateRange] = useState<string[]>([]);\r\n  const [selectedEmployeeIds, setSelectedEmployeeIds] = useState<number[]>([]);\r\n  const [customViews, setCustomViews] = useState<API.CustomView[]>([]);\r\n  const [currentViewId, setCurrentViewId] = useState<string>('default');\r\n  const [viewModalOpen, setViewModalOpen] = useState(false);\r\n  const [editingView, setEditingView] = useState<API.CustomView | null>(null);\r\n  const [form] = Form.useForm();\r\n  \r\n  // Add these watchers to monitor form field changes\r\n  const selectedZones = Form.useWatch('zones', form) || [];\r\n  const selectedEmployees = Form.useWatch('employees', form) || [];\r\n\r\n  const { token } = theme.useToken();\r\n  const [antdMessageApi, contextHolder] = antdMessage.useMessage();\r\n  const [selectedDispatch, setSelectedDispatch] = useState<API.Dispatch>({});\r\n\r\n  const sensors = useSensors(\r\n    useSensor(PointerSensor, {\r\n      activationConstraint: {\r\n        distance: 8,\r\n      },\r\n    })\r\n  );\r\n\r\n  const successMsg = useCallback((msg: string) => {\r\n    antdMessageApi.open({\r\n      type: 'success',\r\n      content: msg,\r\n    });\r\n  }, [antdMessageApi]);\r\n\r\n  const errorMsg = useCallback((msg: string) => {\r\n    antdMessageApi.open({\r\n      type: 'error',\r\n      content: msg,\r\n    });\r\n  }, [antdMessageApi]);\r\n\r\n  const assignDispatchToEmployee = useCallback(async (data: API.DispatchAssignPayload) => {\r\n    try {\r\n      const result = await assignDispatch(data);\r\n      return result;\r\n    } catch (error) {\r\n      console.error('Error assigning dispatch:', error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const getInitialData = async () => {\r\n    setIsLoading(true);\r\n    setMessage(\"Loading dispatch data...\");\r\n    \r\n    try {\r\n      const [employeesData, dispatchesData, zoneData] = await Promise.all([\r\n        getTenantEmployees({\r\n          with: \"userProfile,tenantRoles\"\r\n        }),\r\n        searchDispatch({\r\n          employeeId: \"\",\r\n          startDate: \"\",\r\n          endDate: \"\",\r\n          date: \"\",\r\n          jobStatus: \"250,260,20,140,150\",\r\n          appointmentStatus: \"\",\r\n          with: 'dispatchItems.symptoms,customer,customer.customerPhones,addresses,addresses.customerGeolocation',\r\n          without: 'archived'\r\n        }),\r\n        getFullZoneList()\r\n      ]);\r\n\r\n      setAllZones(zoneData.data);\r\n      let officeColumnsPt: ColumnData[] = [];\r\n      let employeeColumnsPt: ColumnData[] = [];\r\n\r\n      if (dispatchesData) {\r\n        setAllDispatches(dispatchesData);\r\n        \r\n        // Create office columns - show zones with accepted jobs\r\n        const acceptedDispatches = dispatchesData.filter(d => \r\n          d.dispatch_status?.job_status === '260' // accepted jobs\r\n        );\r\n\r\n        const dispatchMap = groupSimpleDispatchByZone(acceptedDispatches, zoneData.data);\r\n        const zoneColumns: ColumnData[] = [];\r\n\r\n        // Create a column for each zone that has accepted jobs\r\n        Object.entries(dispatchMap.zone_dispatch_map).forEach(([zoneName, dispatches]) => {\r\n          if (dispatches.length > 0) {\r\n            const items: Array<{ id: string; dispatchId: number; sortKey: string }> = [];\r\n            \r\n            dispatches.forEach(dispatch => {\r\n              items.push({\r\n                id: `dispatch_${dispatch.id}`,\r\n                dispatchId: dispatch?.id || 0,\r\n                sortKey: zoneName\r\n              });\r\n              dispatchIdMap[dispatch?.id || \"unknown\"] = dispatch;\r\n            });\r\n\r\n            zoneColumns.push({\r\n              id: `office_column_${zoneName}`,\r\n              title: zoneName,\r\n              type: 'Zone' as const,\r\n              items\r\n            });\r\n          }\r\n        });\r\n\r\n        // Add special status columns (parked, unassigned)\r\n        const specialColumns: ColumnData[] = ['parked', 'unassigned'].map(status => {\r\n          const statusDispatches = dispatchesData.filter(d => {\r\n            if (status === 'unassigned') return !d.dispatch_user?.user_id && d.dispatch_status?.job_status !== '150' && d.dispatch_status?.job_status === '250';\r\n            if (status === 'parked') return d.dispatch_status?.parking_status === 'parked' && d.dispatch_status?.job_status === '150';\r\n            return false;\r\n          });\r\n          \r\n          const dispatchMap = groupSimpleDispatchByZone(statusDispatches, zoneData.data);\r\n          const items: Array<{ id: string; dispatchId: number; sortKey: string }> = [];\r\n          \r\n          Object.entries(dispatchMap.zone_dispatch_map).forEach(([zone, dispatches]) => {\r\n            dispatches.forEach(dispatch => {\r\n              items.push({\r\n                id: `dispatch_${dispatch.id}`,\r\n                dispatchId: dispatch?.id || 0,\r\n                sortKey: zone\r\n              });\r\n              dispatchIdMap[dispatch?.id || \"unknown\"] = dispatch;\r\n            });\r\n          });\r\n\r\n          return {\r\n            id: `office_column_${status}`,\r\n            title: status,\r\n            type: 'Zone' as const,\r\n            items\r\n          };\r\n        });\r\n\r\n        officeColumnsPt = [...specialColumns, ...zoneColumns];\r\n      }\r\n\r\n      if (employeesData) {\r\n        // Store all employees for filter options\r\n        setAllEmployees(employeesData);\r\n        \r\n        // Filter employees based on selection\r\n        const filteredEmployees = selectedEmployeeIds.length > 0 \r\n          ? employeesData.filter(emp => selectedEmployeeIds.includes(emp.id || 0))\r\n          : employeesData;\r\n\r\n        // Create employee columns with date range support\r\n        const employeeColumns: ColumnData[] = filteredEmployees.map(employee => {\r\n          const employeeDispatchMap = groupEmployeeDispatchByDateRange(dispatchesData, employee?.id || 0, dateRange);\r\n          \r\n          const items: Array<{ id: string; dispatchId: number; sortKey: string }> = [];\r\n          \r\n          // Add all dates in range, even if no dispatches\r\n          dateRange.forEach(date => {\r\n            const dispatches = employeeDispatchMap.employee_dispatch_map[date] || [];\r\n            dispatches.forEach(dispatch => {\r\n              items.push({\r\n                id: `dispatch_${dispatch.id}`,\r\n                dispatchId: dispatch.id || 0,\r\n                sortKey: date\r\n              });\r\n              dispatchIdMap[dispatch?.id || \"unknown\"] = dispatch;\r\n            });\r\n          });\r\n\r\n          return {\r\n            id: `field_column_${employee.id}`,\r\n            title: employee.user_name || \"Unknown\",\r\n            type: 'Date' as const,\r\n            items\r\n          };\r\n        });\r\n\r\n        employeeColumnsPt = employeeColumns;        \r\n        setEmployees(filteredEmployees);\r\n      }\r\n      setColumnData([...officeColumnsPt, ...employeeColumnsPt]);\r\n    } catch (error) {\r\n      errorMsg('Failed to load data');\r\n    } finally {\r\n      setIsLoading(false);\r\n      setMessage('');\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getInitialData();\r\n    \r\n    const handleRefresh = async () => {\r\n      await getInitialData();\r\n    };\r\n    \r\n    window.addEventListener('refreshDispatchEDB', handleRefresh);    \r\n    \r\n    return () => {\r\n      window.removeEventListener('refreshDispatchEDB', handleRefresh);\r\n    };\r\n  // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [dateRange, selectedEmployeeIds]);\r\n  \r\n  const handleDragStart = (event: DragStartEvent) => {    \r\n    const { active } = event;\r\n    setActiveId(active.id as string);\r\n        \r\n    // Find the active dispatch\r\n    const dispatchId = Number(active.id.toString().split('_')[1]);    \r\n    const foundDispatch = dispatchIdMap[dispatchId];    \r\n    setActiveDispatch(foundDispatch);\r\n  };\r\n\r\n  const handleDragEnd = async (event: DragEndEvent) => {\r\n    console.log('[DragEnd]', event);\r\n    const { active, over } = event;\r\n    setActiveId(null);\r\n    setActiveDispatch(null);\r\n\r\n    if (!over) return;\r\n\r\n    const activeId = active.id as string;\r\n    const overId = over.id as string;\r\n\r\n    // Handle column reordering\r\n    if (activeId.startsWith('office_column_') && overId.startsWith('office_column_')) {\r\n      const activeIndex = columnData.findIndex(col => col.id === activeId && col.type === 'Zone');\r\n      const overIndex = columnData.findIndex(col => col.id === overId && col.type === 'Zone');\r\n      \r\n      if (activeIndex !== -1 && overIndex !== -1 && activeIndex !== overIndex) {\r\n        const newColumnData = [...columnData];\r\n        const zoneColumns = newColumnData.filter(col => col.type === 'Zone');\r\n        const dateColumns = newColumnData.filter(col => col.type === 'Date');        \r\n        const reorderedZoneColumns = arrayMove(zoneColumns, activeIndex, overIndex);\r\n        setColumnData([...reorderedZoneColumns, ...dateColumns]);\r\n      }\r\n      return;\r\n    }\r\n\r\n    if (activeId.startsWith('field_column_') && overId.startsWith('field_column_')) {      \r\n      const activeIndex = employees.findIndex(emp => `field_column_${emp.id}` === activeId);\r\n      const overIndex = employees.findIndex(emp => `field_column_${emp.id}` === overId);\r\n      \r\n      if (activeIndex !== -1 && overIndex !== -1 && activeIndex !== overIndex) {\r\n        setEmployees(arrayMove(employees, activeIndex, overIndex));\r\n        const newColumnData = [...columnData];\r\n        const zoneColumns = newColumnData.filter(col => col.type === 'Zone');\r\n        const dateColumns = newColumnData.filter(col => col.type === 'Date');        \r\n        const reorderedDateColumns = arrayMove(dateColumns, activeIndex, overIndex);\r\n        setColumnData([...reorderedDateColumns, ...zoneColumns]);\r\n      }\r\n      return;\r\n    }\r\n    \r\n    // Handle dispatch assignment\r\n    if (activeId.startsWith('dispatch_')) {\r\n      \r\n      const dispatchId = Number(activeId.split('_')[1]);\r\n\r\n      const activeDataPos = active?.data?.current?.sortable?.index;\r\n      let activeSectionId = '';\r\n      for (let i=activeDataPos; i >=0; i--) {                    \r\n        const itemId = active?.data?.current?.sortable?.items[i] as string;          \r\n        if (itemId.startsWith('date_header_')) {            \r\n          activeSectionId = active?.data?.current?.sortable?.items[i];\r\n          break;\r\n        }  \r\n      }        \r\n      const [activeEmployeeId, activeDate] = activeSectionId.replaceAll('date_header_', '').split('_');\r\n      \r\n      // Check if dropping on employee\r\n      if (overId.startsWith('employee_')) {\r\n        const [, employeeId, appDate] = overId.split('_');\r\n        const dispatchData = dispatchIdMap[dispatchId];\r\n        \r\n        if (dispatchData?.dispatch_status?.job_status == '250') {\r\n          errorMsg('Please accept the job.');\r\n          return;\r\n        }\r\n        \r\n        const assignDispatchData: API.DispatchAssignPayload = {\r\n          job_id: dispatchId,\r\n          assigned_techs: employeeId,\r\n          appointment_set: appDate,\r\n          index: 0\r\n        };\r\n\r\n        setMessage(\"Assigning job to employee...\");        \r\n        setIsLoading(true);\r\n        \r\n        try {\r\n          const result = await assignDispatchToEmployee(assignDispatchData);\r\n          if (result?.success) {\r\n            await getInitialData();\r\n            successMsg('Job was assigned successfully.');\r\n          } else {\r\n            errorMsg(result?.errorMessage || 'Assignment failed');\r\n          }\r\n        } catch (error: any) {\r\n          errorMsg(error?.message || 'Assignment failed');\r\n        } finally {\r\n          setMessage('');\r\n          setIsLoading(false);\r\n        }\r\n      } else if (overId.startsWith('dispatch_')) {\r\n        const overDispatchId = Number(overId.split('_')[1]);\r\n        const overDataPos = over?.data?.current?.sortable?.index;\r\n        let overSectionId = '';\r\n\r\n        for (let i=overDataPos; i >=0; i--) {                    \r\n          const itemId = over?.data?.current?.sortable?.items[i] as string;          \r\n          if (itemId.startsWith('date_header_')) {            \r\n            overSectionId = over?.data?.current?.sortable?.items[i];\r\n            break;\r\n          }  \r\n        }        \r\n        const [overEmployeeId, overDate] = overSectionId.replaceAll('date_header_', '').split('_');\r\n        \r\n        if (activeEmployeeId && overEmployeeId) {\r\n          const Appts = allDispatches.filter(dispatch => \r\n            dispatch?.dispatch_user?.user_id == Number(overEmployeeId) &&\r\n            dispatch?.dispatch_appointment?.appointment_date == overDate)\r\n            .sort((a, b) => (a?.dispatch_appointment?.stop_sequence || 0) - (b?.dispatch_appointment?.stop_sequence || 0));\r\n          const dispatchSequenceData: API.DispatchSequenceUpdatePayload = {\r\n            sequence_updates: [],\r\n            user_id: Number(overEmployeeId),\r\n            appt_date: overDate\r\n          };\r\n          let stop_sequence = 1;\r\n          \r\n          Appts.forEach(appt => {\r\n            if (appt?.id == overDispatchId) {\r\n              dispatchSequenceData.sequence_updates?.push({\r\n                job_id: dispatchIdMap[dispatchId]?.id || 0,\r\n                stop_sequence: stop_sequence\r\n              });\r\n              stop_sequence++;\r\n\r\n              dispatchSequenceData.sequence_updates?.push({\r\n                job_id: appt?.id,\r\n                stop_sequence: stop_sequence\r\n              });\r\n              stop_sequence++;\r\n            } else if (appt?.id != dispatchId && appt?.id != overDispatchId) {\r\n              dispatchSequenceData.sequence_updates?.push({\r\n                job_id: appt?.id || 0,\r\n                stop_sequence: stop_sequence\r\n              });\r\n              stop_sequence++;\r\n            }\r\n          });\r\n\r\n          setMessage(\"Upadting job...\");        \r\n          setIsLoading(true);\r\n          \r\n          try {\r\n            const result = await updateJobSequence(dispatchSequenceData);\r\n            if (result?.success) {\r\n              await getInitialData();\r\n              successMsg('Job was updated successfully.');\r\n            } else {\r\n              errorMsg(result?.errorMessage || 'Update failed');\r\n            }\r\n          } catch (error: any) {\r\n            errorMsg(error?.message || 'Update failed');\r\n          } finally {\r\n            setMessage('');\r\n            setIsLoading(false);\r\n          }          \r\n\r\n        }        \r\n      }\r\n    }\r\n  }; \r\n  \r\n  // Generate date range based on view mode and selected date\r\n  useEffect(() => {\r\n    const generateDateRange = () => {\r\n      const dates: string[] = [];\r\n      const startDate = selectedDate.clone();\r\n      \r\n      switch (viewMode) {\r\n        case 'daily':\r\n          dates.push(startDate.format('YYYY-MM-DD'));\r\n          break;\r\n        case 'weekly': {\r\n          const weekStart = startDate.startOf('week');\r\n          for (let i = 0; i < 7; i++) {\r\n            dates.push(weekStart.clone().add(i, 'day').format('YYYY-MM-DD'));\r\n          }\r\n          break;\r\n        }\r\n        case 'monthly': {\r\n          const monthStart = startDate.startOf('month');\r\n          const monthEnd = startDate.endOf('month');\r\n          let current = monthStart.clone();\r\n          while (current.isSameOrBefore(monthEnd)) {\r\n            dates.push(current.format('YYYY-MM-DD'));\r\n            current = current.add(1, 'day');\r\n          }\r\n          break;\r\n        }\r\n      }\r\n      \r\n      setDateRange(dates);\r\n    };\r\n    \r\n    generateDateRange();\r\n  }, [viewMode, selectedDate]);\r\n\r\n  const applyView = (view: API.CustomView) => {\r\n    if (view.id === 'default') {\r\n      setSelectedEmployeeIds([]);\r\n    } else {\r\n      if ('selected_employee_ids' in view) {\r\n        let selectedIds = [];\r\n        if (Array.isArray(view.selected_employee_ids)) {\r\n          selectedIds = view.selected_employee_ids;\r\n        } else if (typeof view.selected_employee_ids === 'string') {\r\n          try {\r\n            selectedIds = JSON.parse(view.selected_employee_ids);\r\n          } catch (e) {\r\n            console.error('Failed to parse selected_employee_ids:', view.selected_employee_ids);\r\n            selectedIds = [];\r\n          }\r\n        }\r\n        setSelectedEmployeeIds(selectedIds);\r\n      } else {\r\n        setSelectedEmployeeIds(view.selected_employee_ids || []);\r\n      }\r\n    }\r\n  };\r\n\r\n  const loadCustomViews = async () => {\r\n    try {\r\n      const result = await getCustomViewListByPage({ current: 1, pageSize: 100 });\r\n      \r\n      if (result.success && result.data) {\r\n        setCustomViews(result.data);\r\n        \r\n        const defaultView = result.data.find((v: API.CustomView) => v.is_default);\r\n        if (defaultView) {\r\n          setCurrentViewId(defaultView.id?.toString() || 'default');\r\n          applyView(defaultView);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to load custom views:', error);\r\n    }\r\n  };\r\n  \r\n  \r\n\r\n  const handleViewChange = (viewId: string) => {\r\n    setCurrentViewId(viewId);\r\n    if (viewId === 'default') {\r\n      applyView({ id: 'default', name: 'Default', selected_zones: [], selected_employee_ids: [], is_default: false });\r\n    } else {\r\n      const view = customViews.find(v => v.id?.toString() === viewId);\r\n      if (view) {\r\n        applyView(view);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleSaveView = async (values: any) => {\r\n    try {\r\n      const viewData = {\r\n        name: values.name,\r\n        selected_zones: values.zones || [],\r\n        selected_employee_ids: values.employees || [],\r\n        is_default: values.isDefault || false\r\n      };\r\n\r\n      let result;\r\n      if (editingView) {\r\n        result = await updateCustomView(Number(editingView.id), viewData);\r\n      } else {\r\n        result = await addCustomView(viewData);\r\n      }\r\n\r\n      if (result.success) {\r\n        await loadCustomViews();\r\n        setViewModalOpen(false);\r\n        setEditingView(null);\r\n        form.resetFields();\r\n        antdMessageApi.success(editingView ? 'View updated successfully' : 'View created successfully');\r\n        \r\n        if (viewData.is_default && result.data) {\r\n          const newViewId = result.data.id?.toString() || 'default';\r\n          setCurrentViewId(newViewId);\r\n          applyView(result.data);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      antdMessageApi.error('Failed to save view');\r\n      console.error('Error saving view:', error);\r\n    }\r\n  };\r\n\r\n  const handleEditView = (viewId: string) => {\r\n    const view = customViews.find(v => v.id?.toString() === viewId);\r\n    if (!view) return;\r\n    \r\n    setEditingView(view);\r\n    \r\n    // Parse zones and employees data\r\n    const zones = Array.isArray(view.selected_zones) \r\n      ? view.selected_zones \r\n      : JSON.parse(view.selected_zones || '[]');\r\n    const employees = Array.isArray(view.selected_employee_ids) \r\n      ? view.selected_employee_ids \r\n      : JSON.parse(view.selected_employee_ids || '[]');\r\n    \r\n    // Populate form with existing view data\r\n    form.setFieldsValue({\r\n      name: view.name,\r\n      zones: zones,\r\n      employees: employees,\r\n      isDefault: view.is_default || false\r\n    });\r\n    \r\n    setViewModalOpen(true);\r\n  };\r\n\r\n  const handleDeleteView = async (viewId: string) => {\r\n    try {\r\n      const result = await deleteCustomView(viewId);\r\n      if (result.success) {\r\n        await loadCustomViews();\r\n        antdMessageApi.success('View deleted successfully');\r\n        \r\n        if (currentViewId === viewId) {\r\n          setCurrentViewId('default');\r\n          handleViewChange('default');\r\n        }\r\n      }\r\n    } catch (error) {\r\n      antdMessageApi.error('Failed to delete view');\r\n      console.error('Error deleting view:', error);\r\n    }\r\n  };\r\n\r\n  const openViewModal = (view?: API.CustomView) => {\r\n    setEditingView(view || null);\r\n    if (view) {\r\n      const zones = Array.isArray(view.selected_zones) \r\n        ? view.selected_zones \r\n        : JSON.parse(view.selected_zones || '[]');\r\n      const employees = Array.isArray(view.selected_employee_ids) \r\n        ? view.selected_employee_ids \r\n        : JSON.parse(view.selected_employee_ids || '[]');\r\n        \r\n      form.setFieldsValue({\r\n        name: view.name,\r\n        zones: zones,\r\n        employees: employees,\r\n        isDefault: view.is_default\r\n      });\r\n    } else {\r\n      form.resetFields();\r\n    }\r\n    setViewModalOpen(true);\r\n  };\r\n\r\n  // Filter zones and employees based on current view\r\n  const getFilteredColumnData = () => {    \r\n    if (currentViewId === 'default') {      \r\n      return columnData;\r\n    }\r\n    \r\n    const currentView = customViews.find(v => v.id?.toString() === currentViewId);    \r\n    if (!currentView) {\r\n      return columnData;\r\n    }\r\n\r\n    // Always include parked and unassigned columns\r\n    const specialColumns = columnData.filter(col => \r\n      col.title.toLowerCase().includes('parked') || \r\n      col.title.toLowerCase().includes('unassigned')\r\n    );\r\n\r\n    // Filter zone columns based on selected zones\r\n    const zoneColumns = columnData.filter(col => \r\n      col.type === 'Zone' && \r\n      !col.title.toLowerCase().includes('parked') && \r\n      !col.title.toLowerCase().includes('unassigned') &&\r\n      (currentView?.selected_zones?.length === 0 || currentView?.selected_zones?.includes(col.title))\r\n    );\r\n\r\n    // Filter employee columns based on selected employees\r\n    const employeeColumns = columnData.filter(col => {\r\n      if (col.type !== 'Date') return false;\r\n          \r\n      const employeeId = parseInt(col.id.replace('field_column_', ''));\r\n      \r\n      return currentView?.selected_employee_ids?.length === 0 || \r\n             currentView?.selected_employee_ids?.includes(employeeId);\r\n    });\r\n    \r\n    return [...specialColumns, ...zoneColumns, ...employeeColumns];\r\n  };\r\n\r\n  // Load custom views from database\r\n  useEffect(() => {\r\n    loadCustomViews();\r\n\r\n    const handleShowDetails = (event: any) => {      \r\n      setSelectedDispatch(event.detail.dispatch);      \r\n    };\r\n\r\n    window.addEventListener('showDispatchDetails', handleShowDetails);\r\n    return () => window.removeEventListener('showDispatchDetails', handleShowDetails);\r\n  // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  // Update selectedDispatch when allDispatches changes\r\n  useEffect(() => {\r\n    if (selectedDispatch?.id && allDispatches.length > 0) {\r\n      const updatedDispatch = allDispatches.find(d => d.id === selectedDispatch.id);\r\n      if (updatedDispatch) {\r\n        setSelectedDispatch(updatedDispatch);\r\n      }\r\n    }\r\n  // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [allDispatches]);\r\n\r\n  return (\r\n    <PageContainer>\r\n      {contextHolder}\r\n      <Spin \r\n        tip={message} \r\n        spinning={isLoading}\r\n      >\r\n        {/* View Controls */}\r\n        <Card style={{ marginBottom: 16 }}>\r\n          <Space size=\"large\" wrap>\r\n            <Space>\r\n              <span>Custom View:</span>\r\n              <Select\r\n                value={currentViewId}\r\n                onChange={handleViewChange}\r\n                style={{ minWidth: 150 }}\r\n                options={[\r\n                  { label: 'Default View', value: 'default' },\r\n                  ...customViews.map(view => ({\r\n                    label: `${view.name}${view.is_default ? ' (Default)' : ''}`,\r\n                    value: String(view.id)\r\n                  }))\r\n                ]}\r\n              />\r\n              <Button \r\n                icon={<PlusOutlined />} \r\n                onClick={() => openViewModal()}\r\n              >\r\n                New View\r\n              </Button>\r\n              {currentViewId !== 'default' && (\r\n                <Button\r\n                  size=\"small\"\r\n                  icon={<SettingOutlined />}\r\n                  onClick={() => handleEditView(currentViewId)}\r\n                >\r\n                  Edit\r\n                </Button>\r\n              )}\r\n            </Space>\r\n            \r\n            <Space>\r\n              <span>View Mode:</span>\r\n              <Select\r\n                value={viewMode}\r\n                onChange={setViewMode}\r\n                style={{ minWidth: 100 }}\r\n                options={[\r\n                  { label: 'Daily', value: 'daily' },\r\n                  { label: 'Weekly', value: 'weekly' },\r\n                  { label: 'Monthly', value: 'monthly' }\r\n                ]}\r\n              />\r\n            </Space>\r\n\r\n            <Space>\r\n              <span>Select {viewMode === 'daily' ? 'Date' : viewMode === 'weekly' ? 'Week' : 'Month'}:</span>\r\n              <DatePicker\r\n                value={selectedDate}\r\n                onChange={(date) => date && setSelectedDate(date)}\r\n                picker={viewMode === 'monthly' ? 'month' : viewMode === 'weekly' ? 'week' : 'date'}\r\n                format={viewMode === 'monthly' ? 'YYYY-MM' : viewMode === 'weekly' ? 'YYYY-[W]WW' : 'YYYY-MM-DD'}\r\n              />\r\n            </Space>\r\n\r\n            <Space>\r\n              <span>Employees:</span>\r\n              <Select\r\n                mode=\"multiple\"\r\n                placeholder=\"Select employees\"\r\n                style={{ minWidth: 200 }}\r\n                value={selectedEmployeeIds}\r\n                onChange={setSelectedEmployeeIds}\r\n                showSearch\r\n                filterOption={(input, option) =>\r\n                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())\r\n                }\r\n                options={allEmployees.map(emp => ({\r\n                  label: emp.user_name || \"Unknown\",\r\n                  value: emp.id || 0\r\n                }))}\r\n                allowClear\r\n                maxTagCount=\"responsive\"\r\n                dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}\r\n              />\r\n            </Space>\r\n            \r\n            <Button onClick={getInitialData} type=\"primary\">\r\n              Refresh\r\n            </Button>\r\n            <Button onClick={() => setColorModalOpen(true)}>\r\n              Customize Status Colors\r\n            </Button>\r\n          </Space>\r\n        </Card>\r\n\r\n        <EmployeeProvider employees={employees}>\r\n        <DispatchProvider dispatches={allDispatches}>\r\n          <DndContext\r\n            sensors={sensors}\r\n            collisionDetection={closestCenter}\r\n            onDragStart={handleDragStart}\r\n            onDragEnd={handleDragEnd}\r\n          >\r\n            <Splitter style={{ height: '600px', overflow: 'hidden' }}>\r\n              <Splitter.Panel collapsible>\r\n                <SortableContext items={getFilteredColumnData().filter(col => col.type === 'Zone').map(col => col.id)} strategy={horizontalListSortingStrategy}>\r\n                  <div style={{ backgroundColor: token.colorBgLayout, minHeight: '300px', display: 'inline-flex' }}>\r\n                    {getFilteredColumnData().filter(col => col.type === 'Zone').map((column, index) => (\r\n                      <Column\r\n                        key={column.id}\r\n                        id={column.id}\r\n                        index={index}\r\n                        title={column.title}\r\n                        columnData={column}\r\n                      />\r\n                    ))}\r\n                  </div>\r\n                </SortableContext>\r\n              </Splitter.Panel>\r\n              <Splitter.Panel>\r\n                <SortableContext items={getFilteredColumnData().filter(col => col.type === 'Date').map(col => col.id)} strategy={horizontalListSortingStrategy}>\r\n                  <div style={{ backgroundColor: token.colorBgLayout, minHeight: '300px', display: 'inline-flex' }}>\r\n                    {getFilteredColumnData().filter(col => col.type === 'Date').map((column, index) => (\r\n                      <Column\r\n                        key={column.id}\r\n                        id={column.id}\r\n                        index={index}\r\n                        title={column.title}\r\n                        columnData={column}\r\n                        dateRange={dateRange}\r\n                        viewMode={viewMode}\r\n                      />\r\n                    ))}\r\n                  </div>\r\n                </SortableContext>\r\n              </Splitter.Panel>\r\n              <Splitter.Panel collapsible>\r\n                <RouteMap dispatchData={allDispatches} />\r\n              </Splitter.Panel>\r\n            </Splitter>\r\n            <DragOverlay>\r\n              {activeDispatch ? (\r\n                <DispatchItem\r\n                  id={activeId || ''}                  \r\n                  dispatch={activeDispatch}\r\n                  isDragging={true}\r\n                  isGroupedOver={false}\r\n                  isClone={true}\r\n                />\r\n              ) : null}\r\n            </DragOverlay>\r\n          </DndContext>\r\n        </DispatchProvider>\r\n        </EmployeeProvider>\r\n      </Spin>\r\n\r\n      {/* Static Details Section */}\r\n      {selectedDispatch && selectedDispatch.id && (\r\n        <Detail \r\n          selectedDispatch={selectedDispatch}\r\n          onClose={() => setSelectedDispatch({})}\r\n        />\r\n      )}\r\n\r\n      {/* Custom View Modal */}\r\n      <Modal\r\n        title={editingView ? 'Edit Custom View' : 'Create Custom View'}\r\n        open={viewModalOpen}\r\n        onCancel={() => {\r\n          setViewModalOpen(false);\r\n          setEditingView(null);\r\n          form.resetFields();\r\n        }}\r\n        footer={null}\r\n        width={800}\r\n      >\r\n        <Form\r\n          form={form}\r\n          layout=\"vertical\"\r\n          onFinish={handleSaveView}\r\n        >\r\n          <Form.Item\r\n            name=\"name\"\r\n            label=\"View Name\"\r\n            rules={[{ required: true, message: 'Please enter view name' }]}\r\n          >\r\n            <Input placeholder=\"Enter view name\" />\r\n          </Form.Item>\r\n\r\n          <div style={{ display: 'flex', gap: '24px' }}>\r\n            {/* Zones Section */}\r\n            <div style={{ flex: 1 }}>\r\n              <Form.Item\r\n                name=\"zones\"\r\n                label=\"Select Zones (leave empty for all zones)\"\r\n              >\r\n                <div>\r\n                  <div style={{ marginBottom: '8px' }}>\r\n                    <Select\r\n                      placeholder=\"Select a zone to add\"\r\n                      style={{ width: '100%' }}\r\n                      value={undefined}\r\n                      onChange={(value) => {\r\n                        if (value) {\r\n                          const currentZones = selectedZones;\r\n                          if (!currentZones.includes(value)) {\r\n                            form.setFieldValue('zones', [...currentZones, value]);\r\n                          }\r\n                        }\r\n                      }}\r\n                      options={allZones\r\n                        .map(zone => ({ label: zone.zone_name, value: zone.zone_name }))\r\n                        .filter(option => !selectedZones.includes(option.value))}\r\n                      showSearch\r\n                      filterOption={(input, option) =>\r\n                        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())\r\n                      }\r\n                    />\r\n                  </div>\r\n                  <div \r\n                    style={{ \r\n                      border: '1px solid #d9d9d9', \r\n                      borderRadius: '6px', \r\n                      minHeight: '200px', \r\n                      maxHeight: '200px', \r\n                      overflow: 'auto',\r\n                      padding: '8px'\r\n                    }}\r\n                  >\r\n                    {selectedZones.length === 0 ? (\r\n                      <div style={{ color: '#999', textAlign: 'center', padding: '20px' }}>\r\n                        No zones selected\r\n                      </div>\r\n                    ) : (\r\n                      selectedZones.map((zone: string) => (\r\n                        <div \r\n                          key={zone}\r\n                          style={{ \r\n                            display: 'flex', \r\n                            justifyContent: 'space-between', \r\n                            alignItems: 'center',\r\n                            padding: '8px',\r\n                            marginBottom: '4px',\r\n                            backgroundColor: '#f5f5f5',\r\n                            borderRadius: '4px'\r\n                          }}\r\n                        >\r\n                          <span>{zone}</span>\r\n                          <Button\r\n                            type=\"text\"\r\n                            size=\"small\"\r\n                            danger\r\n                            onClick={() => {\r\n                              form.setFieldValue('zones', selectedZones.filter((z: string) => z !== zone));\r\n                            }}\r\n                          >\r\n                            Remove\r\n                          </Button>\r\n                        </div>\r\n                      ))\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </Form.Item>\r\n            </div>\r\n\r\n            {/* Employees Section */}\r\n            <div style={{ flex: 1 }}>\r\n              <Form.Item\r\n                name=\"employees\"\r\n                label=\"Select Employees (leave empty for all employees)\"\r\n              >\r\n                <div>\r\n                  <div style={{ marginBottom: '8px' }}>\r\n                    <Select\r\n                      placeholder=\"Select an employee to add\"\r\n                      style={{ width: '100%' }}\r\n                      value={undefined}\r\n                      onChange={(value) => {\r\n                        if (value) {\r\n                          const currentEmployees = selectedEmployees;\r\n                          if (!currentEmployees.includes(value)) {\r\n                            form.setFieldValue('employees', [...currentEmployees, value]);\r\n                          }\r\n                        }\r\n                      }}\r\n                      options={allEmployees\r\n                        .map(emp => ({\r\n                          label: emp.user_name || \"Unknown\",\r\n                          value: emp.id || 0\r\n                        }))\r\n                        .filter(option => !selectedEmployees.includes(option.value))}\r\n                      showSearch\r\n                      filterOption={(input, option) =>\r\n                        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())\r\n                      }\r\n                    />\r\n                  </div>\r\n                  <div \r\n                    style={{ \r\n                      border: '1px solid #d9d9d9', \r\n                      borderRadius: '6px', \r\n                      minHeight: '200px', \r\n                      maxHeight: '200px', \r\n                      overflow: 'auto',\r\n                      padding: '8px'\r\n                    }}\r\n                  >\r\n                    {selectedEmployees.length === 0 ? (\r\n                      <div style={{ color: '#999', textAlign: 'center', padding: '20px' }}>\r\n                        No employees selected\r\n                      </div>\r\n                    ) : (\r\n                      selectedEmployees.map((empId: number) => {\r\n                        const employee = allEmployees.find(emp => emp.id === empId);\r\n                        return (\r\n                          <div \r\n                            key={empId}\r\n                            style={{ \r\n                              display: 'flex', \r\n                              justifyContent: 'space-between', \r\n                              alignItems: 'center',\r\n                              padding: '8px',\r\n                              marginBottom: '4px',\r\n                              backgroundColor: '#f5f5f5',\r\n                              borderRadius: '4px'\r\n                            }}\r\n                          >\r\n                            <span>{employee?.user_name || \"Unknown\"}</span>\r\n                            <Button\r\n                              type=\"text\"\r\n                              size=\"small\"\r\n                              danger\r\n                              onClick={() => {\r\n                                form.setFieldValue('employees', selectedEmployees.filter((id: number) => id !== empId));\r\n                              }}\r\n                            >\r\n                              Remove\r\n                            </Button>\r\n                          </div>\r\n                        );\r\n                      })\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </Form.Item>\r\n            </div>\r\n          </div>\r\n\r\n          <Form.Item name=\"isDefault\" valuePropName=\"checked\">\r\n            <Checkbox>Set as default view</Checkbox>\r\n          </Form.Item>\r\n\r\n          <Form.Item>\r\n            <Space>\r\n              <Button type=\"primary\" htmlType=\"submit\">\r\n                {editingView ? 'Update View' : 'Create View'}\r\n              </Button>\r\n              <Button onClick={() => {\r\n                setViewModalOpen(false);\r\n                setEditingView(null);\r\n                form.resetFields();\r\n              }}>\r\n                Cancel\r\n              </Button>\r\n              {editingView && (\r\n                <Button \r\n                  danger \r\n                  onClick={() => {\r\n                    Modal.confirm({\r\n                      title: 'Delete View',\r\n                      content: 'Are you sure you want to delete this view?',\r\n                      onOk: () => handleDeleteView(String(editingView.id))\r\n                    });\r\n                  }}\r\n                >\r\n                  Delete View\r\n                </Button>\r\n              )}\r\n            </Space>\r\n          </Form.Item>\r\n        </Form>\r\n      </Modal>\r\n\r\n      <DispatchColorModal \r\n        open={colorModalOpen} \r\n        onClose={() => setColorModalOpen(false)} \r\n      />\r\n    </PageContainer>\r\n  );\r\n}\r\n\r\nexport default DispatchDashboard;\r\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/tenant/dispatch/DispatchDashboard/index.tsx"}, "5": {"path": "/tenant/dashboard/dispatch-list", "name": "Dispatch List", "file": "@/pages/tenant/dispatch/DispatchList/index.tsx", "parentId": "3", "id": "5", "absPath": "/tenant/dashboard/dispatch-list", "__content": "import React, { useRef, useState } from 'react';\r\nimport { useModel } from '@umijs/max';\r\nimport { \r\n  Button,\r\n  Card,\r\n  theme,\r\n  Space,\r\n  Table,\r\n  Tag,\r\n  TagProps,\r\n  Typography\r\n} from 'antd';\r\nimport {\r\n  ActionType,\r\n  PageContainer,\r\n  ProColumns,\r\n  ProForm,\r\n  ProFormDigit,\r\n  ProFormInstance,\r\n  ProFormSelect,\r\n  ProFormText,\r\n  ProTable,\r\n} from \"@ant-design/pro-components\";\r\nimport { \r\n  EditOutlined, \r\n  FilePdfOutlined, \r\n  PlusOutlined, \r\n  SendOutlined,\r\n  CheckOutlined\r\n} from \"@ant-design/icons\";\r\nimport Util, { nf2, sn } from \"@/util\";\r\nimport { DEFAULT_PER_PAGE_PAGINATION, DispatchStatus, DispatchStatusKv } from \"@/constants\";\r\nimport { getDispatchListByPage, acceptOrDeclineDispatch } from \"@/services/app/tenant/dispatch/dispatch\";\r\nimport { message } from 'antd';\r\n\r\ntype RecordType = API.Dispatch;\r\n\r\nexport const DispatchStatusComp: React.FC<{ status?: DispatchStatus | string }> = ({ status }) => {\r\n  let color: TagProps[\"color\"] = \"default\";\r\n  switch (status) {\r\n    case DispatchStatus.ASSIGNED:\r\n      color = \"blue-inverse\";\r\n      break;\r\n    case DispatchStatus.ACCEPTED:\r\n      color = \"cyan\";\r\n      break;\r\n    case DispatchStatus.PROGRESS:\r\n      color = \"blue\";\r\n      break;\r\n    case DispatchStatus.COMPLETED:\r\n      color = \"green\";\r\n      break;\r\n    case DispatchStatus.CANCELED:\r\n      color = \"red\";\r\n      break;\r\n    case DispatchStatus.INCOMPLETED:\r\n      color = \"orange\";\r\n      break;\r\n    case DispatchStatus.HOLD:\r\n      color = \"purple\";\r\n      break;\r\n    case DispatchStatus.ARCHIVED:\r\n      color = \"default\";\r\n      break;\r\n    case DispatchStatus.DECLINED:\r\n      color = \"red-inverse\";\r\n      break;\r\n  }\r\n\r\n  return <Tag color={color as any}>{DispatchStatusKv[status || \"-\"] ?? status}</Tag>;\r\n};\r\n\r\nconst DispatchList: React.FC = () => {\r\n  const { initialState } = useModel('@@initialState');\r\n  const actionRef = useRef<ActionType>(null);\r\n  const searchFormRef = useRef<ProFormInstance>(null);\r\n  const [selectedRows, setSelectedRows] = useState<RecordType[]>([]);\r\n\r\n  const columns: ProColumns<RecordType>[] = [\r\n    {\r\n      title: \"ID\",\r\n      dataIndex: \"id\",\r\n      sorter: true,\r\n      width: 30,\r\n      align: \"center\",\r\n      render(dom, entity) {\r\n        return entity?.id;\r\n      },      \r\n    }, \r\n    {\r\n      title: \"Status\",\r\n      dataIndex: \"dispatch_combine_status\",\r\n      sorter: true,\r\n      width: 100,\r\n      align: \"center\",\r\n      render(dom, entity) {\r\n        const status = entity.dispatch_status;\r\n        if (status?.job_status == '250') {\r\n          return <DispatchStatusComp status={'assigned' as DispatchStatus} />\r\n        } else if (status?.job_status == '260' && entity.dispatch_user == null) {\r\n          return <DispatchStatusComp status={'accepted' as DispatchStatus} />\r\n        } else if (status?.job_status == '20' && entity.dispatch_user?.user_id) {\r\n          return <DispatchStatusComp status={'progress' as DispatchStatus} />\r\n        } else if (status?.job_status == '150') {\r\n          if (status?.flow_step == 'archived') {\r\n            return <DispatchStatusComp status={'archived' as DispatchStatus} />\r\n          }\r\n          return <DispatchStatusComp status={'hold' as DispatchStatus} />\r\n        } else if (status?.job_status == '10') {\r\n          return <DispatchStatusComp status={'completed' as DispatchStatus} />\r\n        } else if (status?.job_status == '40') {\r\n          return <DispatchStatusComp status={'cancelled' as DispatchStatus} />\r\n        } else if (status?.job_status == '140') {\r\n          return <DispatchStatusComp status={'incompleted' as DispatchStatus} />\r\n        } else {\r\n          return <DispatchStatusComp status={entity.dispatch_status?.flow_step as DispatchStatus} />;\r\n        }        \r\n      },      \r\n    },\r\n    {\r\n      title: \"Source Org\",\r\n      dataIndex: \"dispatch_org.external_id\",\r\n      sorter: true,\r\n      width: 100,\r\n      align: \"center\",\r\n      render(dom, entity) {\r\n        return entity.dispatch_org?.external_id;\r\n      },      \r\n    },  \r\n    {\r\n      title: \"Customer Name\",\r\n      dataIndex: \"customer.name\",\r\n      sorter: false,\r\n      width: 100,\r\n      align: \"left\",\r\n      render(dom, entity) {\r\n        return entity.customer?.name;\r\n      },      \r\n    },    \r\n    {\r\n      title: \"Customer address\",\r\n      dataIndex: \"address.full_address\",\r\n      ellipsis: true,\r\n      width: 200,\r\n      render(dom, entity) {\r\n        return entity.address?.full_address;\r\n      },\r\n    },\r\n    {\r\n      title: \"Job Source\",\r\n      dataIndex: \"job_source\",\r\n      sorter: false,\r\n      width: 100,\r\n      align: \"left\",\r\n      render(dom, entity) {\r\n        return <Tag color=\"geekblue\">{entity?.job_source}</Tag>;\r\n      },      \r\n    },   \r\n    {\r\n      title: \"Created At\",\r\n      dataIndex: \"created_at\",\r\n      sorter: true,\r\n      width: 100,\r\n      align: \"center\",\r\n      render(dom, entity) {\r\n        return Util.dtToMDY(entity.created_at);\r\n      },      \r\n    },\r\n    {\r\n      title: \"Actions\",\r\n      dataIndex: \"option\",\r\n      valueType: \"option\",\r\n      width: 100,\r\n      align: \"center\",\r\n      render: (_, record) => {\r\n        const status = record.dispatch_status;\r\n        const isArchived = status?.flow_step === 'archived';\r\n        const isAssigned = status?.job_status === '250';\r\n        \r\n        if (!isArchived && !isAssigned) {\r\n          return null;\r\n        }\r\n        \r\n        return [\r\n          <Button\r\n            key=\"accept\"\r\n            type=\"primary\"\r\n            size=\"small\"\r\n            icon={<CheckOutlined />}\r\n            onClick={async () => {\r\n              try {\r\n                const res = await acceptOrDeclineDispatch({\r\n                  job_id: record.id,\r\n                  action: 'accept'\r\n                });\r\n                if (res?.success) {\r\n                  message.success('Status updated to accepted successfully');\r\n                  actionRef.current?.reload();\r\n                } else {\r\n                  message.error(res?.message || 'Failed to update status');\r\n                }\r\n              } catch (error) {\r\n                message.error('Failed to update status');\r\n              }\r\n            }}\r\n          >\r\n            Accept\r\n          </Button>\r\n        ];\r\n      },\r\n    },\r\n  ];\r\n  \r\n  return (\r\n    <PageContainer>\r\n      <Card size=\"small\" style={{ marginBottom: 16 }}>\r\n        <ProForm\r\n          formRef={searchFormRef}\r\n          layout=\"inline\"\r\n          isKeyPressSubmit\r\n          submitter={{\r\n            resetButtonProps: { style: { marginLeft: \"auto\" } },\r\n            searchConfig: { submitText: \"Search\" },\r\n            submitButtonProps: { htmlType: \"submit\" },\r\n            onReset(value) {\r\n              searchFormRef.current?.resetFields();\r\n              actionRef.current?.reload();\r\n            },\r\n            onSubmit(value) {\r\n              actionRef.current?.reload();\r\n            },\r\n            render(props, dom) {\r\n              return (\r\n                <Space style={{ marginLeft: \"auto\" }} size={8}>\r\n                  {dom}\r\n                </Space>\r\n              );\r\n            },\r\n          }}\r\n        >\r\n          <ProFormDigit name=\"dispatch_id\" label=\"Job ID#\" width=\"xs\" />\r\n          <ProFormDigit name=\"dispatch_external_id\" label=\"External ID#\" width={120} />\r\n          <ProFormSelect\r\n            name=\"status\"\r\n            label=\"Status\"\r\n            colProps={{ flex: \"100px\" }}\r\n            placeholder={\"All\"}\r\n            formItemProps={{ style: { marginBottom: 0 } }}\r\n            valueEnum={DispatchStatusKv}\r\n            fieldProps={{\r\n              popupMatchSelectWidth: false,\r\n              onChange(value, option) {\r\n                actionRef.current?.reload();\r\n              },\r\n            }}\r\n          />\r\n          <ProFormSelect\r\n            name=\"job_source\"\r\n            label=\"Job Source\"\r\n            colProps={{ flex: \"120px\" }}\r\n            placeholder={\"All\"}\r\n            formItemProps={{ style: { marginBottom: 0 } }}\r\n            options={[\r\n              { label: \"Front Door\", value: \"frontdoor\" },\r\n              { label: \"Dev Butler\", value: \"developmentbutler\" },\r\n              { label: \"Self\", value: \"self\" }\r\n            ]}\r\n            fieldProps={{\r\n              popupMatchSelectWidth: false,\r\n              onChange(value, option) {\r\n                actionRef.current?.reload();\r\n              },\r\n            }}\r\n          />\r\n          <ProFormText name=\"ft_keyWords\" placeholder=\"Search Customer / Address\" />\r\n        </ProForm>\r\n      </Card>\r\n\r\n      <ProTable<RecordType, API.PageParams>\r\n        headerTitle={\"Dispatch list\"}\r\n        actionRef={actionRef}\r\n        rowKey=\"id\"\r\n        revalidateOnFocus={false}\r\n        size=\"small\"\r\n        search={false}\r\n        scroll={{ x: 1000 }}\r\n        \r\n        pagination={{ defaultPageSize: DEFAULT_PER_PAGE_PAGINATION }}\r\n        request={(params, sort, filter) => {            \r\n          const sfValues = searchFormRef.current?.getFieldsValue();          \r\n\r\n          const newParams = {\r\n            ...sfValues,\r\n            ...params,\r\n            with: \"dispatchStatus,dispatchUser\",\r\n          };\r\n          return getDispatchListByPage(newParams, sort, filter);\r\n        }}\r\n        onRequestError={Util.error}\r\n        columns={columns}\r\n        rowSelection={{\r\n          onChange: (_, selectedRows) => {\r\n            setSelectedRows(selectedRows);\r\n          },\r\n        }}\r\n        tableAlertRender={false}\r\n        columnEmptyText=\"\"\r\n        locale={{ emptyText: <></> }}\r\n      />        \r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default DispatchList;\r\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/tenant/dispatch/DispatchList/index.tsx"}, "6": {"path": "/tenant/dashboard/radius-zone-mgmt", "name": "Zone Map", "file": "@/pages/tenant/dispatch/RadiusZone/index.tsx", "parentId": "3", "id": "6", "absPath": "/tenant/dashboard/radius-zone-mgmt", "__content": "import React, { useRef, useState } from 'react';\r\nimport { useModel } from '@umijs/max';\r\nimport { \r\n  Button,\r\n  Card,\r\n  theme,\r\n  Space,\r\n  Table,\r\n  Tag,\r\n  TagProps,\r\n  Typography,\r\n  Splitter\r\n} from 'antd';\r\nimport {\r\n  ActionType,\r\n  PageContainer,\r\n  ProColumns,\r\n  ProForm,\r\n  ProFormDigit,\r\n  ProFormInstance,\r\n  ProFormSelect,\r\n  ProFormText,\r\n  ProTable,\r\n} from \"@ant-design/pro-components\";\r\nimport { \r\n  EditOutlined, \r\n  FilePdfOutlined, \r\n  PlusOutlined, \r\n  SendOutlined \r\n} from \"@ant-design/icons\";\r\nimport Util, { nf2, sn } from \"@/util\";\r\nimport { DEFAULT_PER_PAGE_PAGINATION, DispatchStatus, DispatchStatusKv } from \"@/constants\";\r\nimport { getZoneListByPage } from \"@/services/app/tenant/zone/zone\";\r\nimport ZoneMap from './components/zoneMap';\r\n\r\ntype RecordType = API.DispatchZone;\r\ntype ZipcodeRecordType = API.DispatchZoneZipcode;\r\n\r\nconst RadiusZone: React.FC = () => {\r\n  const actionRef = useRef<ActionType>(null);\r\n  const zipcodeActionRef = useRef<ActionType>(null);\r\n  const searchFormRef = useRef<ProFormInstance>(null);  \r\n  const [selectedRows, setSelectedRows] = useState<RecordType[]>([]);\r\n  const [clickedRow, setClickedRow] = useState<RecordType>({});\r\n  const columns: ProColumns<RecordType>[] = [\r\n    {\r\n      title: \"ID\",\r\n      dataIndex: \"id\",\r\n      sorter: true,\r\n      width: 10,\r\n      align: \"center\",\r\n      render(dom, entity) {\r\n        return entity?.id;\r\n      },      \r\n    }, \r\n    {\r\n      title: \"Name\",\r\n      dataIndex: \"name\",\r\n      sorter: true,\r\n      width: 10,\r\n      align: \"left\",\r\n      render(dom, entity) {\r\n        return entity?.zone_name;\r\n      },      \r\n    }, \r\n    {\r\n      title: \"Created At\",\r\n      dataIndex: \"id\",\r\n      sorter: true,\r\n      width: 100,\r\n      align: \"center\",\r\n      render(dom, entity) {\r\n        return Util.dtToMDY(entity.created_at);\r\n      },      \r\n    }, \r\n  ];\r\n\r\n  const zipcodeColumns: ProColumns<ZipcodeRecordType>[] = [\r\n    {\r\n      title: \"ZipCode\",\r\n      dataIndex: \"zipcode\",\r\n      sorter: true,\r\n      width: 15,\r\n      align: \"center\",\r\n      render(dom, entity) {\r\n        return entity?.zipcode;\r\n      },      \r\n    }, \r\n  ];\r\n\r\n  return (\r\n    <PageContainer>\r\n      \r\n      <Splitter style={{ height: 'calc(100vh - 200px)', overflow: 'hidden', marginTop: 16 }}>\r\n        <Splitter.Panel collapsible>\r\n          <ProTable<RecordType, API.PageParams>\r\n            headerTitle={\"Zone list\"}\r\n            actionRef={actionRef}\r\n            rowKey=\"id\"\r\n            revalidateOnFocus={false}\r\n            size=\"small\"\r\n            search={false}\r\n            \r\n            pagination={{ defaultPageSize: 5, hideOnSinglePage: true }}\r\n            request={(params, sort, filter) => {            \r\n              const sfValues = searchFormRef.current?.getFieldsValue();          \r\n\r\n              const newParams = {\r\n                ...sfValues,\r\n                ...params,\r\n                with: \"\",\r\n              };\r\n              return getZoneListByPage(newParams, sort, filter);\r\n            }}\r\n            onRequestError={Util.error}\r\n            columns={columns}\r\n            onRow={(record) => {\r\n              return {\r\n                onClick: () => {                  \r\n                  setClickedRow(record);\r\n                },\r\n              };\r\n            }}            \r\n            tableAlertRender={false}\r\n            columnEmptyText=\"\"\r\n            locale={{ emptyText: <></> }}\r\n          />   \r\n\r\n          <ProTable<ZipcodeRecordType, API.PageParams>\r\n            headerTitle={\"Zipcode list\"}\r\n            actionRef={zipcodeActionRef}\r\n            rowKey=\"zipcode\"\r\n            revalidateOnFocus={false}\r\n            size=\"small\"\r\n            search={false}            \r\n            dataSource={clickedRow?.zipcodes || []}\r\n            pagination={{ defaultPageSize: 10, hideOnSinglePage: true }}\r\n            columns={zipcodeColumns}            \r\n            tableAlertRender={false}\r\n            columnEmptyText=\"\"\r\n            locale={{ emptyText: <></> }}\r\n          />\r\n        </Splitter.Panel>\r\n        \r\n        <Splitter.Panel collapsible>          \r\n          <ZoneMap \r\n            zoneData={clickedRow} \r\n            onZoneCreated={() => actionRef.current?.reload()}\r\n            onZoneUpdated={(updatedZone) => {\r\n              actionRef.current?.reload();\r\n              if (updatedZone) {\r\n                setClickedRow(updatedZone);\r\n              }\r\n            }} \r\n          />\r\n        </Splitter.Panel>\r\n      </Splitter>\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default RadiusZone;\r\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/tenant/dispatch/RadiusZone/index.tsx"}, "7": {"key": "customer", "path": "/tenant/customer", "name": "Customer", "parentId": "ant-design-pro-layout", "id": "7", "absPath": "/tenant/customer"}, "8": {"path": "/tenant/customer/list", "name": "Customer List", "file": "@/pages/tenant/Customer/CustomerList/index.tsx", "parentId": "7", "id": "8", "absPath": "/tenant/customer/list", "__content": "import { DEFAULT_PER_PAGE_PAGINATION } from \"@/constants\";\r\nimport { getCustomerListByPage } from \"@/services/app/tenant/customer/customer\";\r\nimport Util, { nf2, ni, sn } from \"@/util\";\r\nimport {\r\n  ActionType,\r\n  PageContainer,\r\n  ProColumns,\r\n  ProForm,\r\n  ProFormInstance,\r\n  ProFormSelect,\r\n  ProFormText,\r\n  ProTable,\r\n} from \"@ant-design/pro-components\";\r\nimport { Card, Space, Tag, TagProps, Button } from \"antd\";\r\nimport { PlusOutlined, UserAddOutlined } from \"@ant-design/icons\";\r\nimport { useEffect, useRef, useState } from \"react\";\r\nimport CreateForm from \"./components/CreateForm\";\r\nimport CreateJobForm from \"./components/CreateJobForm\";\r\n\r\ntype RecordType = API.Customer;\r\n\r\nconst CustomerList: React.FC = () => {\r\n  const actionRef = useRef<ActionType>(null);\r\n  const searchFormRef = useRef<ProFormInstance>(null);\r\n  const [selectedRowsState, setSelectedRows] = useState<RecordType[]>([]);\r\n  const [totalRow, setTotalRow] = useState<RecordType>();\r\n  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);\r\n  const [createJobModalVisible, setCreateJobModalVisible] = useState<boolean>(false);\r\n  const [selectedCustomer, setSelectedCustomer] = useState<RecordType>();\r\n\r\n  const columns: ProColumns<RecordType>[] = [\r\n    {\r\n      title: \"Name\",\r\n      dataIndex: \"name\",\r\n      sorter: true,\r\n      width: 150,\r\n      ellipsis: true,\r\n      fixed: \"left\",\r\n    },\r\n    {\r\n      title: \"Email\",\r\n      dataIndex: \"email\",\r\n      sorter: true,\r\n      width: 200,\r\n      ellipsis: true,\r\n      copyable: true,\r\n    },\r\n    {\r\n      title: \"Phone\",\r\n      dataIndex: [\"latest_customer_phones\", \"phone\"],\r\n      width: 120,\r\n    },\r\n    {\r\n      title: \"Recent Address\",\r\n      dataIndex: [\"latest_address\", \"full_address\"],\r\n      ellipsis: true,\r\n      width: 400,\r\n    },    \r\n    {\r\n      title: \"Source\",\r\n      dataIndex: \"source\",\r\n      width: 100,\r\n      render(dom, entity) {\r\n        let color: TagProps[\"color\"] = \"default\";\r\n        if (entity.source === \"frontdoor\") {\r\n          color = \"blue\";\r\n        } else if (entity.source === \"development bulter\") {\r\n          color = \"cyan\";\r\n        }\r\n        return <Tag color={color}>{entity.source}</Tag>;\r\n      },\r\n    },\r\n    {\r\n      title: \"ENV\",\r\n      dataIndex: \"env\",\r\n      width: 100,\r\n      render(dom, entity) {\r\n        return <Tag color={entity.env == \"production\" ? \"green\" : \"default\"}>{entity.env}</Tag>;\r\n      },\r\n    },\r\n    {\r\n      title: \"ID\",\r\n      dataIndex: \"id\",\r\n      sorter: true,\r\n      width: 80,\r\n      className: \"c-grey\",\r\n      align: \"center\",\r\n    },\r\n    {\r\n      title: \"Actions\",\r\n      dataIndex: \"option\",\r\n      valueType: \"option\",\r\n      width: 120,\r\n      render: (_, record) => [\r\n        <Button\r\n          key=\"createJob\"\r\n          size=\"small\"\r\n          type=\"primary\"\r\n          icon={<UserAddOutlined />}\r\n          onClick={() => {\r\n            setSelectedCustomer(record);\r\n            setCreateJobModalVisible(true);\r\n          }}\r\n        >\r\n          Create Job\r\n        </Button>,\r\n      ],\r\n    },\r\n  ];\r\n\r\n  useEffect(() => {\r\n    searchFormRef.current?.setFieldsValue(Util.getSfValues(\"customer_list\", {}));\r\n  }, []);\r\n\r\n  return (\r\n    <PageContainer>\r\n      <Card size=\"small\" style={{ marginBottom: 16 }}>\r\n        <ProForm\r\n          formRef={searchFormRef}\r\n          layout=\"inline\"\r\n          isKeyPressSubmit\r\n          submitter={{\r\n            resetButtonProps: { style: { marginLeft: \"auto\" } },\r\n            searchConfig: { submitText: \"Search\" },\r\n            submitButtonProps: { htmlType: \"submit\" },\r\n            onReset() {\r\n              searchFormRef.current?.resetFields();\r\n              actionRef.current?.reload();\r\n            },\r\n            onSubmit() {\r\n              actionRef.current?.reload();\r\n            },\r\n            render(props, dom) {\r\n              return (\r\n                <Space style={{ marginLeft: \"auto\" }} size={8}>\r\n                  {dom}\r\n                </Space>\r\n              );\r\n            },\r\n          }}\r\n        >\r\n          <ProFormText name=\"like_name\" label=\"Customer Name\" placeholder=\"Customer Name\" width={140} />\r\n          <ProFormText name=\"like_email\" label=\"Email\" width={140} />\r\n          <ProFormText name=\"phone\" label=\"Phone\" width={120} />\r\n          <ProFormText name=\"ft_keyWords\" placeholder=\"Search Customer / Address\" />          \r\n          <ProFormSelect name=\"source\" options={[\"frontdoor\", \"development butler\"]} />\r\n          <ProFormSelect name=\"env\" options={[\"sandbox\", \"production\"]} />\r\n        </ProForm>\r\n      </Card>\r\n\r\n      <ProTable<RecordType, API.PageParams>\r\n        headerTitle={\"Customers list\"}\r\n        actionRef={actionRef}\r\n        rowKey=\"id\"\r\n        revalidateOnFocus={false}\r\n        size=\"small\"\r\n        search={false}\r\n        scroll={{ x: 1400, y: 600 }}\r\n        sticky\r\n        pagination={{ defaultPageSize: DEFAULT_PER_PAGE_PAGINATION }}\r\n        toolBarRender={() => [\r\n          <Button\r\n            type=\"primary\"\r\n            key=\"primary\"\r\n            onClick={() => setCreateModalVisible(true)}\r\n          >\r\n            <PlusOutlined /> New Customer\r\n          </Button>,\r\n        ]}\r\n        request={async (params, sort, filter) => {\r\n          const sfValues = searchFormRef.current?.getFieldsValue();\r\n          Util.setSfValues(\"customer_list\", sfValues);\r\n\r\n          const newParams = {\r\n            ...sfValues,\r\n            ...params,\r\n            with: \"latest_address,total_row,latest_customer_phones,customer_all\",\r\n          };\r\n          return getCustomerListByPage(newParams, sort, filter).then((res) => {\r\n            setTotalRow(res.total_row);\r\n            return res;\r\n          });\r\n        }}\r\n        onRequestError={Util.error}\r\n        columns={columns}       \r\n        rowSelection={{\r\n          onChange: (_, selectedRows) => {\r\n            setSelectedRows(selectedRows);\r\n          },\r\n        }}\r\n        tableAlertRender={false}\r\n        columnEmptyText=\"\"\r\n        locale={{ emptyText: <></> }}\r\n      />\r\n\r\n      <CreateForm\r\n        visible={createModalVisible}\r\n        onVisibleChange={setCreateModalVisible}\r\n        onSuccess={() => {\r\n          actionRef.current?.reload();\r\n        }}\r\n      />\r\n\r\n      <CreateJobForm\r\n        customer={selectedCustomer!}\r\n        modalVisible={createJobModalVisible && !!selectedCustomer}\r\n        handleModalVisible={setCreateJobModalVisible}\r\n        onSubmit={() => {\r\n          actionRef.current?.reload();\r\n        }}\r\n      />\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default CustomerList;\r\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/tenant/Customer/CustomerList/index.tsx"}, "9": {"path": "/tenant/invoice", "name": "Estimates & Invoices", "parentId": "ant-design-pro-layout", "id": "9", "absPath": "/tenant/invoice"}, "10": {"path": "/tenant/invoice/estimate/list", "name": "Estimates", "file": "@/pages/tenant/Estimate/index.tsx", "parentId": "9", "id": "10", "absPath": "/tenant/invoice/estimate/list", "__content": "import { deleteDispatchEstimate, getDispatchEstimateListByPage, getDispatchEstimatePdf } from \"@/services/app/tenant/dispatch/dispatch-estimate\";\nimport Util, { nf2, sn } from \"@/util\";\nimport { EditOutlined, FilePdfOutlined, PlusOutlined, SendOutlined } from \"@ant-design/icons\";\nimport {\n  ActionType,\n  PageContainer,\n  ProColumns,\n  ProForm,\n  ProFormDigit,\n  ProFormInstance,\n  ProFormSelect,\n  ProFormText,\n  ProTable,\n} from \"@ant-design/pro-components\";\nimport { Button, Card, message, notification, Popover, Space, Tag, TagProps, Typography } from \"antd\";\nimport { useEffect, useRef, useState } from \"react\";\nimport CreateForm from \"./components/CreateForm\";\nimport { DEFAULT_PER_PAGE_PAGINATION, EstimateStatus, EstimateStatusKv } from \"@/constants\";\nimport UpdateForm from \"./components/UpdateForm\";\nimport { addDispatchInvoiceByEstimate } from \"@/services/app/tenant/dispatch/dispatch-invoice\";\nimport SFooterToolbar from \"@/components/Table/SFooterToolbar\";\n\nexport const EstimateStatusComp: React.FC<{ status?: EstimateStatus | string }> = ({ status }) => {\n  let color: TagProps[\"color\"] = \"default\";\n  switch (status) {\n    case EstimateStatus.CLOSED:\n      color = \"green-inverse\";\n      break;\n    case EstimateStatus.APPROVED:\n      color = \"success\";\n      break;\n    case EstimateStatus.SENT:\n      color = \"blue\";\n      break;\n    case EstimateStatus.EXPIRED:\n      color = \"geekblue-inverse\";\n      break;\n    case EstimateStatus.DECLINED:\n      color = \"red\";\n      break;\n  }\n\n  return <Tag color={color as any}>{EstimateStatusKv[status || \"-\"] ?? \"-\"}</Tag>;\n};\n\ntype RecordType = API.DispatchEstimate;\n\nconst InvoiceListPage: React.FC = () => {\n  const actionRef = useRef<ActionType>(null);\n  const searchFormRef = useRef<ProFormInstance>(null);\n\n  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);\n  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);\n\n  const [currentRow, setCurrentRow] = useState<RecordType>();\n  const [selectedRowsState, setSelectedRows] = useState<RecordType[]>([]);\n\n  const columns: ProColumns<RecordType>[] = [\n    {\n      title: \"Status\",\n      dataIndex: \"status\",\n      sorter: true,\n      width: 100,\n      align: \"center\",\n      render(dom, entity) {\n        return <EstimateStatusComp status={entity.status as EstimateStatus} />;\n      },\n    },\n    {\n      title: \"Est. No\",\n      dataIndex: \"id\",\n      sorter: true,\n      width: 80,\n      copyable: true,\n      defaultSortOrder: \"descend\",\n    },\n    {\n      title: \"Purchase No\",\n      dataIndex: \"purchase_no\",\n      sorter: true,\n      width: 120,\n      copyable: true,\n    },\n    {\n      title: \"File\",\n      dataIndex: [\"file\", \"url\"],\n      ellipsis: true,\n      width: 40,\n      render(dom, entity, index, action, schema) {\n        return entity.file ? (\n          <Typography.Link href={`${API_URL}/${entity.file?.url}`} target=\"_blank\">\n            <FilePdfOutlined />\n          </Typography.Link>\n        ) : null;\n      },\n    },\n    {\n      title: \"Job ID\",\n      dataIndex: \"dispatch_id\",\n      sorter: true,\n      width: 80,\n      align: \"center\",\n    },\n    {\n      title: \"External ID\",\n      dataIndex: [\"dispatch\", \"external_id\"],\n      sorter: true,\n      copyable: true,\n      width: 110,\n      align: \"center\",\n    },\n    {\n      title: \"Customer\",\n      dataIndex: \"customer_name\",\n      ellipsis: true,\n      width: 150,\n    },\n    {\n      title: \"Customer address\",\n      dataIndex: \"customer_full_address\",\n      ellipsis: true,\n      width: 320,\n    },\n    {\n      title: \"Issue Date\",\n      dataIndex: \"issue_date\",\n      sorter: true,\n      valueType: \"date\",\n      width: 90,\n      render(dom, entity) {\n        return Util.dtToMDY(entity.issue_date);\n      },\n    },\n    {\n      title: \"Work Start Date\",\n      dataIndex: \"work_start_date\",\n      sorter: true,\n      valueType: \"date\",\n      width: 90,\n      render(dom, entity) {\n        return Util.dtToMDY(entity.work_start_date);\n      },\n    },\n\n    {\n      title: \"Sub Total\",\n      dataIndex: \"subtotal\",\n      width: 100,\n      valueType: \"money\",\n      align: \"right\",\n      className: \"bl-2 b-gray\",\n      render: (_, record) => sn(record.subtotal) > 0 ?  `$${nf2(record.subtotal || 0)}` : null,\n    },\n    {\n      title: \"Tax %\",\n      dataIndex: \"tax_percent\",\n      width: 65,\n      valueType: \"money\",\n      align: \"right\",\n      render: (_, record) => (sn(record.tax_percent) > 0 ? `${nf2(record.tax_percent || 0)}%` : null),\n    },\n    {\n      title: \"Tax\",\n      dataIndex: \"tax_amount\",\n      width: 90,\n      valueType: \"money\",\n      align: \"right\",\n      render: (_, record) => (sn(record.tax_amount) > 0 ? `$${nf2(record.tax_amount || 0)}` : null),\n    },\n    {\n      title: \"Coupon\",\n      dataIndex: [\"coupon\", \"name\"],\n      width: 80,\n      tooltip: \"Click to view details...\",\n      className: \"text-sm cursor-pointer\",\n      render(__, entity) {\n        return entity.coupon ? (\n          <>\n            <Popover\n              title={`Coupon ${entity.coupon.name}`}\n              trigger={[\"click\"]}\n              content={\n                <Space direction=\"vertical\" size={4}>\n                  <div>\n                    <strong>Code:</strong>\n                    <Typography.Text copyable>{entity.coupon.code}</Typography.Text>\n                  </div>\n                  <div>\n                    <strong>Type:</strong> {entity.coupon.type === \"percentage\" ? \"Percentage\" : \"Fixed Amount\"}\n                  </div>\n                  <div>\n                    <strong>Value:</strong> {entity.coupon.type === \"percentage\" ? `${entity.coupon.value}%` : `$${entity.coupon.value}`}\n                  </div>\n                  <div>\n                    <strong>Discount Applied:</strong> ${nf2(entity.coupon_discount || 0)}\n                  </div>\n                  {entity.coupon.description && (\n                    <div>\n                      <strong>Description:</strong> {entity.coupon.description}\n                    </div>\n                  )}\n                </Space>\n              }\n            >\n              <div className=\"c-red\" style={{ textAlign: \"right\" }}>\n                ${nf2(entity.coupon_discount)}\n              </div>\n            </Popover>\n          </>\n        ) : null;\n      },\n    },\n    {\n      title: \"Promotion\",\n      dataIndex: [\"promo\", \"name\"],\n      width: 80,\n      tooltip: \"Click to view details...\",\n      className: \"text-sm cursor-pointer\",\n      render(__, entity) {\n        return entity.promo ? (\n          <>\n            <Popover\n              title={`Promotion ${entity.promo.name}`}\n              trigger={[\"click\"]}\n              content={\n                <Space direction=\"vertical\" size={4}>\n                  <div>\n                    <strong>Code:</strong>\n                    <Typography.Text copyable>{entity.promo.code}</Typography.Text>\n                  </div>\n                  <div>\n                    <strong>Type:</strong> {entity.promo.type === \"percentage\" ? \"Percentage\" : \"Fixed Amount\"}\n                  </div>\n                  <div>\n                    <strong>Value:</strong> {entity.promo.type === \"percentage\" ? `${entity.promo.value}%` : `$${entity.promo.value}`}\n                  </div>\n                  <div>\n                    <strong>Discount Applied:</strong> ${nf2(entity.promo_discount || 0)}\n                  </div>\n                  {entity.promo.description && (\n                    <div>\n                      <strong>Description:</strong> {entity.promo.description}\n                    </div>\n                  )}\n                </Space>\n              }\n            >\n              <div className=\"c-red\" style={{ textAlign: \"right\" }}>\n                ${nf2(entity.promo_discount)}\n              </div>\n            </Popover>\n          </>\n        ) : null;\n      },\n    },\n    {\n      title: \"Discount\",\n      dataIndex: \"discount_amount\",\n      width: 80,\n      valueType: \"money\",\n      align: \"right\",\n      render: (_, record) => (sn(record.discount_amount) > 0 ? <span className=\"c-red\">{`$${nf2(record.discount_amount || 0)}`}</span> : null),\n    },\n    {\n      title: \"Grand Total\",\n      dataIndex: \"grand_total\",\n      width: 100,\n      valueType: \"money\",\n      align: \"right\",\n      className: \"bl-2 b-gray\",\n      render: (_, record) => (sn(record.grand_total) > 0 ? <span className=\"bold\">{`$${nf2(record.grand_total || 0)}`}</span> : null),\n    },\n    {\n      title: \"Payment Terms\",\n      dataIndex: \"payment_terms\",\n      className: \"bl-2 b-gray\",\n      ellipsis: true,\n      width: 120,\n    },\n\n    {\n      title: \"Created On\",\n      sorter: true,\n      dataIndex: \"created_at\",\n      valueType: \"dateTime\",\n      width: 130,\n      render(__, record) {\n        return Util.dtToMDYHHMM(record.created_at);\n      },\n    },\n    {\n      title: \"Option\",\n      dataIndex: \"option\",\n      valueType: \"option\",\n      width: 100,\n      fixed: \"right\",\n      render: (_, record) => [\n        <Button\n          key=\"edit\"\n          size=\"small\"\n          icon={<EditOutlined />}\n          title=\"Edit estimate\"\n          onClick={() => {\n            handleUpdateModalVisible(true);\n            setCurrentRow(record);\n          }}\n        />,\n        <Button\n          key=\"pdf\"\n          size=\"small\"\n          title=\"Generate PDF\"\n          type={record.file ? \"primary\" : \"default\"}\n          icon={<FilePdfOutlined />}\n          onClick={() => {\n            const hide = message.loading(\"Downloading PDF...\", 0);\n            getDispatchEstimatePdf(sn(record.id))\n              .then((res) => {\n                window.open(`${API_URL}/${res.url}`, \"_blank\");\n                message.success(\"Downloaded PDF successfully!\");\n                actionRef.current?.reload();\n              })\n              .catch(Util.error)\n              .finally(hide);\n          }}\n        />,\n        <Button\n          key=\"convert\"\n          size=\"small\"\n          type=\"primary\"\n          title=\"Create new invoice from this estimation\"\n          icon={<SendOutlined />}\n          onClick={() => {\n            const hide = message.loading(\"Create new invoice from this estimation...\", 0);\n            addDispatchInvoiceByEstimate(sn(record.id))\n              .then((res) => {\n                notification.success({\n                  duration: 0,\n                  message: `Created new invoice #${res.invoice_no} successfully!`,\n                  description: (\n                    <Space direction=\"vertical\" size={8}>\n                      <Typography.Link href={`${API_URL}/${res.file?.url}`} target=\"_blank\">\n                        View invoice\n                      </Typography.Link>\n                      <Typography.Link href={`${PUBLIC_PATH}tenant/invoice/list?id=${res.id}`} target=\"_blank\">\n                        Go to Invoices...\n                      </Typography.Link>\n                    </Space>\n                  ),\n                });\n                actionRef.current?.reload();\n              })\n              .catch(Util.error)\n              .finally(hide);\n          }}\n        />,\n      ],\n    },\n  ];\n\n  useEffect(() => {}, []);\n\n  return (\n    <PageContainer>\n      <Card size=\"small\" style={{ marginBottom: 16 }}>\n        <ProForm\n          formRef={searchFormRef}\n          layout=\"inline\"\n          isKeyPressSubmit\n          submitter={{\n            resetButtonProps: { style: { marginLeft: \"auto\" } },\n            searchConfig: { submitText: \"Search\" },\n            submitButtonProps: { htmlType: \"submit\" },\n            onReset(value) {\n              searchFormRef.current?.resetFields();\n              actionRef.current?.reload();\n            },\n            onSubmit(value) {\n              actionRef.current?.reload();\n            },\n            render(props, dom) {\n              return (\n                <Space style={{ marginLeft: \"auto\" }} size={8}>\n                  {dom}\n                </Space>\n              );\n            },\n          }}\n        >\n          <ProFormText name=\"id\" label=\"Estimate No#\" width={140} />\n          <ProFormDigit name=\"dispatch_id\" label=\"Job ID#\" width=\"xs\" />\n          <ProFormDigit name=\"dispatch_external_id\" label=\"External ID#\" width={120} />\n          <ProFormText name=\"purchase_no\" label=\"Purchase No#\" width={140} />\n          <ProFormSelect\n            name=\"status\"\n            label=\"Status\"\n            colProps={{ flex: \"100px\" }}\n            placeholder={\"All\"}\n            formItemProps={{ style: { marginBottom: 0 } }}\n            valueEnum={EstimateStatusKv}\n            fieldProps={{\n              popupMatchSelectWidth: false,\n              onChange(value, option) {\n                actionRef.current?.reload();\n              },\n            }}\n          />\n          <ProFormText name=\"ft_keyWords\" placeholder=\"Search Customer / Address\" />\n        </ProForm>\n      </Card>\n\n      <ProTable<RecordType, API.PageParams>\n        headerTitle={\"Estimates list\"}\n        actionRef={actionRef}\n        rowKey=\"id\"\n        revalidateOnFocus={false}\n        size=\"small\"\n        search={false}\n        scroll={{ x: 1000 }}\n        toolBarRender={() => [\n          <Button\n            type=\"primary\"\n            key=\"primary\"\n            onClick={() => {\n              handleCreateModalVisible(true);\n            }}\n          >\n            <PlusOutlined /> New\n          </Button>,\n        ]}\n        pagination={{ defaultPageSize: DEFAULT_PER_PAGE_PAGINATION }}\n        request={(params, sort, filter) => {\n          // const sfValues = Util.getSfValues(\"invoice_list\", {});\n          const sfValues = searchFormRef.current?.getFieldsValue();\n          Util.setSfValues(\"invoice_list\", sfValues);\n\n          const newParams = {\n            ...sfValues,\n            ...params,\n            with: \"items,logs,promo,coupon\",\n          };\n          return getDispatchEstimateListByPage(newParams, sort, filter);\n        }}\n        onRequestError={Util.error}\n        columns={columns}\n        rowSelection={{\n          onChange: (_, selectedRows) => {\n            setSelectedRows(selectedRows);\n          },\n        }}\n        tableAlertRender={false}\n        columnEmptyText=\"\"\n        locale={{ emptyText: <></> }}\n      />\n\n      <CreateForm\n        modalVisible={createModalVisible}\n        handleModalVisible={handleCreateModalVisible}\n        onSubmit={(values) => {\n          handleCreateModalVisible(false);\n          actionRef.current?.reload();\n          return Promise.resolve(true);\n        }}\n      />\n\n      <UpdateForm\n        modalVisible={updateModalVisible}\n        handleModalVisible={handleUpdateModalVisible}\n        initialValues={currentRow}\n        onSubmit={(values) => {\n          handleUpdateModalVisible(false);\n          actionRef.current?.reload();\n          return Promise.resolve(true);\n        }}\n      />\n\n      <SFooterToolbar\n        title={\"estimate\"}\n        selectedRowsState={selectedRowsState}\n        setSelectedRows={setSelectedRows}\n        actionRef={actionRef}\n        handleRemove={(rows) => {\n          return deleteDispatchEstimate(selectedRowsState.map((row) => row.id)?.join(\",\"));\n        }}\n      />\n    </PageContainer>\n  );\n};\n\nexport default InvoiceListPage;\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/tenant/Estimate/index.tsx"}, "11": {"path": "/tenant/invoice/list", "name": "Invoices", "file": "@/pages/tenant/Invoice/index.tsx", "parentId": "9", "id": "11", "absPath": "/tenant/invoice/list", "__content": "import { deleteDispatchInvoice, getDispatchInvoiceListByPage, getDispatchInvoicePdf } from \"@/services/app/tenant/dispatch/dispatch-invoice\";\nimport Util, { nf2, sn } from \"@/util\";\nimport { EditOutlined, FilePdfOutlined, PlusOutlined, FileProtectOutlined } from \"@ant-design/icons\";\nimport {\n  ActionType,\n  PageContainer,\n  ProColumns,\n  ProForm,\n  ProFormDigit,\n  ProFormInstance,\n  ProFormSelect,\n  ProFormSwitch,\n  ProFormText,\n  ProTable,\n} from \"@ant-design/pro-components\";\nimport { Button, Card, message, Popover, Space, Tag, TagProps, Typography } from \"antd\";\nimport { useRef, useState } from \"react\";\nimport CreateForm from \"./components/CreateForm\";\nimport { DEFAULT_PER_PAGE_PAGINATION, InvoiceStatus, InvoiceStatusKv } from \"@/constants\";\nimport UpdateForm from \"./components/UpdateForm\";\nimport SFooterToolbar from \"@/components/Table/SFooterToolbar\";\nimport { startESign } from \"@/services/app/tenant/dispatch/dispatch-invoice-esign\";\nimport { startSignNow } from \"@/services/app/tenant/dispatch/dispatch-invoice-signnow\";\n\nexport const InvoiceStatusComp: React.FC<{ status?: InvoiceStatus | string }> = ({ status }) => {\n  let color: TagProps[\"color\"] = \"default\";\n  switch (status) {\n    case InvoiceStatus.CLOSED:\n      color = \"green-inverse\";\n      break;\n    case InvoiceStatus.PAID:\n      color = \"success\";\n      break;\n    case InvoiceStatus.SENT:\n      color = \"blue\";\n      break;\n    case InvoiceStatus.PENDING:\n      color = \"orange\";\n      break;\n    case InvoiceStatus.CANCELED:\n      color = \"geekblue-inverse\";\n      break;\n    case InvoiceStatus.REFUNDED:\n      color = \"red\";\n      break;\n  }\n\n  return <Tag color={color as any}>{InvoiceStatusKv[status || \"-\"] ?? \"-\"}</Tag>;\n};\n\ntype RecordType = API.DispatchInvoice;\n\nconst InvoiceListPage: React.FC = () => {\n  const actionRef = useRef<ActionType>(null);\n  const searchFormRef = useRef<ProFormInstance>(null);\n\n  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);\n  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);\n\n  const [currentRow, setCurrentRow] = useState<RecordType>();\n  const [selectedRowsState, setSelectedRows] = useState<RecordType[]>([]);\n  const [totalRow, setTotalRow] = useState<RecordType>();\n\n  const columns: ProColumns<RecordType>[] = [\n    {\n      title: \"Status\",\n      dataIndex: \"status\",\n      sorter: true,\n      width: 100,\n      align: \"center\",\n      render(dom, entity) {\n        return <InvoiceStatusComp status={entity.status as InvoiceStatus} />;\n      },\n    },\n    {\n      title: \"Invoice No\",\n      dataIndex: \"invoice_no\",\n      sorter: true,\n      width: 120,\n      copyable: true,\n      defaultSortOrder: \"descend\",\n    },\n    {\n      title: \"Est. No\",\n      dataIndex: [\"estimate_id\"],\n      width: 60,\n    },\n    {\n      title: \"File\",\n      dataIndex: [\"file\", \"url\"],\n      ellipsis: true,\n      width: 40,\n      render(dom, entity) {\n        return entity.file ? (\n          <Typography.Link href={`${API_URL}/${entity.file?.url}`} target=\"_blank\">\n            <FilePdfOutlined />\n          </Typography.Link>\n        ) : null;\n      },\n    },\n    {\n      title: \"Job ID\",\n      dataIndex: \"dispatch_id\",\n      sorter: true,\n      width: 80,\n      align: \"center\",\n      copyable: true,\n      className: \"bl-2 b-gray\",\n    },\n    {\n      title: \"External ID\",\n      dataIndex: [\"dispatch\", \"external_id\"],\n      sorter: true,\n      copyable: true,\n      width: 110,\n      align: \"center\",\n    },\n    {\n      title: \"Customer\",\n      dataIndex: \"customer_name\",\n      ellipsis: true,\n      width: 150,\n    },\n    {\n      title: \"Issue Date\",\n      dataIndex: \"issue_date\",\n      sorter: true,\n      valueType: \"date\",\n      width: 90,\n      render(dom, entity) {\n        return Util.dtToMDY(entity.issue_date);\n      },\n    },\n    {\n      title: \"Service Date\",\n      dataIndex: \"service_date\",\n      sorter: true,\n      valueType: \"date\",\n      width: 90,\n      render(dom, entity) {\n        return Util.dtToMDY(entity.service_date);\n      },\n    },\n\n    {\n      title: \"Sub Total\",\n      dataIndex: \"subtotal\",\n      width: 100,\n      valueType: \"money\",\n      align: \"right\",\n      className: \"bl-2 b-gray\",\n      render: (_, record) => (sn(record.subtotal) > 0 ? `$${nf2(record.subtotal || 0)}` : null),\n    },\n    {\n      title: \"Tax %\",\n      dataIndex: \"tax_percent\",\n      width: 65,\n      valueType: \"money\",\n      align: \"right\",\n      render: (_, record) => (sn(record.tax_amount) > 0 ? (sn(record.tax_percent) > 0 ? `${nf2(record.tax_percent || 0)}%` : null) : null),\n    },\n    {\n      title: \"Tax\",\n      dataIndex: \"tax_amount\",\n      width: 90,\n      valueType: \"money\",\n      align: \"right\",\n      render: (_, record) => (sn(record.tax_amount) > 0 ? `$${nf2(record.tax_amount || 0)}` : null),\n    },\n    {\n      title: \"Coupon\",\n      dataIndex: [\"coupon\", \"name\"],\n      width: 80,\n      tooltip: \"Click to view details...\",\n      className: \"text-sm cursor-pointer\",\n      render(__, entity) {\n        return entity.coupon ? (\n          <>\n            <Popover\n              title={`Coupon ${entity.coupon.name}`}\n              trigger={[\"click\"]}\n              content={\n                <Space direction=\"vertical\" size={4}>\n                  <div>\n                    <strong>Code:</strong>\n                    <Typography.Text copyable>{entity.coupon.code}</Typography.Text>\n                  </div>\n                  <div>\n                    <strong>Type:</strong> {entity.coupon.type === \"percentage\" ? \"Percentage\" : \"Fixed Amount\"}\n                  </div>\n                  <div>\n                    <strong>Value:</strong> {entity.coupon.type === \"percentage\" ? `${entity.coupon.value}%` : `$${entity.coupon.value}`}\n                  </div>\n                  <div>\n                    <strong>Discount Applied:</strong> ${nf2(entity.coupon_discount || 0)}\n                  </div>\n                  {entity.coupon.description && (\n                    <div>\n                      <strong>Description:</strong> {entity.coupon.description}\n                    </div>\n                  )}\n                </Space>\n              }\n            >\n              <div className=\"c-red\" style={{ textAlign: \"right\" }}>\n                ${nf2(entity.coupon_discount)}\n              </div>\n            </Popover>\n          </>\n        ) : null;\n      },\n    },\n    {\n      title: \"Promotion\",\n      dataIndex: [\"promo\", \"name\"],\n      width: 80,\n      tooltip: \"Click to view details...\",\n      className: \"text-sm cursor-pointer\",\n      render(__, entity) {\n        return entity.promo ? (\n          <>\n            <Popover\n              title={`Promotion ${entity.promo.name}`}\n              trigger={[\"click\"]}\n              content={\n                <Space direction=\"vertical\" size={4}>\n                  <div>\n                    <strong>Code:</strong>\n                    <Typography.Text copyable>{entity.promo.code}</Typography.Text>\n                  </div>\n                  <div>\n                    <strong>Type:</strong> {entity.promo.type === \"percentage\" ? \"Percentage\" : \"Fixed Amount\"}\n                  </div>\n                  <div>\n                    <strong>Value:</strong> {entity.promo.type === \"percentage\" ? `${entity.promo.value}%` : `$${entity.promo.value}`}\n                  </div>\n                  <div>\n                    <strong>Discount Applied:</strong> ${nf2(entity.promo_discount || 0)}\n                  </div>\n                  {entity.promo.description && (\n                    <div>\n                      <strong>Description:</strong> {entity.promo.description}\n                    </div>\n                  )}\n                </Space>\n              }\n            >\n              <div className=\"c-red\" style={{ textAlign: \"right\" }}>\n                ${nf2(entity.promo_discount)}\n              </div>\n            </Popover>\n          </>\n        ) : null;\n      },\n    },\n    {\n      title: \"Discount\",\n      dataIndex: \"discount_amount\",\n      width: 80,\n      valueType: \"money\",\n      align: \"right\",\n      render: (_, record) => (sn(record.discount_amount) > 0 ? <span className=\"c-red\">{`$${nf2(record.discount_amount || 0)}`}</span> : null),\n    },\n    {\n      title: \"Grand Total\",\n      dataIndex: \"grand_total\",\n      width: 100,\n      valueType: \"money\",\n      align: \"right\",\n      className: \"bl-2 b-gray\",\n      render: (_, record) => (sn(record.grand_total) > 0 ? <span className=\"bold\">{`$${nf2(record.grand_total || 0)}`}</span> : null),\n    },\n    {\n      title: \"Payment Terms\",\n      dataIndex: \"payment_terms\",\n      className: \"bl-2 b-gray\",\n      ellipsis: true,\n      width: 120,\n    },\n    {\n      title: \"Billing address\",\n      dataIndex: \"billing_full_address\",\n      ellipsis: true,\n      width: 320,\n    },\n    {\n      title: \"Created On\",\n      sorter: true,\n      dataIndex: \"created_at\",\n      valueType: \"dateTime\",\n      width: 100,\n      className: \"text-sm c-grey\",\n      ellipsis: true,\n      render(__, record) {\n        return Util.dtToMDYHHMM(record.created_at);\n      },\n    },\n    {\n      title: \"Option\",\n      dataIndex: \"option\",\n      valueType: \"option\",\n      fixed: \"right\",\n      width: 100,\n      render: (_, record) => [\n        <Button\n          key=\"edit\"\n          size=\"small\"\n          icon={<EditOutlined />}\n          onClick={() => {\n            handleUpdateModalVisible(true);\n            setCurrentRow(record);\n          }}\n        />,\n        <Button\n          key=\"pdf\"\n          size=\"small\"\n          title=\"Generate PDF\"\n          type={record.file ? \"primary\" : \"default\"}\n          icon={<FilePdfOutlined />}\n          onClick={() => {\n            const hide = message.loading(\"Downloading PDF...\", 0);\n            getDispatchInvoicePdf(sn(record.id))\n              .then((res) => {\n                window.open(`${API_URL}/${res.url}`, \"_blank\");\n                message.success(\"Downloaded PDF successfully!\");\n                actionRef.current?.reload();\n              })\n              .catch(Util.error)\n              .finally(hide);\n          }}\n        />,\n        <Button\n          key=\"esign\"\n          size=\"small\"\n          title=\"Start e-Signature\"\n          type=\"default\"\n          style={{ display: \"none\" }}\n          icon={<FileProtectOutlined />}\n          onClick={() => {\n            if (!record.file) {\n              message.error(\"Please generate PDF first before starting e-signature\");\n              return;\n            }\n\n            const hide = message.loading(\"Starting e-signature process...\", 0);\n            startESign({ invoice_id: sn(record.id) })\n              .then((res) => {\n                message.success(\"E-signature session created successfully!\");\n                // window.open(res.signing_url, \"_blank\");\n                actionRef.current?.reload();\n              })\n              .catch(Util.error)\n              .finally(hide);\n          }}\n        />,\n        <Button\n          key=\"signnow\"\n          size=\"small\"\n          title=\"Start e-Signature (SignNow)\"\n          type=\"default\"\n          icon={<FileProtectOutlined />}\n          onClick={() => {\n            if (!record.file) {\n              message.error(\"Please generate PDF first before starting e-signature\");\n              return;\n            }\n\n            const hide = message.loading(\"Starting SignNow e-signature process...\", 0);\n            startSignNow({ invoice_id: sn(record.id) })\n              .then((res) => {\n                message.success(\"SignNow e-signature session created successfully!\");\n                actionRef.current?.reload();\n              })\n              .catch(Util.error)\n              .finally(hide);\n          }}\n        />,\n      ],\n    },\n  ];\n\n  return (\n    <PageContainer>\n      <Card size=\"small\" style={{ marginBottom: 16 }}>\n        <ProForm\n          formRef={searchFormRef}\n          layout=\"inline\"\n          isKeyPressSubmit\n          initialValues={Util.getSfValues(\"invoice_list\", {})}\n          submitter={{\n            resetButtonProps: { style: { marginLeft: \"auto\" } },\n            searchConfig: { submitText: \"Search\" },\n            submitButtonProps: { htmlType: \"submit\" },\n            onReset(value) {\n              searchFormRef.current?.resetFields();\n              actionRef.current?.reload();\n            },\n            onSubmit(value) {\n              actionRef.current?.reload();\n            },\n            render(props, dom) {\n              return (\n                <Space style={{ marginLeft: \"auto\" }} size={8}>\n                  {dom}\n                </Space>\n              );\n            },\n          }}\n        >\n          <ProFormText name=\"invoice_no\" label=\"Invoice No#\" width={140} />\n          <ProFormDigit name=\"dispatch_id\" label=\"Job ID#\" width=\"xs\" />\n          <ProFormDigit name=\"dispatch_external_id\" label=\"External ID#\" width={120} />\n          <ProFormSwitch\n            name=\"no_draft\"\n            label=\"Excl. Draft?\"\n            tooltip=\"Exclude draft invoices from the list\"\n            fieldProps={{ onChange: () => actionRef.current?.reload() }}\n          />\n          <ProFormSelect\n            name=\"status\"\n            label=\"Status\"\n            colProps={{ flex: \"100px\" }}\n            placeholder={\"All\"}\n            formItemProps={{ style: { marginBottom: 0 } }}\n            valueEnum={InvoiceStatusKv}\n            fieldProps={{\n              popupMatchSelectWidth: false,\n              onChange(value, option) {\n                actionRef.current?.reload();\n              },\n            }}\n          />\n          <ProFormText name=\"ft_keyWords\" placeholder=\"Search Customer / Address\" />\n        </ProForm>\n      </Card>\n\n      <ProTable<RecordType, API.PageParams>\n        headerTitle={\"Invoices list\"}\n        actionRef={actionRef}\n        rowKey=\"id\"\n        revalidateOnFocus={false}\n        size=\"small\"\n        search={false}\n        scroll={{ x: 1000 }}\n        sticky\n        toolBarRender={() => [\n          <Button\n            type=\"primary\"\n            key=\"primary\"\n            onClick={() => {\n              handleCreateModalVisible(true);\n            }}\n          >\n            <PlusOutlined /> New\n          </Button>,\n        ]}\n        pagination={{ defaultPageSize: DEFAULT_PER_PAGE_PAGINATION }}\n        request={async (params, sort, filter) => {\n          // const sfValues = Util.getSfValues(\"invoice_list\", {});\n          const sfValues = searchFormRef.current?.getFieldsValue();\n          Util.setSfValues(\"invoice_list\", sfValues);\n\n          const newParams = {\n            ...sfValues,\n            ...params,\n            with: \"items,logs,estimate,file,coupon,promo,total_row\",\n          };\n          return getDispatchInvoiceListByPage(newParams, sort, filter).then((res) => {\n            setTotalRow(res.total_row);\n            return res;\n          });\n        }}\n        onRequestError={Util.error}\n        columns={columns}\n        summary={(dataParam) => {\n          const totalRowLocal: any = totalRow ?? {};\n\n          return (\n            <ProTable.Summary fixed=\"top\">\n              <ProTable.Summary.Row style={{ fontWeight: \"bold\" }}>\n                <ProTable.Summary.Cell index={-1} className=\"bg-green1\"></ProTable.Summary.Cell>\n                {columns\n                  .filter((c, ind) => {\n                    if (c.hideInTable) return false;\n                    return true;\n                  })\n                  .map((c, index) => {\n                    const tmpKey = typeof c.dataIndex === \"string\" ? c.dataIndex : ((c.dataIndex || []) as any[]).join(\".\");\n                    let value: any = null;\n                    let align: any = \"left\";\n                    let prefix = \"\";\n\n                    if (index == 0) value = <span>Total</span>;\n                    else if (tmpKey == \"grand_total\" || tmpKey == \"discount_amount\" || tmpKey == \"tax_amount\" || tmpKey == \"subtotal\") {\n                      value = nf2(totalRowLocal[tmpKey]);\n                      align = \"right\";\n                      prefix = \"$\";\n                    }\n\n                    // CSS class\n                    let cls = `bg-green1 ${c.className || \"\"}`;\n                    if (tmpKey == \"discount_amount\") cls += \" c-red\";\n                    return (\n                      <ProTable.Summary.Cell key={tmpKey} index={index} align={align} className={cls}>\n                        {prefix}\n                        {value}\n                      </ProTable.Summary.Cell>\n                    );\n                  })}\n              </ProTable.Summary.Row>\n            </ProTable.Summary>\n          );\n        }}\n        rowSelection={{\n          onChange: (_, selectedRows) => {\n            setSelectedRows(selectedRows);\n          },\n        }}\n        tableAlertRender={false}\n        columnEmptyText=\"\"\n        locale={{ emptyText: <></> }}\n      />\n\n      <CreateForm\n        modalVisible={createModalVisible}\n        handleModalVisible={handleCreateModalVisible}\n        onSubmit={(values) => {\n          handleCreateModalVisible(false);\n          actionRef.current?.reload();\n          return Promise.resolve(true);\n        }}\n      />\n\n      <UpdateForm\n        modalVisible={updateModalVisible}\n        handleModalVisible={handleUpdateModalVisible}\n        initialValues={currentRow}\n        onSubmit={(values) => {\n          handleUpdateModalVisible(false);\n          actionRef.current?.reload();\n          return Promise.resolve(true);\n        }}\n      />\n      <SFooterToolbar\n        title={\"invoice\"}\n        confirmDescription=\"Drafted invoices can be deleted.\"\n        selectedRowsState={selectedRowsState}\n        setSelectedRows={setSelectedRows}\n        actionRef={actionRef}\n        handleRemove={(rows) => {\n          return deleteDispatchInvoice(selectedRowsState.map((row) => row.id)?.join(\",\"));\n        }}\n      />\n    </PageContainer>\n  );\n};\n\nexport default InvoiceListPage;\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/tenant/Invoice/index.tsx"}, "12": {"path": "/tenant/invoice/coupon", "name": "Coupons", "file": "@/pages/tenant/Coupon/index.tsx", "parentId": "9", "id": "12", "absPath": "/tenant/invoice/coupon", "__content": "import { deleteCoupon, getCouponListByPage } from \"@/services/app/tenant/coupon\";\nimport { DeleteOutlined, EditOutlined, PlusOutlined } from \"@ant-design/icons\";\nimport { ActionType, PageContainer, ProColumns, ProTable } from \"@ant-design/pro-components\";\nimport { Button, message, Popconfirm, Space, Tag } from \"antd\";\nimport { useRef, useState } from \"react\";\nimport CreateForm from \"./components/CreateForm\";\nimport UpdateForm from \"./components/UpdateForm\";\nimport UsageListModal from \"./components/UsageListModal\";\nimport Util from \"@/util\";\n\ntype RecordType = API.Coupon;\n\nconst CouponPage: React.FC = () => {\n  const actionRef = useRef<ActionType>(null);\n  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);\n  const [updateModalVisible, setUpdateModalVisible] = useState<boolean>(false);\n  const [usageModalVisible, setUsageModalVisible] = useState<boolean>(false);\n  const [currentRow, setCurrentRow] = useState<RecordType | undefined>();\n\n  const handleDelete = async (record: RecordType) => {\n    const hide = message.loading(\"Deleting...\", 0);\n    try {\n      await deleteCoupon(record.id!);\n      message.success(\"Deleted successfully\");\n      actionRef.current?.reload();\n    } catch (error) {\n      message.error(\"Delete failed\");\n    } finally {\n      hide();\n    }\n  };\n\n  const handleEdit = (record: RecordType) => {\n    setCurrentRow(record);\n    setUpdateModalVisible(true);\n  };\n\n  const handleCreate = () => {\n    setCreateModalVisible(true);\n  };\n\n  const handleUpdateModalClose = () => {\n    setUpdateModalVisible(false);\n    setCurrentRow(undefined);\n  };\n\n  const handleShowUsage = (record: RecordType) => {\n    setCurrentRow(record);\n    setUsageModalVisible(true);\n  };\n\n  const columns: ProColumns<RecordType>[] = [\n    {\n      title: \"Promo?\",\n      dataIndex: \"is_promo\",\n      width: 60,\n      ellipsis: true,\n      render(dom, entity, index, action, schema) {\n        return entity.is_promo ? <Tag color=\"gold-inverse\">Promo</Tag> : null;\n      },\n    },\n    {\n      title: \"Code\",\n      dataIndex: \"code\",\n      width: 120,\n      copyable: true,\n    },\n    {\n      title: \"Name\",\n      dataIndex: \"name\",\n      ellipsis: true,\n      width: 250,\n    },\n    {\n      title: \"Description\",\n      dataIndex: \"description\",\n      ellipsis: true,\n    },\n    {\n      title: \"Type\",\n      dataIndex: \"type\",\n      width: 100,\n      render: (_, record) => (\n        <Tag color={record.type === \"percentage\" ? \"blue\" : \"green\"}>{record.type === \"percentage\" ? \"Percentage\" : \"Fixed Amount\"}</Tag>\n      ),\n    },\n    {\n      title: \"Value\",\n      dataIndex: \"value\",\n      width: 100,\n      render: (_, record) => (record.type === \"percentage\" ? `${record.value}%` : `$${record.value}`),\n      onCell: (record) => {\n        return {\n          style: {\n            color: record.type === \"percentage\" ? \"blue\" : \"red\",\n            textAlign: record.type === \"percentage\" ? \"left\" : \"right\",\n          },\n        };\n      },\n    },\n    {\n      title: \"Usage\",\n      width: 100,\n      render: (_, record) => (\n        <Button type=\"link\" size=\"small\" onClick={() => handleShowUsage(record)} style={{ padding: 0 }}>\n          {record.used_count || 0}\n          {record.usage_limit ? `/${record.usage_limit}` : \"\"}\n        </Button>\n      ),\n    },\n    {\n      title: \"Status\",\n      dataIndex: \"status\",\n      width: 100,\n      render: (_, record) => {\n        const colors = { active: \"green\", inactive: \"orange\", expired: \"red\" };\n        return <Tag color={colors[record.status!]}>{record.status?.toUpperCase()}</Tag>;\n      },\n    },\n    {\n      title: \"Valid From\",\n      dataIndex: \"valid_from\",\n      width: 130,\n      valueType: \"dateTime\",\n      render: (_, record) => Util.dtToMDYHHMM(record.valid_from),\n    },\n    {\n      title: \"Valid Until\",\n      dataIndex: \"valid_until\",\n      width: 130,\n      valueType: \"dateTime\",\n      render: (_, record) => Util.dtToMDYHHMM(record.valid_until),\n    },\n    {\n      title: \"Actions\",\n      width: 120,\n      render: (_, record) => (\n        <Space>\n          <Button size=\"small\" icon={<EditOutlined />} onClick={() => handleEdit(record)} />\n          <Popconfirm title=\"Are you sure to delete this coupon?\" onConfirm={() => handleDelete(record)}>\n            <Button size=\"small\" danger icon={<DeleteOutlined />} />\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <PageContainer>\n      <ProTable<RecordType>\n        headerTitle=\"Coupons\"\n        actionRef={actionRef}\n        rowKey=\"id\"\n        search={false}\n        toolBarRender={() => [\n          <Button type=\"primary\" key=\"primary\" onClick={handleCreate}>\n            <PlusOutlined /> New Coupon\n          </Button>,\n        ]}\n        request={getCouponListByPage}\n        columns={columns}\n        columnEmptyText=\"\"\n      />\n\n      <CreateForm\n        visible={createModalVisible}\n        onVisibleChange={setCreateModalVisible}\n        onSuccess={() => {\n          actionRef.current?.reload();\n        }}\n      />\n\n      <UpdateForm\n        modalVisible={updateModalVisible}\n        handleModalVisible={setUpdateModalVisible}\n        initialValues={currentRow}\n        onSubmit={async () => {\n          actionRef.current?.reload();\n          return Promise.resolve(true);\n        }}\n      />\n\n      <UsageListModal visible={usageModalVisible} onVisibleChange={setUsageModalVisible} coupon={currentRow} />\n    </PageContainer>\n  );\n};\n\nexport default CouponPage;\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/tenant/Coupon/index.tsx"}, "13": {"path": "/tenant/invoice/customer", "name": "Top Customers", "file": "@/pages/tenant/Invoice/CustomerListPage.tsx", "parentId": "9", "id": "13", "absPath": "/tenant/invoice/customer", "__content": "import { DEFAULT_PER_PAGE_PAGINATION } from \"@/constants\";\r\nimport { getCustomerListByPage } from \"@/services/app/tenant/customer/customer\";\r\nimport Util, { nf2, ni, sn } from \"@/util\";\r\nimport {\r\n  ActionType,\r\n  PageContainer,\r\n  ProColumns,\r\n  ProForm,\r\n  ProFormDigit,\r\n  ProFormInstance,\r\n  ProFormSelect,\r\n  ProFormText,\r\n  ProTable,\r\n} from \"@ant-design/pro-components\";\r\nimport { Card, Space, Tag, Button, TagProps } from \"antd\";\r\nimport { useEffect, useRef, useState } from \"react\";\r\nimport { EditOutlined, EyeOutlined } from \"@ant-design/icons\";\r\n\r\ntype RecordType = API.Customer;\r\n\r\nconst CustomerListPage: React.FC = () => {\r\n  const actionRef = useRef<ActionType>(null);\r\n  const searchFormRef = useRef<ProFormInstance>(null);\r\n\r\n  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);\r\n  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);\r\n\r\n  const [currentRow, setCurrentRow] = useState<RecordType>();\r\n  const [selectedRowsState, setSelectedRows] = useState<RecordType[]>([]);\r\n  const [totalRow, setTotalRow] = useState<RecordType>();\r\n\r\n  const columns: ProColumns<RecordType>[] = [\r\n    {\r\n      title: \"Name\",\r\n      dataIndex: \"name\",\r\n      sorter: true,\r\n      width: 150,\r\n      ellipsis: true,\r\n      fixed: \"left\",\r\n    },\r\n    {\r\n      title: \"Email\",\r\n      dataIndex: \"email\",\r\n      sorter: true,\r\n      width: 200,\r\n      ellipsis: true,\r\n      copyable: true,\r\n    },\r\n    {\r\n      title: \"Phone\",\r\n      dataIndex: [\"latest_customer_phones\", \"phone\"],\r\n      width: 120,\r\n    },\r\n    {\r\n      title: \"Recent Address\",\r\n      dataIndex: [\"latest_address\", \"full_address\"],\r\n      ellipsis: true,\r\n      width: 400,\r\n    },\r\n    {\r\n      title: \"Inv. Count\",\r\n      dataIndex: \"invoice_count\",\r\n      width: 60,\r\n      align: \"right\",\r\n      ellipsis: true,\r\n      className: \"bl-2 b-gray\",\r\n      render: (_, record) => ni(record.invoices_count || 0),\r\n    },\r\n    {\r\n      title: \"Net Total\",\r\n      dataIndex: \"invoices_sum_subtotal\",\r\n      width: 120,\r\n      align: \"right\",\r\n      sorter: true,\r\n      render: (_, record) => (sn(record.invoices_sum_subtotal) > 0 ? `$${nf2(record.invoices_sum_subtotal || 0)}` : null),\r\n    },\r\n    {\r\n      title: \"Tax Total\",\r\n      dataIndex: \"invoices_sum_tax_amount\",\r\n      width: 120,\r\n      align: \"right\",\r\n      sorter: true,\r\n      render: (_, record) => (sn(record.invoices_sum_tax_amount) > 0 ? `$${nf2(record.invoices_sum_tax_amount || 0)}` : null),\r\n    },\r\n    {\r\n      title: \"Discount Total\",\r\n      dataIndex: \"invoices_sum_discount_amount\",\r\n      width: 120,\r\n      align: \"right\",\r\n      sorter: true,\r\n      render: (_, record) => (sn(record.invoices_sum_discount_amount) > 0 ? `$${nf2(record.invoices_sum_discount_amount || 0)}` : null),\r\n    },\r\n    {\r\n      title: \"Gross Total\",\r\n      dataIndex: \"invoices_sum_grand_total\",\r\n      width: 120,\r\n      align: \"right\",\r\n      sorter: true,\r\n      defaultSortOrder: \"descend\",\r\n      render: (_, record) => (sn(record.invoices_sum_grand_total) > 0 ? `$${nf2(record.invoices_sum_grand_total || 0)}` : null),\r\n    },\r\n    {\r\n      title: \"Source\",\r\n      dataIndex: \"source\",\r\n      width: 100,\r\n      render(dom, entity, index, action, schema) {\r\n        let color: TagProps[\"color\"] = \"default\";\r\n        if (entity.source === \"frontdoor\") {\r\n          color = \"blue\";\r\n        } else if (entity.source === \"development bulter\") {\r\n          color = \"cyan\";\r\n        }\r\n        return <Tag color={color}>{entity.source}</Tag>;\r\n      },\r\n    },\r\n    {\r\n      title: \"ENV\",\r\n      dataIndex: \"env\",\r\n      width: 100,\r\n      render(dom, entity, index, action, schema) {\r\n        return <Tag color={entity.env == \"production\" ? \"green\" : \"default\"}>{entity.env}</Tag>;\r\n      },\r\n    },\r\n    {\r\n      title: \"ID\",\r\n      dataIndex: \"id\",\r\n      sorter: true,\r\n      width: 80,\r\n      className: \"c-grey\",\r\n      align: \"center\",\r\n    },\r\n    {\r\n      title: \"\",\r\n      dataIndex: \"option\",\r\n      valueType: \"option\",\r\n    },\r\n    /* {\r\n      title: \"Actions\",\r\n      dataIndex: \"option\",\r\n      valueType: \"option\",\r\n      fixed: \"right\",\r\n      width: 100,\r\n      render: (_, record) => [\r\n        <Button\r\n          key=\"view\"\r\n          size=\"small\"\r\n          icon={<EyeOutlined />}\r\n          onClick={() => {\r\n            // Navigate to customer detail or invoices\r\n            window.open(`/tenant/invoice/list?customer_id=${record.id}`, '_blank');\r\n          }}\r\n        />,\r\n        <Button\r\n          key=\"edit\"\r\n          size=\"small\"\r\n          icon={<EditOutlined />}\r\n          onClick={() => {\r\n            handleUpdateModalVisible(true);\r\n            setCurrentRow(record);\r\n          }}\r\n        />,\r\n      ],\r\n    }, */\r\n  ];\r\n\r\n  useEffect(() => {\r\n    searchFormRef.current?.setFieldsValue(Util.getSfValues(\"customer_list\", {}));\r\n  }, []);\r\n\r\n  return (\r\n    <PageContainer>\r\n      <Card size=\"small\" style={{ marginBottom: 16 }}>\r\n        <ProForm\r\n          formRef={searchFormRef}\r\n          layout=\"inline\"\r\n          isKeyPressSubmit\r\n          submitter={{\r\n            resetButtonProps: { style: { marginLeft: \"auto\" } },\r\n            searchConfig: { submitText: \"Search\" },\r\n            submitButtonProps: { htmlType: \"submit\" },\r\n            onReset(value) {\r\n              searchFormRef.current?.resetFields();\r\n              actionRef.current?.reload();\r\n            },\r\n            onSubmit(value) {\r\n              actionRef.current?.reload();\r\n            },\r\n            render(props, dom) {\r\n              return (\r\n                <Space style={{ marginLeft: \"auto\" }} size={8}>\r\n                  {dom}\r\n                </Space>\r\n              );\r\n            },\r\n          }}\r\n        >\r\n          <ProFormText name=\"like_name\" label=\"Customer Name\" placeholder=\"Customer Name\" width={140} />\r\n          <ProFormText name=\"like_email\" label=\"Email\" width={140} />\r\n          <ProFormText name=\"phone\" label=\"Phone\" width={120} />\r\n          <ProFormText name=\"ft_keyWords\" placeholder=\"Search Customer / Address\" />\r\n          <ProFormText label=\"Job ID#\" name=\"dispatch_id\" width={120} />\r\n          <ProFormText label=\"Job External ID#\" name=\"dispatch_external_id\" width={120} />\r\n          <ProFormSelect name=\"source\" options={[\"frontdoor\", \"development butler\"]} />\r\n          <ProFormSelect name=\"env\" options={[\"sandbox\", \"production\"]} />\r\n        </ProForm>\r\n      </Card>\r\n\r\n      <ProTable<RecordType, API.PageParams>\r\n        headerTitle={\"Customers list\"}\r\n        actionRef={actionRef}\r\n        rowKey=\"id\"\r\n        revalidateOnFocus={false}\r\n        size=\"small\"\r\n        search={false}\r\n        scroll={{ x: 1400, y: 600 }}\r\n        sticky\r\n        pagination={{ defaultPageSize: DEFAULT_PER_PAGE_PAGINATION }}\r\n        request={async (params, sort, filter) => {\r\n          const sfValues = searchFormRef.current?.getFieldsValue();\r\n          Util.setSfValues(\"customer_list\", sfValues);\r\n\r\n          const newParams = {\r\n            ...sfValues,\r\n            ...params,\r\n            with: \"invoice_stats,latest_address,total_row,latest_customer_phones\",\r\n          };\r\n          return getCustomerListByPage(newParams, sort, filter).then((res) => {\r\n            setTotalRow(res.total_row);\r\n            return res;\r\n          });\r\n        }}\r\n        onRequestError={Util.error}\r\n        columns={columns}\r\n        summary={(dataParam) => {\r\n          const totalRowLocal: any = totalRow ?? {};\r\n\r\n          return (\r\n            <ProTable.Summary fixed=\"top\">\r\n              <ProTable.Summary.Row style={{ fontWeight: \"bold\" }}>\r\n                <ProTable.Summary.Cell index={-1} className=\"bg-green1\"></ProTable.Summary.Cell>\r\n                {columns\r\n                  .filter((c) => !c.hideInTable)\r\n                  .map((c, index) => {\r\n                    const tmpKey = typeof c.dataIndex === \"string\" ? c.dataIndex : ((c.dataIndex || []) as any[]).join(\".\");\r\n                    let value: any = null;\r\n                    let align: any = \"left\";\r\n                    let prefix = \"\";\r\n\r\n                    if (index === 0) value = <span>Total</span>;\r\n                    else if (tmpKey === \"invoices_count\") {\r\n                      value = ni(totalRowLocal[tmpKey] || 0);\r\n                      align = \"right\";\r\n                    } else if (\r\n                      tmpKey === \"invoices_sum_grand_total\" ||\r\n                      tmpKey === \"invoices_sum_subtotal\" ||\r\n                      tmpKey === \"invoices_sum_discount_amount\"\r\n                    ) {\r\n                      value = nf2(totalRowLocal[tmpKey] || 0);\r\n                      align = \"right\";\r\n                      if (totalRowLocal[tmpKey]) {\r\n                        prefix = \"$\";\r\n                      }\r\n                    }\r\n\r\n                    const cls = `bg-green1 ${c.className || \"\"}`;\r\n                    return (\r\n                      <ProTable.Summary.Cell key={tmpKey} index={index} align={align} className={cls}>\r\n                        {prefix}\r\n                        {value}\r\n                      </ProTable.Summary.Cell>\r\n                    );\r\n                  })}\r\n              </ProTable.Summary.Row>\r\n            </ProTable.Summary>\r\n          );\r\n        }}\r\n        rowSelection={{\r\n          onChange: (_, selectedRows) => {\r\n            setSelectedRows(selectedRows);\r\n          },\r\n        }}\r\n        tableAlertRender={false}\r\n        columnEmptyText=\"\"\r\n        locale={{ emptyText: <></> }}\r\n      />\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default CustomerListPage;\r\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/tenant/Invoice/CustomerListPage.tsx"}, "14": {"path": "/tenant/pricebook", "name": "Pricebook", "parentId": "ant-design-pro-layout", "id": "14", "absPath": "/tenant/pricebook"}, "15": {"path": "/tenant/pricebook/service", "name": "Services", "external": true, "parentId": "14", "id": "15", "absPath": "/tenant/pricebook/service"}, "16": {"path": "/tenant/pricebook/service-category", "name": "Service Category", "external": true, "parentId": "14", "id": "16", "absPath": "/tenant/pricebook/service-category"}, "17": {"path": "tenant/employee", "name": "Employees", "parentId": "ant-design-pro-layout", "id": "17", "absPath": "/tenant/employee"}, "18": {"path": "/tenant/employee/list", "name": "Employees List", "external": true, "parentId": "17", "id": "18", "absPath": "/tenant/employee/list"}, "19": {"path": "/tenant/employee/add", "name": "New Employees", "external": true, "parentId": "17", "id": "19", "absPath": "/tenant/employee/add"}, "20": {"path": "/tenant/employee/roles", "name": "Employee Roles List", "external": true, "parentId": "17", "id": "20", "absPath": "/tenant/employee/roles"}, "21": {"path": "/tenant/driver", "name": "Driver / Device", "parentId": "ant-design-pro-layout", "id": "21", "absPath": "/tenant/driver"}, "22": {"path": "/tenant/driver/device/list", "name": "Devices List", "file": "@/pages/tenant/driver/DeviceList/index.tsx", "parentId": "21", "id": "22", "absPath": "/tenant/driver/device/list", "__content": "import React, { useRef, useState } from \"react\";\nimport { <PERSON>ton, <PERSON>, Space, Tag, Typography, message, Modal, Input, Row, Col } from \"antd\";\nimport { ActionType, PageContainer, ProColumns, ProTable, ProForm, ProFormCheckbox, ProFormText } from \"@ant-design/pro-components\";\nimport { EditOutlined, SyncOutlined, UserOutlined } from \"@ant-design/icons\";\nimport Util from \"@/util\";\nimport { DEFAULT_PER_PAGE_PAGINATION } from \"@/constants\";\nimport { getDriverDeviceListByPage, updateDeviceDisplayName, syncDevices } from \"@/services/app/tenant/gps/driver-device\";\nimport HomeAddressModal from \"./components/HomeAddressModal\";\n\nconst { Text } = Typography;\n\ntype RecordType = API.DriverDevice;\n\nconst DeviceList: React.FC = () => {\n  const actionRef = useRef<ActionType>(null);\n  const searchFormRef = useRef<any>(null);\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [selectedDeviceId, setSelectedDeviceId] = useState<number>();\n  const [currentDevice, setCurrentDevice] = useState<RecordType | null>(null);\n  const [displayName, setDisplayName] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n\n  const handleEdit = (record: RecordType) => {\n    setCurrentDevice(record);\n    setDisplayName(record.display_name || \"\");\n    setEditModalVisible(true);\n  };\n\n  const handleUpdate = async () => {\n    if (!currentDevice || !displayName.trim()) {\n      message.error(\"Display name is required\");\n      return;\n    }\n\n    setLoading(true);\n    try {\n      await updateDeviceDisplayName(currentDevice.device_sid || \"\", displayName.trim());\n      message.success(\"Device updated successfully\");\n      setEditModalVisible(false);\n      actionRef.current?.reload();\n    } catch (error) {\n      Util.error(error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSync = async () => {\n    setLoading(true);\n    try {\n      await syncDevices();\n      message.success(\"Devices synced successfully\");\n      actionRef.current?.reload();\n    } catch (error) {\n      Util.error(error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const columns: ProColumns<RecordType>[] = [\n    {\n      title: \"ID\",\n      dataIndex: \"id\",\n      width: 80,\n      align: \"center\",\n    },\n    {\n      title: \"Employee\",\n      dataIndex: [\"user\", \"user_name\"],\n      width: 120,\n      render: (_, record) => (\n        <Space>\n          <UserOutlined />\n          <Text>{record.user?.user_name || \"-\"}</Text>\n        </Space>\n      ),\n    },\n    {\n      title: \"Display Name\",\n      dataIndex: \"display_name\",\n      width: 200,\n      render: (_, record) => (\n        <Row>\n          <Col flex=\"auto\">\n            <Text>{record.display_name}</Text>\n          </Col>\n          <Col flex=\"20px\">\n            <Button type=\"link\" size=\"small\" icon={<EditOutlined />} onClick={() => handleEdit(record)} />\n          </Col>\n        </Row>\n      ),\n    },\n    {\n      title: \"Device ID\",\n      dataIndex: \"device_sid\",\n      width: 170,\n    },\n    {\n      title: \"Factory ID\",\n      dataIndex: \"factory_id\",\n      width: 120,\n    },\n    {\n      title: \"Make\",\n      dataIndex: \"make\",\n      width: 100,\n    },\n    {\n      title: \"Model\",\n      dataIndex: \"model\",\n      width: 120,\n    },\n    {\n      title: \"Status\",\n      dataIndex: \"active_state\",\n      width: 100,\n      align: \"center\",\n      render: (dom, record) => <Tag color={record.active_state === \"active\" ? \"green\" : \"red\"}>{record.active_state || \"Unknown\"}</Tag>,\n    },\n    {\n      title: \"Last Status\",\n      dataIndex: \"last_status\",\n      width: 120,\n      align: \"center\",\n      render: (dom, record) => {\n        if (record.last_position?.drive_status) {\n          const colors: any = { driving: \"green\", idle: \"blue\", parked: \"red\", \"-\": \"default\" };\n          return <Tag color={colors[record.last_position?.drive_status || \"-\"]}>{record.last_position?.drive_status || \"-\"}</Tag>;\n        }\n        return null;\n      },\n    },\n    {\n      title: \"Last Status Period\",\n      dataIndex: \"last_status_period\",\n      width: 150,\n      align: \"right\",\n      render(__, record) {\n        const lp = record.last_position;\n        if (lp && lp.drive_status_duration_value) {\n          return Util.secondsToDhms(lp.drive_status_duration_value);\n        }\n        return null;\n      },\n    },\n    {\n      title: \"Last Position\",\n      dataIndex: \"last_position\",\n      width: 200,\n      render: (_, record) =>\n        record.last_position ? (\n          <Text type=\"secondary\">\n            {record.last_position.lat}, {record.last_position.lng}\n          </Text>\n        ) : (\n          \"-\"\n        ),\n    },\n    {\n      title: \"Actions\",\n      dataIndex: \"option\",\n      valueType: \"option\",\n      width: 120,\n      render: (_, record) => [\n        <Button\n          key=\"edit\"\n          size=\"small\"\n          icon={<EditOutlined />}\n          onClick={() => {\n            setSelectedDeviceId(record.id);\n            setEditModalVisible(true);\n          }}\n        >\n          Edit\n        </Button>,\n      ],\n    },\n  ];\n\n  return (\n    <PageContainer>\n      <Card size=\"small\" style={{ marginBottom: 16 }}>\n        <ProForm\n          formRef={searchFormRef}\n          layout=\"inline\"\n          submitter={{\n            resetButtonProps: { style: { marginLeft: \"auto\" } },\n            searchConfig: { submitText: \"Search\" },\n            submitButtonProps: { htmlType: \"submit\" },\n            onReset() {\n              searchFormRef.current?.resetFields();\n              actionRef.current?.reload();\n            },\n            onSubmit() {\n              actionRef.current?.reload();\n            },\n            render(props, dom) {\n              return (\n                <Space style={{ marginLeft: \"auto\" }} size={8}>\n                  {dom}\n                </Space>\n              );\n            },\n          }}\n        >\n          <ProFormCheckbox\n            name=\"systemUserOnly\"\n            fieldProps={{\n              onChange: () => actionRef.current?.reload(),\n            }}\n          >\n            Assigned Only\n          </ProFormCheckbox>\n          <ProFormText name=\"display_name\" label=\"Display Name\" placeholder=\"Search Display Name\" width={200} />\n        </ProForm>\n      </Card>\n\n      <ProTable<RecordType, API.PageParams>\n        headerTitle=\"Devices List\"\n        actionRef={actionRef}\n        rowKey=\"id\"\n        search={false}\n        size=\"small\"\n        scroll={{ x: 1200 }}\n        toolBarRender={() => [\n          <Button key=\"sync\" type=\"primary\" icon={<SyncOutlined />} onClick={handleSync} loading={loading}>\n            Down Sync\n          </Button>,\n        ]}\n        pagination={{ defaultPageSize: DEFAULT_PER_PAGE_PAGINATION }}\n        request={(params, sort, filter) => {\n          const sfValues = searchFormRef.current?.getFieldsValue();\n\n          return getDriverDeviceListByPage(\n            {\n              ...sfValues,\n              ...params,\n              with: \"lastPosition,user\",\n            },\n            sort,\n            filter,\n          );\n        }}\n        onRequestError={Util.error}\n        columns={columns}\n        columnEmptyText=\"-\"\n      />\n\n      <HomeAddressModal\n        visible={editModalVisible}\n        onCancel={() => setEditModalVisible(false)}\n        onSuccess={() => {\n          actionRef.current?.reload();\n        }}\n        deviceId={selectedDeviceId}\n      />\n    </PageContainer>\n  );\n};\n\nexport default DeviceList;\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/tenant/driver/DeviceList/index.tsx"}, "23": {"path": "/tenant/integration", "name": "Integration", "parentId": "ant-design-pro-layout", "id": "23", "absPath": "/tenant/integration"}, "24": {"path": "/tenant/integration/twilio", "name": "<PERSON><PERSON><PERSON>", "external": true, "parentId": "23", "id": "24", "absPath": "/tenant/integration/twilio"}, "25": {"path": "/tenant/gps", "name": "GPS", "parentId": "ant-design-pro-layout", "id": "25", "absPath": "/tenant/gps"}, "26": {"path": "/tenant/gps", "redirect": "/tenant/gps/job-board", "parentId": "25", "id": "26", "absPath": "/tenant/gps"}, "27": {"path": "/tenant/gps/job-board", "name": "GPS Job Board", "file": "@/pages/tenant/gps/GpsJobBoard/index.tsx", "parentId": "25", "id": "27", "absPath": "/tenant/gps/job-board", "__content": "import { <PERSON><PERSON>, <PERSON>, Col, List, Row, Space, Splitter, Tag, Typography } from \"antd\";\r\nimport React, { useState, useRef, useCallback, useEffect, useMemo } from \"react\";\r\nimport { PageContainer } from \"@ant-design/pro-layout\";\r\nimport type { ActionType } from \"@ant-design/pro-table\";\r\nimport Util, { isNumeric } from \"@/util\";\r\nimport { ProForm, ProFormInstance, ProFormSelect, ProFormText, ProList } from \"@ant-design/pro-components\";\r\nimport styles from \"./index.less\";\r\nimport { getDriverDeviceListByPage } from \"@/services/app/tenant/gps/driver-device\";\r\nimport { CardProps } from \"antd/lib\";\r\nimport { Link } from \"@umijs/max\";\r\nimport UpdateNotesForm from \"./components/UpdateNotesForm\";\r\nimport { SyncOutlined } from \"@ant-design/icons\";\r\nimport { getGpsDispatchBoardListByPage } from \"@/services/app/tenant/gps/gps-dispatch-board\";\r\n\r\nfunction agoString(iso: any, dt?: string) {\r\n  const diff = iso;\r\n  if (diff && isNumeric(diff)) {\r\n    if (diff < 60) return \"just now\";\r\n    if (diff < 3600) return Math.floor(diff / 60) + \" min ago\";\r\n    if (diff < 14400) return Math.floor(diff / 3600) + \" hr ago\";\r\n\r\n    const hrs = Math.floor(diff / 3600);\r\n    if (hrs > 24) {\r\n      return `${Math.floor(hrs / 24)}d ${hrs % 24} hr ago`;\r\n    } else {\r\n      return `${hrs} hr ago`;\r\n    }\r\n  }\r\n\r\n  if (dt) return new Date(dt).toLocaleString();\r\n}\r\n\r\nconst CardPropsInSideBar: CardProps = {\r\n  size: \"small\",\r\n  variant: \"borderless\",\r\n  styles: {\r\n    extra: { alignSelf: \"flex-start\", paddingTop: 4 },\r\n    body: { maxHeight: \"calc(100vh - 140px)\", overflowY: \"auto\", paddingRight: 0 },\r\n  },\r\n  style: { borderRadius: 0, height: \"100%\" },\r\n};\r\n\r\nconst GpsJobBoard: React.FC = () => {\r\n  const formRef = useRef<ProFormInstance>(null);\r\n  const actionRef = useRef<ActionType>(null);\r\n\r\n  const [currentRow, setCurrentRow] = useState<API.GpsDispatchBoard>();\r\n  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);\r\n\r\n  const [loadingDevices, setLoadingDevices] = useState<boolean>(false);\r\n  const [devices, setDevices] = useState<API.DriverDevice[]>([]);\r\n\r\n  const loadDevices = useCallback(() => {\r\n    setLoadingDevices(true);\r\n    getDriverDeviceListByPage({ pageSize: 500, with: \"lastPosition\" })\r\n      .then((res) => {\r\n        setDevices(res.data);\r\n      })\r\n      .catch(Util.error)\r\n      .finally(() => {\r\n        setLoadingDevices(false);\r\n      });\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    loadDevices();\r\n  }, [loadDevices]);\r\n\r\n  const activeDevices = useMemo(() => {\r\n    return devices.filter((x) => x.last_position?.is_active == 1);\r\n  }, [devices]);\r\n\r\n  const inactiveDevices = useMemo(() => {\r\n    return devices.filter((x) => x.last_position?.is_active != 1);\r\n  }, [devices]);\r\n\r\n  const devicesOptions = useMemo(() => {\r\n    return devices.map((x) => ({ id: x.id, value: x.id, label: x.display_name }));\r\n  }, [devices]);\r\n\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      loadDevices();\r\n    }, 10000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [loadDevices]);\r\n\r\n  return (\r\n    <PageContainer title={false} className={styles.gpsJobBoard}>\r\n      <Splitter layout=\"horizontal\">\r\n        <Splitter.Panel defaultSize={280} max={280} collapsible style={{ height: \"calc(100vh - 80px)\" }}>\r\n          <Card\r\n            {...{\r\n              ...CardPropsInSideBar,\r\n              styles: {\r\n                ...CardPropsInSideBar.styles,\r\n                body: { maxHeight: \"calc(100vh - 160px)\", overflowY: \"auto\", paddingRight: 0 },\r\n              },\r\n            }}\r\n            title={\r\n              <>\r\n                <h2>Active Drivers</h2>\r\n                <div className=\"color-key\" style={{ display: \"flex\", alignItems: \"center\" }}>\r\n                  <span className=\"key-dot green\"></span> &lt;2min\r\n                  <span className=\"key-dot gray\"></span> 2–60min\r\n                  <span className=\"key-dot red\"></span> 1–4hr\r\n                  <svg className=\"home-icon-svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 16 16\">\r\n                    <path d=\"M8.707 1.5a1 1 0 0 0-1.414 0L.646 8.354a.5.5 0 0 0 .708.708L8 2.207l6.646 6.853a.5.5 0 0 0 .708-.708L8.707 1.5Z\" />\r\n                    <path d=\"m8 3.293 6 6V13.5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 13.5V9.293l6-6Z\" />\r\n                  </svg>{\" \"}\r\n                  Home\r\n                </div>\r\n              </>\r\n            }\r\n            className=\"sidebarCard\"\r\n            extra={<SyncOutlined spin style={{ visibility: loadingDevices ? \"visible\" : \"hidden\" }} />}\r\n          >\r\n            <ProList\r\n              dataSource={activeDevices}\r\n              size=\"small\"\r\n              renderItem={(item) => {\r\n                return (\r\n                  <List.Item key={item.id}>\r\n                    <Row gutter={4} wrap={false} style={{ width: \"100%\", height: \"100%\" }}>\r\n                      <Col flex=\"24px\">\r\n                        <span className=\"driver-dot green\"></span>\r\n                      </Col>\r\n                      <Col flex=\"120px\">\r\n                        <Typography.Text ellipsis>{item.display_name}</Typography.Text>\r\n                      </Col>\r\n                      <Col flex=\"auto\" style={{ textAlign: \"right\", fontSize: \"85%\" }} className=\"text-sm c-grey\">\r\n                        {agoString(item.last_position?.drive_status_duration_value, item.last_position?.dt_server)}\r\n                      </Col>\r\n                    </Row>\r\n                  </List.Item>\r\n                );\r\n              }}\r\n            />\r\n          </Card>\r\n        </Splitter.Panel>\r\n        <Splitter.Panel>\r\n          <div style={{ height: \"100%\" }}>\r\n            <Card\r\n              size=\"small\"\r\n              title={\r\n                <>\r\n                  <h2>Live Job Board</h2>\r\n                </>\r\n              }\r\n              variant=\"borderless\"\r\n              styles={{}}\r\n              style={{ borderRadius: 0, height: \"100%\" }}\r\n              extra={\r\n                <Space size={16}>\r\n                  <Button type=\"primary\" ghost>\r\n                    View/Edit Notes\r\n                  </Button>\r\n                  <Button type=\"primary\" ghost>\r\n                    View Map\r\n                  </Button>\r\n                  {/* <Button type=\"primary\" ghost>\r\n                    Driver Report\r\n                  </Button> */}\r\n                  <Link to={\"/tenant/gps/job-notes-management\"}>Management Page</Link>\r\n                  <Link to={\"/tenant/gps/statuses\"}>Control Panel</Link>\r\n                </Space>\r\n              }\r\n            >\r\n              <ProForm formRef={formRef} layout=\"horizontal\" grid submitter={false}>\r\n                <ProFormText name=\"\" colProps={{ span: 13 }} placeholder={\"Search by Customer Name...\"} />\r\n                <ProFormSelect name=\"device\" colProps={{ span: 4 }} options={[{ value: \"\", label: \"All Drivers\" }, ...devicesOptions]} />\r\n                <ProFormSelect name=\"\" colProps={{ span: 4 }} options={[{ value: \"\", label: \"All Statuses\" }]} />\r\n                <Col span=\"3\">\r\n                  <Button style={{ marginRight: 8 }}>Reset</Button>\r\n                  <Button\r\n                    type=\"primary\"\r\n                    onClick={() => {\r\n                      actionRef.current?.reload();\r\n                    }}\r\n                  >\r\n                    Search\r\n                  </Button>\r\n                </Col>\r\n              </ProForm>\r\n\r\n              <ProList\r\n                grid={{ gutter: 16, xs: 1, sm: 2, md: 3, lg: 3, xl: 4, xxl: 4 }}\r\n                itemCardProps={{ style: { height: \"100%\" } }}\r\n                cardProps={{ style: { height: \"100%\" } }}\r\n                actionRef={actionRef}\r\n                className=\"cardList\"\r\n                pagination={{ defaultPageSize: 180, hideOnSinglePage: false }}\r\n                polling={8000}\r\n                request={(params) => {\r\n                  const sfValues = Util.getSfValues(\"sf_gps_job_board\", {});\r\n                  const newParams = { ...sfValues, ...params, with: \"dispatch,driverDevice\" };\r\n\r\n                  // return getDispatchListByPage(newParams);\r\n                  return getGpsDispatchBoardListByPage(newParams);\r\n                }}\r\n                renderItem={(item) => (\r\n                  <List.Item\r\n                    onClick={(e) => {\r\n                      console.log(\"itemClick\", e);\r\n                      setCurrentRow(item);\r\n                      handleUpdateModalVisible(true);\r\n                    }}\r\n                  >\r\n                    <Card\r\n                      title={item.customer_full_name}\r\n                      size=\"small\"\r\n                      className=\"cardItem cursor-pointer\"\r\n                      extra={\r\n                        <Space size={0}>\r\n                          <Tag color=\"geekblue\">{item.dispatch?.env}</Tag>\r\n                          <Tag color=\"magenta\">{item.dispatch?.job_source}</Tag>\r\n                        </Space>\r\n                      }\r\n                    >\r\n                      <div>\r\n                        <label>Address:</label>\r\n                        <div>{item.customer_full_address}</div>\r\n                      </div>\r\n                      <Space style={{ width: \"100%\" }}>\r\n                        <label style={{ width: 100 }}>ID#</label>\r\n                        <div>{item.dispatch_id}</div>\r\n                      </Space>\r\n                      <Space style={{ width: \"100%\" }}>\r\n                        <label style={{ width: 100 }}>Ext. ID#</label>\r\n                        <div>{item.dispatch?.external_id}</div>\r\n                      </Space>\r\n                    </Card>\r\n                  </List.Item>\r\n                )}\r\n              />\r\n            </Card>\r\n          </div>\r\n        </Splitter.Panel>\r\n        <Splitter.Panel defaultSize={280} max={280} collapsible>\r\n          <Card\r\n            {...{\r\n              ...CardPropsInSideBar,\r\n              styles: {\r\n                body: { maxHeight: \"calc(100vh - 160px)\", overflowY: \"auto\", paddingRight: 0 },\r\n              },\r\n            }}\r\n            title={\r\n              <>\r\n                <h2>Inactive Drivers</h2>\r\n                <div className=\"color-key\" style={{ display: \"flex\", alignItems: \"center\" }}>\r\n                  <span className=\"driver-dot idle\"></span> Idle&nbsp;&nbsp;\r\n                  <span className=\"key-dot red\"></span> Off&nbsp;&nbsp;\r\n                  <svg className=\"home-icon-svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 16 16\">\r\n                    <path d=\"M8.707 1.5a1 1 0 0 0-1.414 0L.646 8.354a.5.5 0 0 0 .708.708L8 2.207l6.646 6.853a.5.5 0 0 0 .708-.708L8.707 1.5Z\" />\r\n                    <path d=\"m8 3.293 6 6V13.5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 13.5V9.293l6-6Z\" />\r\n                  </svg>\r\n                  &nbsp; Home\r\n                </div>\r\n              </>\r\n            }\r\n            className=\"sidebarCard\"\r\n            extra={<SyncOutlined spin style={{ visibility: loadingDevices ? \"visible\" : \"hidden\" }} />}\r\n          >\r\n            <ProList\r\n              dataSource={inactiveDevices}\r\n              size=\"small\"\r\n              renderItem={(item) => {\r\n                let statusCls = \"driver-dot\";\r\n                if (item.last_position?.drive_status == \"off\") {\r\n                  statusCls += \" red\";\r\n                } else {\r\n                  statusCls += \" idle\";\r\n                }\r\n                return (\r\n                  <List.Item key={item.id}>\r\n                    <Row gutter={4} wrap={false} style={{ width: \"100%\", height: \"100%\" }}>\r\n                      <Col flex=\"24px\">\r\n                        <span className={statusCls}></span>\r\n                      </Col>\r\n                      <Col flex=\"120px\">\r\n                        <Typography.Text ellipsis>{item.display_name}</Typography.Text>\r\n                      </Col>\r\n                      <Col flex=\"auto\" style={{ textAlign: \"right\", fontSize: \"85%\" }} className=\"text-sm c-grey\">\r\n                        {agoString(item.last_position?.drive_status_duration_value, item.last_position?.dt_server)}\r\n                      </Col>\r\n                    </Row>\r\n                  </List.Item>\r\n                );\r\n              }}\r\n            />\r\n          </Card>\r\n        </Splitter.Panel>\r\n      </Splitter>\r\n\r\n      {currentRow && currentRow.dispatch ? (\r\n        <UpdateNotesForm\r\n          modalVisible={updateModalVisible}\r\n          handleModalVisible={handleUpdateModalVisible}\r\n          dispatch={currentRow.dispatch}\r\n          devices={devicesOptions}\r\n          onSubmit={(data) => {\r\n            actionRef.current?.reload();\r\n          }}\r\n        />\r\n      ) : null}\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default GpsJobBoard;\r\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/tenant/gps/GpsJobBoard/index.tsx"}, "28": {"path": "/tenant/gps/job-notes-management", "name": "Job Notes Management", "file": "@/pages/tenant/gps/GpsDispatchNoteList/index.tsx", "parentId": "25", "id": "28", "absPath": "/tenant/gps/job-notes-management", "__content": "import { getGpsDispatchNoteListByPage, updateGpsDispatchNote } from \"@/services/app/tenant/gps/gps-dispatch-notes\";\r\nimport Util, { sn } from \"@/util\";\r\nimport {\r\n  ActionType,\r\n  PageContainer,\r\n  ProColumns,\r\n  ProForm,\r\n  ProFormDatePicker,\r\n  ProFormInstance,\r\n  ProFormSelect,\r\n  ProFormText,\r\n  ProTable,\r\n} from \"@ant-design/pro-components\";\r\nimport { Link } from \"@umijs/max\";\r\nimport { Button, Card, message } from \"antd\";\r\nimport { useEffect, useRef } from \"react\";\r\nimport useGpsJobStatusOptions from \"../GpsJobBoard/hooks/useGpsJobStatusOptions\";\r\nimport { DefaultOptionType } from \"antd/es/select\";\r\nimport useDriverDeviceOptions from \"../GpsJobBoard/hooks/useDriverDeviceOptions\";\r\nimport styles from \"./index.less\";\r\nimport { DEFAULT_PER_PAGE_PAGINATION } from \"@/constants\";\r\n\r\nexport const ViewedOptions: DefaultOptionType[] = [\r\n  { value: \"new\", label: \"New\" },\r\n  { value: \"working\", label: \"Working\" },\r\n  { value: \"submitted\", label: \"Submitted\" },\r\n];\r\n\r\ntype RecordType = API.GpsDispatchNote;\r\nconst isShaded = (entity: RecordType) => entity.submitted || entity.acknowledged == \"submitted\";\r\n\r\nconst GpsDispatchNoteList: React.FC = () => {\r\n  const actionRef = useRef<ActionType>(null);\r\n  const searchFormRef = useRef<ProFormInstance>(null);\r\n\r\n  const { statusOptions, statusOptionsKv } = useGpsJobStatusOptions();\r\n  const { driverDeviceOptions } = useDriverDeviceOptions();\r\n\r\n  const defaultColumnProps: ProColumns<RecordType> = {\r\n    onCell: (entity) => {\r\n      const job_status = entity.job_status;\r\n      return {\r\n        style: {\r\n          background: isShaded(entity) ? \"auto\" : (statusOptionsKv?.[entity.job_status ?? \"\"]?.color_hex ?? \"auto\"),\r\n        },\r\n      };\r\n    },\r\n  };\r\n\r\n  const columns: ProColumns<RecordType>[] = [\r\n    {\r\n      title: \"Call By Call Date\",\r\n      dataIndex: \"note_date\",\r\n      width: 100,\r\n      ellipsis: true,\r\n      align: \"center\",\r\n      render(dom, entity) {\r\n        return Util.dtToMDY(entity.note_date);\r\n      },\r\n      ...defaultColumnProps,\r\n    },\r\n    {\r\n      title: \"Address\",\r\n      dataIndex: \"customer_full_address\",\r\n      width: 400,\r\n      ...defaultColumnProps,\r\n    },\r\n    {\r\n      title: \"Address/WO #\",\r\n      dataIndex: [\"dispatch\", \"external_id\"],\r\n      align: \"center\",\r\n      width: 120,\r\n      render(dom, entity, index, action, schema) {\r\n        const dispatch = entity.dispatch;\r\n        return dispatch && dispatch.external_id ? (\r\n          <a href={`https://work.dispatch.me/job/${dispatch.external_id}`} target=\"_blank\" rel=\"noreferrer\">\r\n            {dispatch.external_id}\r\n          </a>\r\n        ) : null;\r\n      },\r\n      ...defaultColumnProps,\r\n    },\r\n    {\r\n      title: \"Tech\",\r\n      dataIndex: \"driver_name\",\r\n      ellipsis: true,\r\n      width: 150,\r\n      ...defaultColumnProps,\r\n    },\r\n    {\r\n      title: \"Issue / Notes\",\r\n      dataIndex: \"issue_note\",\r\n      ellipsis: true,\r\n      width: 450,\r\n      ...defaultColumnProps,\r\n    },\r\n    {\r\n      title: \"Pics\",\r\n      dataIndex: \"has_pics\",\r\n      align: \"center\",\r\n      width: 50,\r\n      ...defaultColumnProps,\r\n    },\r\n    {\r\n      title: \"Inc. / Missing\",\r\n      dataIndex: \"job_status\",\r\n      width: 150,\r\n      render(dom, entity) {\r\n        return (\r\n          <ProFormSelect\r\n            name=\"job_status\"\r\n            fieldProps={{ defaultValue: entity.job_status, popupMatchSelectWidth: false }}\r\n            formItemProps={{ style: { marginBottom: 0 } }}\r\n            options={statusOptions}\r\n            placeholder={\"Job Status\"}\r\n            onChange={(value) => {\r\n              updateGpsDispatchNote(sn(entity.id), { job_status: value })\r\n                .then((res) => {\r\n                  message.success(\"Updated successfully.\");\r\n                  actionRef.current?.reload();\r\n                })\r\n                .catch(Util.error);\r\n            }}\r\n          />\r\n        );\r\n      },\r\n      ...defaultColumnProps,\r\n    },\r\n    {\r\n      title: \"Viewed\",\r\n      dataIndex: \"acknowledged\",\r\n      width: 120,\r\n      render(dom, entity) {\r\n        return (\r\n          <ProFormSelect\r\n            fieldProps={{ defaultValue: entity.acknowledged, popupMatchSelectWidth: false }}\r\n            formItemProps={{ style: { marginBottom: 0 } }}\r\n            options={ViewedOptions}\r\n            placeholder={\"\"}\r\n            onChange={(value) => {\r\n              updateGpsDispatchNote(sn(entity.id), { acknowledged: value })\r\n                .then((res) => {\r\n                  message.success(\"Updated successfully.\");\r\n                  actionRef.current?.reload();\r\n                })\r\n                .catch(Util.error);\r\n            }}\r\n          />\r\n        );\r\n      },\r\n      ...defaultColumnProps,\r\n    },\r\n\r\n    {\r\n      title: \"Request authorization\",\r\n      dataIndex: \"submitted\",\r\n      width: 200,\r\n      align: \"center\",\r\n      render(__, entity) {\r\n        return (\r\n          <Button\r\n            type=\"primary\"\r\n            disabled={!!entity.submitted}\r\n            onClick={(e) => {\r\n              const hide = message.loading(\"Requesting authorization...\", 0);\r\n              updateGpsDispatchNote(sn(entity.id), { submitted: 1 })\r\n                .then((res) => {\r\n                  message.success(\"Authorization has been sent successfully.\");\r\n                  actionRef.current?.reload();\r\n                })\r\n                .catch(Util.error)\r\n                .finally(() => {\r\n                  hide();\r\n                });\r\n            }}\r\n          >\r\n            {entity.submitted ? \"Authorization Sent\" : \"Request authorization\"}\r\n          </Button>\r\n        );\r\n      },\r\n      ...defaultColumnProps,\r\n    },\r\n    {\r\n      title: \"ID\",\r\n      dataIndex: \"id\",\r\n      ...defaultColumnProps,\r\n    },\r\n  ];\r\n\r\n  useEffect(() => {\r\n    searchFormRef.current?.setFieldsValue(Util.getSfValues(\"gps_notes_list\", {}));\r\n  }, []);\r\n\r\n  return (\r\n    <PageContainer\r\n      className={styles.gpsDispatchNoteList}\r\n      extra={\r\n        <>\r\n          <Link to={\"/tenant/gps/job-board\"}>Back to Job Board</Link>\r\n        </>\r\n      }\r\n    >\r\n      <Card size=\"small\">\r\n        <ProForm formRef={searchFormRef} layout=\"vertical\" grid submitter={false}>\r\n          <ProFormSelect\r\n            name=\"acknowledged\"\r\n            label=\"Viewed\"\r\n            colProps={{ flex: \"100px\" }}\r\n            options={ViewedOptions}\r\n            placeholder={\"All\"}\r\n            formItemProps={{ style: { marginBottom: 4 } }}\r\n            fieldProps={{\r\n              onChange(value, option) {\r\n                actionRef.current?.reload();\r\n              },\r\n            }}\r\n          />\r\n          <ProFormSelect\r\n            name=\"has_pics\"\r\n            label=\"Pics\"\r\n            colProps={{ flex: \"100px\" }}\r\n            options={[\"Yes\", \"No\"]}\r\n            placeholder={\"All\"}\r\n            formItemProps={{ style: { marginBottom: 4 } }}\r\n            fieldProps={{\r\n              onChange(value, option) {\r\n                actionRef.current?.reload();\r\n              },\r\n            }}\r\n          />\r\n          <ProFormSelect\r\n            name=\"job_status\"\r\n            label=\"Inc. / Missing\"\r\n            showSearch\r\n            allowClear\r\n            colProps={{ flex: \"170px\" }}\r\n            options={statusOptions}\r\n            placeholder={\"All\"}\r\n            formItemProps={{ style: { marginBottom: 4 } }}\r\n            fieldProps={{\r\n              onChange(value, option) {\r\n                actionRef.current?.reload();\r\n              },\r\n            }}\r\n          />\r\n          <ProFormSelect\r\n            name=\"device_id\"\r\n            label=\"Driver\"\r\n            showSearch\r\n            allowClear\r\n            colProps={{ flex: \"240px\" }}\r\n            options={driverDeviceOptions}\r\n            placeholder={\"All\"}\r\n            formItemProps={{ style: { marginBottom: 4 } }}\r\n            fieldProps={{\r\n              onChange(value, option) {\r\n                actionRef.current?.reload();\r\n              },\r\n            }}\r\n          />\r\n\r\n          <ProFormText\r\n            name=\"like_city\"\r\n            label=\"City\"\r\n            colProps={{ flex: \"160px\" }}\r\n            placeholder=\"Search city...\"\r\n            formItemProps={{ style: { marginBottom: 4 } }}\r\n          />\r\n\r\n          <ProFormDatePicker\r\n            name=\"note_date\"\r\n            label=\"Call By Call Date\"\r\n            format=\"MM/DD/YYYY\"\r\n            colProps={{ flex: \"130px\" }}\r\n            formItemProps={{ style: { marginBottom: 4 } }}\r\n          />\r\n\r\n          <ProFormText\r\n            name=\"like_customer_full_address\"\r\n            label=\"Address\"\r\n            colProps={{ flex: \"240px\" }}\r\n            placeholder=\"Search address...\"\r\n            formItemProps={{ style: { marginBottom: 4 } }}\r\n          />\r\n          <ProFormText\r\n            name=\"dispatch_external_id\"\r\n            label=\"Address/WO #\"\r\n            colProps={{ flex: \"200px\" }}\r\n            placeholder=\"Search WO #...\"\r\n            formItemProps={{ style: { marginBottom: 4 } }}\r\n          />\r\n\r\n          <ProFormSelect\r\n            name=\"submitted\"\r\n            label=\"Authorization\"\r\n            colProps={{ flex: \"100px\" }}\r\n            options={[\r\n              { value: 0, label: \"Not Submitted\" },\r\n              { value: 1, label: \"Submitted\" },\r\n            ]}\r\n            placeholder={\"All\"}\r\n            formItemProps={{ style: { marginBottom: 4 } }}\r\n            fieldProps={{\r\n              onChange(value, option) {\r\n                actionRef.current?.reload();\r\n              },\r\n            }}\r\n          />\r\n          <ProForm.Item colProps={{ flex: \"auto\" }} label=\"  \" style={{ marginLeft: \"auto\", marginBottom: 4 }}>\r\n            <Button\r\n              style={{ marginRight: 8 }}\r\n              onClick={() => {\r\n                searchFormRef.current?.resetFields();\r\n                actionRef.current?.reload();\r\n              }}\r\n            >\r\n              Reset\r\n            </Button>\r\n            <Button\r\n              type=\"primary\"\r\n              htmlType=\"submit\"\r\n              onClick={() => {\r\n                actionRef.current?.reload();\r\n              }}\r\n            >\r\n              Search\r\n            </Button>\r\n          </ProForm.Item>\r\n        </ProForm>\r\n      </Card>\r\n\r\n      <ProTable<RecordType, API.PageParams>\r\n        headerTitle={false}\r\n        actionRef={actionRef}\r\n        rowKey=\"id\"\r\n        revalidateOnFocus={false}\r\n        options={false}\r\n        size=\"small\"\r\n        pagination={{ defaultPageSize: sn(Util.getSfValues(\"gps_notes_list_p\")?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION) }}\r\n        request={async (params, sort, filter) => {\r\n          const sfValues = searchFormRef.current?.getFieldsValue();\r\n          Util.setSfValues(\"gps_notes_list\", sfValues);\r\n          Util.setSfValues(\"gps_notes_list_p\", params);\r\n\r\n          const res = await getGpsDispatchNoteListByPage(\r\n            { ...sfValues, ...params, with: \"dispatch,driverDevice\" },\r\n            { note_date: \"descend\", dispatch_id: \"descend\" },\r\n            filter,\r\n          );\r\n          return res;\r\n        }}\r\n        onRequestError={Util.error}\r\n        search={false}\r\n        columns={columns}\r\n        columnEmptyText=\"\"\r\n        tableAlertRender={false}\r\n        style={{ marginTop: 16 }}\r\n        sticky\r\n        rowClassName={(entity) => {\r\n          let cls = \"grid-row\";\r\n          if (entity.acknowledged == \"new\") {\r\n            cls += \" status-acknowledged-new\";\r\n          } else if (entity.acknowledged == \"working\") {\r\n            cls += \" status-acknowledged-working\";\r\n          }\r\n\r\n          if (entity.submitted || entity.acknowledged == \"submitted\") {\r\n            cls += \" shaded\";\r\n          }\r\n\r\n          return cls;\r\n        }}\r\n      />\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default GpsDispatchNoteList;\r\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/tenant/gps/GpsDispatchNoteList/index.tsx"}, "29": {"path": "/tenant/gps/statuses", "name": "GPS Job Statuses", "file": "@/pages/tenant/gps/GpsDispatchStatusList/index.tsx", "parentId": "25", "id": "29", "absPath": "/tenant/gps/statuses", "__content": "import { EditOutlined, PlusOutlined } from \"@ant-design/icons\";\r\nimport { <PERSON><PERSON>, ColorPicker, message } from \"antd\";\r\nimport React, { useState, useRef } from \"react\";\r\nimport { PageContainer, FooterToolbar } from \"@ant-design/pro-layout\";\r\nimport type { ProColumns, ActionType } from \"@ant-design/pro-table\";\r\nimport ProTable from \"@ant-design/pro-table\";\r\nimport { addUser } from \"@/services/app/user\";\r\nimport Util from \"@/util\";\r\nimport CreateForm from \"./components/CreateForm\";\r\nimport UpdateForm from \"./components/UpdateForm\";\r\nimport { deleteGpsDispatchStatus, getGpsDispatchStatusListByPage, updateGpsDispatchStatus } from \"@/services/app/tenant/gps/gps-dispatch-status\";\r\nimport BatchDeleteAction from \"@/components/Table/BatchDeleteAction\";\r\nimport { Link } from \"@umijs/max\";\r\n\r\n/**\r\n *  Delete node\r\n *\r\n * @param selectedRows\r\n */\r\n\r\nconst handleRemove = async (selectedRows: API.GpsDispatchStatus[]) => {\r\n  const hide = message.loading(\"Deleting\");\r\n  if (!selectedRows) return true;\r\n\r\n  try {\r\n    await deleteGpsDispatchStatus(selectedRows.map((row) => row.id)?.join(\",\"));\r\n    hide();\r\n    message.success(\"Deleted successfully and will refresh soon\");\r\n    return true;\r\n  } catch (error) {\r\n    hide();\r\n    Util.error(\"Delete failed, please try again!\");\r\n    return false;\r\n  }\r\n};\r\n\r\nconst GpsDispatchStatusList: React.FC = () => {\r\n  const [createModalVisible, handleModalVisible] = useState<boolean>(false);\r\n\r\n  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);\r\n  const actionRef = useRef<ActionType>(null);\r\n\r\n  const [currentRow, setCurrentRow] = useState<API.GpsDispatchStatus>();\r\n  const [selectedRowsState, setSelectedRows] = useState<API.GpsDispatchStatus[]>([]);\r\n\r\n  const [colors, setColors] = useState<any>({});\r\n\r\n  const columns: ProColumns<API.GpsDispatchStatus>[] = [\r\n    {\r\n      title: \"Status Name\",\r\n      dataIndex: \"status_name\",\r\n      sorter: true,\r\n      defaultSortOrder: \"ascend\",\r\n      width: 400,\r\n    },\r\n    {\r\n      title: \"Color\",\r\n      dataIndex: \"color_hex\",\r\n      width: 120,\r\n      render(dom, entity, index, action, schema) {\r\n        return (\r\n          <ColorPicker\r\n            value={colors[`${entity.id}`]}\r\n            showText\r\n            onChange={(value) => {\r\n              //dsRef.current[`${entity.id}`] = value.toHexString();\r\n              setColors((prev: any) => ({ ...prev, [`${entity.id}`]: value.toHexString() }));\r\n            }}\r\n            onChangeComplete={(value) => {\r\n              // dsRef.current[`${entity.id}`] = value.toHexString();\r\n              setColors((prev: any) => ({ ...prev, [`${entity.id}`]: value.toHexString() }));\r\n            }}\r\n            onOpenChange={(open) => {\r\n              if (!open) {\r\n                if (colors[`${entity.id}`] != entity.color_hex) {\r\n                  updateGpsDispatchStatus(entity.id, { color_hex: colors[`${entity.id}`] })\r\n                    .then((res) => {\r\n                      message.success(\"Saved successfully.\");\r\n                    })\r\n                    .catch(Util.error);\r\n                }\r\n              }\r\n            }}\r\n          />\r\n        );\r\n      },\r\n    },\r\n    {\r\n      title: \"ID\",\r\n      dataIndex: \"id\",\r\n      align: \"center\",\r\n      width: 80,\r\n    },\r\n    {\r\n      title: \"Option\",\r\n      dataIndex: \"option\",\r\n      valueType: \"option\",\r\n      render: (_, entity) => [\r\n        <a\r\n          key=\"config\"\r\n          onClick={() => {\r\n            handleUpdateModalVisible(true);\r\n            setCurrentRow({ ...entity, color_hex: colors[`${entity.id}`] });\r\n          }}\r\n        >\r\n          <EditOutlined />\r\n        </a>,\r\n      ],\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <PageContainer\r\n      extra={\r\n        <>\r\n          <Link to={\"/tenant/gps/job-board\"}>Back to Job Board</Link>\r\n        </>\r\n      }\r\n    >\r\n      <ProTable<API.GpsDispatchStatus, API.PageParams>\r\n        headerTitle={\"Job Status list\"}\r\n        actionRef={actionRef}\r\n        rowKey=\"id\"\r\n        revalidateOnFocus={false}\r\n        size=\"small\"\r\n        toolBarRender={() => [\r\n          <Button\r\n            type=\"primary\"\r\n            key=\"primary\"\r\n            onClick={() => {\r\n              handleModalVisible(true);\r\n            }}\r\n          >\r\n            <PlusOutlined /> New\r\n          </Button>,\r\n        ]}\r\n        request={async (params, sort, filter) => {\r\n          const res = await getGpsDispatchStatusListByPage(params, sort, filter);\r\n          /* dsRef.current = res.data\r\n            .filter((x) => x.id == 14)\r\n            .reduce((prev: any, x) => {\r\n              prev[`${x.id}`] = x.color_hex;\r\n              return prev;\r\n            }, {}); */\r\n          setColors(\r\n            res.data.reduce((prev: any, x) => {\r\n              prev[`${x.id}`] = x.color_hex;\r\n              return prev;\r\n            }, {}),\r\n          );\r\n          return res;\r\n        }}\r\n        onRequestError={Util.error}\r\n        search={false}\r\n        columns={columns}\r\n        rowSelection={{\r\n          onChange: (_, selectedRows) => {\r\n            setSelectedRows(selectedRows);\r\n          },\r\n        }}\r\n        columnEmptyText=\"\"\r\n        tableAlertRender={false}\r\n      />\r\n\r\n      {selectedRowsState?.length > 0 && (\r\n        <FooterToolbar\r\n          extra={\r\n            <div>\r\n              chosen{\" \"}\r\n              <a\r\n                style={{\r\n                  fontWeight: 600,\r\n                }}\r\n              >\r\n                {selectedRowsState.length}\r\n              </a>{\" \"}\r\n              status &nbsp;&nbsp;\r\n            </div>\r\n          }\r\n        >\r\n          <BatchDeleteAction\r\n            title=\"status\"\r\n            onConfirm={async () => {\r\n              if (!selectedRowsState) return true;\r\n\r\n              try {\r\n                await handleRemove(selectedRowsState);\r\n                setSelectedRows([]);\r\n                actionRef.current?.reloadAndRest?.();\r\n              } catch (error) {\r\n                Util.error(error);\r\n              }\r\n            }}\r\n          />\r\n        </FooterToolbar>\r\n      )}\r\n\r\n      <CreateForm\r\n        modalVisible={createModalVisible}\r\n        handleModalVisible={handleModalVisible}\r\n        onSubmit={async (value) => {\r\n          handleModalVisible(false);\r\n\r\n          if (actionRef.current) {\r\n            actionRef.current.reload();\r\n          }\r\n        }}\r\n      />\r\n\r\n      <UpdateForm\r\n        modalVisible={updateModalVisible}\r\n        handleModalVisible={handleUpdateModalVisible}\r\n        initialValues={currentRow || {}}\r\n        onSubmit={async (value) => {\r\n          setCurrentRow(undefined);\r\n\r\n          if (actionRef.current) {\r\n            actionRef.current.reload();\r\n          }\r\n        }}\r\n        onCancel={() => {\r\n          handleUpdateModalVisible(false);\r\n        }}\r\n      />\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default GpsDispatchStatusList;\r\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/tenant/gps/GpsDispatchStatusList/index.tsx"}, "30": {"path": "/tenant/settings", "name": "Settings", "parentId": "ant-design-pro-layout", "id": "30", "absPath": "/tenant/settings"}, "31": {"path": "/tenant/settings/system-message-template/edit", "name": "Automatic SMS Template", "external": true, "parentId": "30", "id": "31", "absPath": "/tenant/settings/system-message-template/edit"}, "32": {"path": "/tenant/settings/dispatch/greetings", "name": "Automatic SMS Logs", "external": true, "parentId": "30", "id": "32", "absPath": "/tenant/settings/dispatch/greetings"}, "33": {"path": "/tenant/settings/address", "name": "Tenant Addresses", "external": true, "parentId": "30", "id": "33", "absPath": "/tenant/settings/address"}, "34": {"path": "/tenant/settings/tenant-info", "name": "Tenant Info", "file": "@/pages/tenant/TenantInfoPage.tsx", "parentId": "30", "id": "34", "absPath": "/tenant/settings/tenant-info", "__content": "import { deleteTenantLogoFile, getCurrentTenant, updateTenant } from \"@/services/app/tenant/tenant-info\";\r\nimport Util, { sn } from \"@/util\";\r\nimport { DeleteOutlined, FullscreenExitOutlined } from \"@ant-design/icons\";\r\nimport { PageContainer, ProForm, ProFormInstance, ProFormText, ProFormUploadDragger } from \"@ant-design/pro-components\";\r\nimport { Card, Col, message, Row, Space, Spin, Image, Button, Popconfirm, Modal } from \"antd\";\r\nimport { RcFile, UploadFile } from \"antd/es/upload\";\r\nimport { useCallback, useEffect, useRef, useState } from \"react\";\r\n\r\nimport styles from \"./TenantInfoPage.less\";\r\n\r\nconst TenantInfoPage: React.FC = () => {\r\n  const formRef = useRef<ProFormInstance>(null);\r\n\r\n  const [loading, setLoading] = useState<boolean>(false);\r\n  const [tenant, setTenant] = useState<API.Tenant>();\r\n\r\n  const loadTenantInfo = useCallback(async () => {\r\n    setLoading(true);\r\n    return getCurrentTenant({ with: \"logoFile\" })\r\n      .then((res) => {\r\n        setTenant(res);\r\n        return res;\r\n      })\r\n      .finally(() => {\r\n        setLoading(false);\r\n      });\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    loadTenantInfo();\r\n  }, [loadTenantInfo]);\r\n\r\n  useEffect(() => {\r\n    formRef.current?.resetFields();\r\n    const newFormValues = {\r\n      ...tenant,\r\n      files: tenant?.logo_file ? [tenant.logo_file] : [],\r\n    };\r\n    formRef.current?.setFieldsValue(newFormValues);\r\n  }, [tenant]);\r\n\r\n  return (\r\n    <PageContainer className={styles.tenantInfoPage}>\r\n      <Row gutter={32}>\r\n        <Col xs={24} lg={12} xl={12}>\r\n          <Card size=\"small\" style={{ marginBottom: 16 }}>\r\n            <Spin style={{ width: \"100%\" }} spinning={loading}>\r\n              <ProForm<API.Tenant & { files?: API.File[] }>\r\n                formRef={formRef}\r\n                layout=\"horizontal\"\r\n                isKeyPressSubmit={false}\r\n                labelCol={{ flex: \"130px\" }}\r\n                submitter={{\r\n                  resetButtonProps: { style: { marginLeft: \"auto\", display: \"none\" } },\r\n                  searchConfig: { submitText: \"Update Tenant\" },\r\n                  submitButtonProps: { htmlType: \"button\", style: { marginLeft: \"auto\" } },\r\n                }}\r\n                onFinish={async (formValues) => {\r\n                  console.log(formValues);\r\n                  const formData = new FormData();\r\n                  formData.set(\"tenant_website\", formValues?.tenant_website || \"\");\r\n                  formData.set(\"tenant_name\", formValues?.tenant_name || \"\");\r\n                  if (formValues?.files?.length) {\r\n                    const file = formValues?.files[0];\r\n                    if (!sn(file.uid)) {\r\n                      formData.set(`file`, (file as UploadFile).originFileObj || \"\");\r\n                    }\r\n                  }\r\n\r\n                  setLoading(true);\r\n                  const hide = message.loading(\"Saving tenant data...\", 0);\r\n                  return updateTenant(formData)\r\n                    .then(() => {\r\n                      message.success(\"Saved successfully\");\r\n                      loadTenantInfo();\r\n                    })\r\n                    .catch((error) => {\r\n                      message.error(\"Failed to save tenant data\");\r\n                    })\r\n                    .finally(() => {\r\n                      hide();\r\n                      setLoading(false);\r\n                    });\r\n                }}\r\n              >\r\n                <ProFormText name=\"tenant_name\" label=\"Tenant Name\" required rules={[{ required: true, message: \"Tenant Name is required!\" }]} />\r\n                <ProFormText name=\"tenant_website\" label=\"Site URL\" rules={[{ type: \"url\", message: \"URL is invalid!\" }]} />\r\n                <ProFormUploadDragger\r\n                  name={[\"files\"]}\r\n                  label={\"Tenant Logo File\"}\r\n                  title={false}\r\n                  description=\"Please select a file or drag & drop\"\r\n                  wrapperCol={{ span: 24 }}\r\n                  fieldProps={{\r\n                    multiple: false,\r\n                    listType: \"picture-card\",\r\n                    name: \"files\",\r\n                    accept: \"image/*\",\r\n                    height: 120,\r\n                    maxCount: 1,\r\n                    beforeUpload: (file: RcFile, fileList: RcFile[]) => {\r\n                      return false;\r\n                    },\r\n                    onRemove: async (file: API.File) => {\r\n                      if (file.id) {\r\n                        const { confirm } = Modal;\r\n                        return new Promise((resolve, reject) => {\r\n                          confirm({\r\n                            title: \"Are you sure you want to delete?\",\r\n                            onOk: async () => {\r\n                              resolve(true);\r\n                              const hide = message.loading(`Deleting a file '${file.file_name}'.`, 0);\r\n                              const res = await deleteTenantLogoFile(sn(file.id));\r\n                              hide();\r\n                              if (res) {\r\n                                message.success(`Deleted successfully!`);\r\n                              } else {\r\n                                Util.error(`Delete failed, please try again!`);\r\n                              }\r\n\r\n                              return res;\r\n                            },\r\n                            onCancel: () => {\r\n                              reject(true);\r\n                            },\r\n                          });\r\n                        });\r\n                      } else {\r\n                        return true;\r\n                      }\r\n                    },\r\n                    itemRender: (originNode: React.ReactElement, file, fileList, actions) => {\r\n                      let thumbUrl: any = \"\",\r\n                        url: any = \"\";\r\n                      if ((file as API.File).id) {\r\n                        thumbUrl = API_URL + \"/\" + (file as API.File).thumb_url;\r\n                        url = API_URL + \"/\" + (file as API.File).url;\r\n                      } else {\r\n                        thumbUrl = file.thumbUrl;\r\n                        url = file.thumbUrl;\r\n                      }\r\n                      return thumbUrl && url ? (\r\n                        <div style={{ position: \"relative\" }}>\r\n                          <Image\r\n                            src={thumbUrl}\r\n                            preview={{\r\n                              src: url,\r\n                            }}\r\n                            width={120}\r\n                            height={120}\r\n                          />\r\n                          <div style={{ justifyContent: \"center\", position: \"absolute\", top: 5, right: 5 }}>\r\n                            <Button\r\n                              size=\"small\"\r\n                              variant=\"outlined\"\r\n                              color=\"danger\"\r\n                              icon={<DeleteOutlined />}\r\n                              title=\"Delete\"\r\n                              onClick={actions.remove}\r\n                            />\r\n                          </div>\r\n                        </div>\r\n                      ) : null;\r\n                    },\r\n                  }}\r\n                />\r\n              </ProForm>\r\n            </Spin>\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default TenantInfoPage;\r\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/tenant/TenantInfoPage.tsx"}, "35": {"path": "/tenant/settings/dispatch/text-note-template", "name": "Text Note Template", "file": "@/pages/tenant/TextNoteTemplate/index.tsx", "parentId": "30", "id": "35", "absPath": "/tenant/settings/dispatch/text-note-template", "__content": "import React, { useRef, useState } from 'react';\r\nimport { PageContainer } from '@ant-design/pro-layout';\r\nimport type { ActionType, ProColumns } from '@ant-design/pro-table';\r\nimport ProTable from '@ant-design/pro-table';\r\nimport { Button, message, Popconfirm } from 'antd';\r\nimport { PlusOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';\r\nimport CreateForm from './components/CreateForm';\r\nimport UpdateForm from './components/UpdateForm';\r\nimport { \r\n  getTextNoteTemplateListByPage, \r\n  deleteTextNoteTemplate \r\n} from '@/services/app/tenant/dispatch/text-note-template';\r\n\r\nconst TextNoteTemplate: React.FC = () => {\r\n  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);\r\n  const [updateModalVisible, setUpdateModalVisible] = useState<boolean>(false);\r\n  const [currentRow, setCurrentRow] = useState<API.TextNoteTemplate>();\r\n  const actionRef = useRef<ActionType>(null);\r\n\r\n  const handleDelete = async (id: number) => {\r\n    try {\r\n      await deleteTextNoteTemplate(id);\r\n      message.success('Template deleted successfully');\r\n      actionRef.current?.reload();\r\n    } catch (error) {\r\n      message.error('Failed to delete template');\r\n    }\r\n  };\r\n\r\n  const columns: ProColumns<API.TextNoteTemplate>[] = [\r\n    {\r\n      title: 'Template Text',\r\n      dataIndex: 'note_template',\r\n      ellipsis: true,\r\n      width: '60%',\r\n    },\r\n    {\r\n      title: 'Created',\r\n      dataIndex: 'created_at',\r\n      valueType: 'dateTime',\r\n      width: '20%',\r\n    },\r\n    {\r\n      title: 'Actions',\r\n      valueType: 'option',\r\n      width: '20%',\r\n      render: (_, record) => [\r\n        <Button\r\n          key=\"edit\"\r\n          type=\"link\"\r\n          icon={<EditOutlined />}\r\n          onClick={() => {\r\n            setCurrentRow(record);\r\n            setUpdateModalVisible(true);\r\n          }}\r\n        >\r\n          Edit\r\n        </Button>,\r\n        <Popconfirm\r\n          key=\"delete\"\r\n          title=\"Are you sure you want to delete this template?\"\r\n          onConfirm={() => handleDelete(record.id!)}\r\n          okText=\"Yes\"\r\n          cancelText=\"No\"\r\n        >\r\n          <Button type=\"link\" danger icon={<DeleteOutlined />}>\r\n            Delete\r\n          </Button>\r\n        </Popconfirm>,\r\n      ],\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <PageContainer>\r\n      <ProTable<API.TextNoteTemplate, API.PageParams>\r\n        headerTitle=\"Text Note Templates\"\r\n        actionRef={actionRef}\r\n        rowKey=\"id\"\r\n        search={false}\r\n        toolBarRender={() => [\r\n          <Button\r\n            type=\"primary\"\r\n            key=\"primary\"\r\n            icon={<PlusOutlined />}\r\n            onClick={() => setCreateModalVisible(true)}\r\n          >\r\n            New Template\r\n          </Button>,\r\n        ]}\r\n        request={(params, sort, filter) => getTextNoteTemplateListByPage(params, sort, filter)}\r\n        columns={columns}\r\n      />\r\n      \r\n      <CreateForm\r\n        visible={createModalVisible}\r\n        onVisibleChange={setCreateModalVisible}\r\n        onSubmit={async () => {\r\n          setCreateModalVisible(false);\r\n          actionRef.current?.reload();\r\n        }}\r\n      />\r\n      \r\n      <UpdateForm\r\n        visible={updateModalVisible}\r\n        onVisibleChange={setUpdateModalVisible}\r\n        currentRow={currentRow}\r\n        onSubmit={async () => {\r\n          setUpdateModalVisible(false);\r\n          setCurrentRow(undefined);\r\n          actionRef.current?.reload();\r\n        }}\r\n      />\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default TextNoteTemplate;\r\n\r\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/tenant/TextNoteTemplate/index.tsx"}, "36": {"path": "/tenant/call_board", "name": "Call Board", "external": true, "parentId": "ant-design-pro-layout", "id": "36", "absPath": "/tenant/call_board"}, "37": {"path": "/tenant/tenant_profile", "name": "My Profile", "external": true, "parentId": "ant-design-pro-layout", "id": "37", "absPath": "/tenant/tenant_profile"}, "38": {"path": "/admin", "name": "Admin", "icon": "crown", "access": "canAdmin", "parentId": "ant-design-pro-layout", "id": "38", "absPath": "/admin"}, "39": {"path": "/admin", "redirect": "/admin/sub-page", "parentId": "38", "id": "39", "absPath": "/admin"}, "40": {"path": "/admin/sub-page", "name": "Sub page", "file": "@/pages/Admin.tsx", "parentId": "38", "id": "40", "absPath": "/admin/sub-page", "__content": "import { PageContainer } from '@ant-design/pro-components';\nimport '@umijs/max';\nimport { <PERSON><PERSON>, <PERSON><PERSON>, Card, Space, Typography } from 'antd';\nimport React from 'react';\nconst Admin: React.FC = () => {\n  return (\n    <PageContainer content={' Admin page'}>\n      <Card>\n\n        <Alert\n          message={'Test page'}\n          type=\"success\"\n          showIcon\n          banner\n          style={{\n            marginBottom: 48,\n          }}\n        />\n\n        <Space size={32}>\n          <Button type='primary'>Primary</Button>\n          <Button type='primary' ghost>Primary (Ghost)</Button>\n        </Space>\n      </Card>\n      \n    </PageContainer>\n  );\n};\nexport default Admin;\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/Admin.tsx"}, "41": {"path": "/", "redirect": "/tenant/dashboard", "parentId": "ant-design-pro-layout", "id": "41", "absPath": "/"}, "42": {"path": "*", "layout": false, "file": "@/pages/404.tsx", "id": "42", "absPath": "/*", "__content": "import { getIntl, history, useIntl } from '@umijs/max';\nimport { Button, Result } from 'antd';\nimport React from 'react';\n\nconst NoFoundPage: React.FC = () => {\n  const intl = getIntl();\n  console.log('intl', intl);\n  \n  return (\n  <Result\n    status=\"404\"\n    title=\"404\"\n    subTitle={useIntl().formatMessage({ id: 'pages.404.subTitle' })}\n    extra={\n      <Button type=\"primary\" onClick={() => history.push('/')}>\n        {useIntl().formatMessage({ id: 'pages.404.buttonText' })}\n      </Button>\n    }\n  />\n);}\n\nexport default NoFoundPage;\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/404.tsx"}, "ant-design-pro-layout": {"id": "ant-design-pro-layout", "path": "/", "file": "F:/htdocs/development_team/frontend/src/.umi/plugin-layout/Layout.tsx", "absPath": "/", "isLayout": true, "__absFile": "F:/htdocs/development_team/frontend/src/.umi/plugin-layout/Layout.tsx"}}, "apiRoutes": {}, "hasSrcDir": true, "npmClient": "yarn", "umi": {"version": "4.4.11", "name": "<PERSON><PERSON>", "importSource": "@umijs/max", "cliName": "max"}, "bundleStatus": {"done": false}, "react": {"version": "19.1.0", "path": "F:\\htdocs\\development_team\\frontend\\node_modules\\react"}, "react-dom": {"version": "19.1.0", "path": "F:\\htdocs\\development_team\\frontend\\node_modules\\react-dom"}, "appJS": {"path": "F:\\htdocs\\development_team\\frontend\\src\\app.tsx", "exports": ["getInitialState", "layout", "request"]}, "locale": "en-US", "globalCSS": ["F:\\htdocs\\development_team\\frontend\\src\\global.less"], "globalJS": ["F:\\htdocs\\development_team\\frontend\\src\\global.tsx"], "overridesCSS": [], "bundler": "mako", "git": {"originUrl": "**************-pa:developmentbutler/development_team.git"}, "framework": "react", "typescript": {"tsVersion": "5.8.3", "tslibVersion": "2.8.1"}, "faviconFiles": [], "port": 3000, "host": "0.0.0.0", "ip": "***********", "antd": {"pkgPath": "F:\\htdocs\\development_team\\frontend\\node_modules\\antd", "version": "5.26.3"}, "pluginLayout": {"pkgPath": "F:/htdocs/development_team/frontend/node_modules/@ant-design/pro-components", "version": "2.8.9"}}