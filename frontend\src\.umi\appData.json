{"cwd": "F:\\htdocs\\development_team\\frontend", "pkg": {"name": "dipatchbutler-frontend-app", "version": "2.0.0", "private": true, "description": "New frontend application for dispatchbutler app", "repository": "https://github.com/developmentbutler/development_team.git", "license": "UNLICENSED", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "deploy": "yarn build && yarn gh-pages", "dev": "yarn start:dev", "gh-pages": "gh-pages -d dist", "postinstall": "max setup", "jest": "jest", "lint": "yarn lint:js && yarn lint:prettier && yarn tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "openapi": "max openapi", "prepare": "husky", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "yarn build && max preview --port 8000", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "serve": "umi-serve", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev PORT=3000 max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "build:sandbox": "cross-env REACT_APP_ENV=sandbox UMI_ENV=sandbox max build", "test": "jest", "test:coverage": "yarn jest -- --coverage", "test:update": "yarn jest -- -u", "tsc": "tsc --noEmit"}, "browserslist": ["defaults"], "dependencies": {"@ant-design/icons": "5.x", "@ant-design/pro-components": "^2.7.19", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@types/prop-types": "^15.7.15", "@types/react-beautiful-dnd": "^13.1.8", "@vis.gl/react-google-maps": "^1.5.4", "antd": "^5.25.4", "antd-style": "^3.7.0", "classnames": "^2.5.1", "dayjs": "^1.11.13", "prop-types": "^15.8.1", "react": "^19.1.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^19.1.0"}, "devDependencies": {"@ant-design/pro-cli": "^3.3.0", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.0.1", "@types/express": "^4.17.21", "@types/jest": "^29.5.13", "@types/lodash": "^4.17.10", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@types/react-helmet": "^6.1.11", "@umijs/lint": "^4.3.24", "@umijs/max": "^4.3.24", "cross-env": "^7.0.3", "eslint": "^8.57.1", "express": "^4.21.1", "gh-pages": "^6.1.1", "husky": "^9.1.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.10", "mockjs": "^1.1.0", "prettier": "^3.3.3", "ts-node": "^10.9.2", "typescript": "^5.6.3", "umi-presets-pro": "^2.0.3", "umi-serve": "^1.9.11"}, "engines": {"node": ">=12.0.0"}}, "pkgPath": "F:\\htdocs\\development_team\\frontend\\package.json", "plugins": {"./node_modules/@umijs/core/dist/service/servicePlugin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "preset", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/core/dist/service/servicePlugin.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/core/dist/service/servicePlugin", "key": "servicePlugin"}, "@umijs/preset-umi": {"config": {}, "time": {"hooks": {}, "register": 67}, "enableBy": "register", "type": "preset", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/index.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "@umijs/preset-umi", "key": "umi"}, "./node_modules/@umijs/max/dist/preset": {"config": {}, "time": {"hooks": {}, "register": 24}, "enableBy": "register", "type": "preset", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/max/dist/preset.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/max/dist/preset", "key": "preset"}, "umi-presets-pro": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 721}, "enableBy": "register", "type": "preset", "path": "F:/htdocs/development_team/frontend/node_modules/umi-presets-pro/dist/index.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "umi-presets-pro", "key": "umiPresetsPro"}, "./node_modules/@umijs/preset-umi/dist/registerMethods": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 102}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/registerMethods.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/registerMethods", "key": "registerMethods"}, "@umijs/did-you-know": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/did-you-know/dist/plugin.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "@umijs/did-you-know", "key": "umijsDidYouKnow"}, "./node_modules/@umijs/preset-umi/dist/features/404/404": {"config": {}, "time": {"hooks": {"modifyRoutes": [1]}, "register": 5}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/404/404.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/404/404", "key": "404"}, "./node_modules/@umijs/preset-umi/dist/features/appData/appData": {"config": {}, "time": {"hooks": {"modifyAppData": [54]}, "register": 194}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/appData/appData.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/appData", "key": "appData"}, "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/appData/umiInfo.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo", "key": "umiInfo"}, "./node_modules/@umijs/preset-umi/dist/features/check/check": {"config": {}, "time": {"hooks": {"onCheckConfig": [0], "onCheck": [1]}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/check/check.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/check/check", "key": "check"}, "./node_modules/@umijs/preset-umi/dist/features/check/babel722": {"config": {}, "time": {"hooks": {"onCheck": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/check/babel722.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/check/babel722", "key": "babel722"}, "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting", "key": "codeSplitting"}, "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 108}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins", "key": "configPlugins"}, "virtual: config-title": {"id": "virtual: config-title", "key": "title", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styles": {"id": "virtual: config-styles", "key": "styles", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-scripts": {"id": "virtual: config-scripts", "key": "scripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routes": {"id": "virtual: config-routes", "key": "routes", "config": {"onChange": "regenerateTmpFiles"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routeLoader": {"id": "virtual: config-routeLoader", "key": "routeLoader", "config": {"default": {"moduleType": "esm"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-reactRouter5Compat": {"id": "virtual: config-reactRouter5Compat", "key": "reactRouter5Compat", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-presets": {"id": "virtual: config-presets", "key": "presets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-plugins": {"id": "virtual: config-plugins", "key": "plugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-npmClient": {"id": "virtual: config-npmClient", "key": "npmClient", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mountElementId": {"id": "virtual: config-mountElementId", "key": "mountElementId", "config": {"default": "root"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-metas": {"id": "virtual: config-metas", "key": "metas", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-links": {"id": "virtual: config-links", "key": "links", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-historyWithQuery": {"id": "virtual: config-historyWithQuery", "key": "historyWithQuery", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-history": {"id": "virtual: config-history", "key": "history", "config": {"default": {"type": "browser"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-headScripts": {"id": "virtual: config-headScripts", "key": "headScripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esbuildMinifyIIFE": {"id": "virtual: config-esbuildMinifyIIFE", "key": "esbuildMinifyIIFE", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionRoutes": {"id": "virtual: config-conventionRoutes", "key": "conventionRoutes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionLayout": {"id": "virtual: config-conventionLayout", "key": "conventionLayout", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-base": {"id": "virtual: config-base", "key": "base", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-analyze": {"id": "virtual: config-analyze", "key": "analyze", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-writeToDisk": {"id": "virtual: config-writeToDisk", "key": "writeToDisk", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-transformRuntime": {"id": "virtual: config-transformRuntime", "key": "transformRuntime", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-theme": {"id": "virtual: config-theme", "key": "theme", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-targets": {"id": "virtual: config-targets", "key": "targets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgr": {"id": "virtual: config-svgr", "key": "svgr", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgo": {"id": "virtual: config-svgo", "key": "svgo", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-stylusLoader": {"id": "virtual: config-stylusLoader", "key": "stylus<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styleLoader": {"id": "virtual: config-style<PERSON>oader", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspilerOptions": {"id": "virtual: config-srcTranspilerOptions", "key": "srcTranspilerOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspiler": {"id": "virtual: config-srcTranspiler", "key": "srcTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-sassLoader": {"id": "virtual: config-sassLoader", "key": "sass<PERSON><PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-runtimePublicPath": {"id": "virtual: config-runtimePublicPath", "key": "runtimePublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-purgeCSS": {"id": "virtual: config-purgeCSS", "key": "purgeCSS", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-publicPath": {"id": "virtual: config-publicPath", "key": "publicPath", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-proxy": {"id": "virtual: config-proxy", "key": "proxy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-postcssLoader": {"id": "virtual: config-postcssLoader", "key": "postcss<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-outputPath": {"id": "virtual: config-outputPath", "key": "outputPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-normalCSSLoaderModules": {"id": "virtual: config-normalCSSLoaderModules", "key": "normalCSSLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mfsu": {"id": "virtual: config-mfsu", "key": "mfsu", "config": {"default": {"strategy": "eager"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mdx": {"id": "virtual: config-mdx", "key": "mdx", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-manifest": {"id": "virtual: config-manifest", "key": "manifest", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-lessLoader": {"id": "virtual: config-less<PERSON><PERSON>der", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifierOptions": {"id": "virtual: config-jsMinifierOptions", "key": "jsMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifier": {"id": "virtual: config-jsMinifier", "key": "jsMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-inlineLimit": {"id": "virtual: config-inlineLimit", "key": "inlineLimit", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-ignoreMomentLocale": {"id": "virtual: config-ignoreMomentLocale", "key": "ignoreMomentLocale", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-https": {"id": "virtual: config-https", "key": "https", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-hash": {"id": "virtual: config-hash", "key": "hash", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-forkTSChecker": {"id": "virtual: config-fork<PERSON><PERSON><PERSON><PERSON>", "key": "forkTSChecker", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-fastRefresh": {"id": "virtual: config-fastRefresh", "key": "fastRefresh", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraPostCSSPlugins": {"id": "virtual: config-extraPostCSSPlugins", "key": "extraPostCSSPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPresets": {"id": "virtual: config-extraBabelPresets", "key": "extraBabelPresets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPlugins": {"id": "virtual: config-extraBabelPlugins", "key": "extraBabelPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelIncludes": {"id": "virtual: config-extraBabelIncludes", "key": "extraBabelIncludes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-externals": {"id": "virtual: config-externals", "key": "externals", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esm": {"id": "virtual: config-esm", "key": "esm", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-devtool": {"id": "virtual: config-devtool", "key": "devtool", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-depTranspiler": {"id": "virtual: config-depTranspiler", "key": "depTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-define": {"id": "virtual: config-define", "key": "define", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-deadCode": {"id": "virtual: config-deadCode", "key": "deadCode", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssPublicPath": {"id": "virtual: config-cssPublicPath", "key": "cssPublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifierOptions": {"id": "virtual: config-cssMinifierOptions", "key": "cssMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifier": {"id": "virtual: config-cssMinifier", "key": "cssMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoaderModules": {"id": "virtual: config-cssLoaderModules", "key": "cssLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoader": {"id": "virtual: config-cssLoader", "key": "cssL<PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-copy": {"id": "virtual: config-copy", "key": "copy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-checkDepCssModules": {"id": "virtual: config-checkDepCssModules", "key": "checkDepCssModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-chainWebpack": {"id": "virtual: config-chainWebpack", "key": "chainWebpack", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cacheDirectoryPath": {"id": "virtual: config-cacheDirectoryPath", "key": "cacheDirectoryPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-babelLoaderCustomize": {"id": "virtual: config-babelLoaderCustomize", "key": "babelLoaderCustomize", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoprefixer": {"id": "virtual: config-autoprefixer", "key": "autoprefixer", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoCSSModules": {"id": "virtual: config-autoCSSModules", "key": "autoCSSModules", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-alias": {"id": "virtual: config-alias", "key": "alias", "config": {"default": {"umi": "@@/exports", "react": "F:\\htdocs\\development_team\\frontend\\node_modules\\react", "react-dom": "F:\\htdocs\\development_team\\frontend\\node_modules\\react-dom", "react-router": "F:\\htdocs\\development_team\\frontend\\node_modules\\react-router", "react-router-dom": "F:\\htdocs\\development_team\\frontend\\node_modules\\react-router-dom"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin", "key": "crossorigin"}, "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand", "key": "deps<PERSON>n<PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/devTool/devTool.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool", "key": "devTool"}, "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker": {"config": {}, "time": {"hooks": {}, "register": 325}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker", "key": "esbuildHelperChecker"}, "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi": {"config": {}, "time": {"hooks": {}, "register": 3599}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/esmi/esmi.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi", "key": "esmi"}, "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic": {"config": {}, "time": {"hooks": {}, "register": 721}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic", "key": "exportStatic"}, "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons": {"config": {}, "time": {"hooks": {"modifyAppData": [0]}, "register": 40}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/favicons/favicons.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons", "key": "favicons"}, "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet": {"config": {}, "time": {"hooks": {}, "register": 6}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/helmet/helmet.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet", "key": "helmet"}, "./node_modules/@umijs/preset-umi/dist/features/icons/icons": {"config": {}, "time": {"hooks": {}, "register": 10}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/icons/icons.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/icons/icons", "key": "icons"}, "./node_modules/@umijs/preset-umi/dist/features/mock/mock": {"config": {}, "time": {"hooks": {}, "register": 98}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/mock/mock.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/mock/mock", "key": "mock"}, "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/mpa/mpa.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa", "key": "mpa"}, "./node_modules/@umijs/preset-umi/dist/features/okam/okam": {"config": {}, "time": {"hooks": {"modifyAppData": [0], "onCheck": [0]}, "register": 2}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/okam/okam.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/okam/okam", "key": "okam"}, "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/overrides/overrides.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides", "key": "overrides"}, "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency", "key": "phantomDependency"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill": {"config": {}, "time": {"hooks": {"modifyConfig": [2]}, "register": 5}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill", "key": "polyfill"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill", "key": "publicPathPolyfill"}, "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/prepare/prepare.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare", "key": "prepare"}, "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch", "key": "routePrefetch"}, "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/terminal/terminal.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal", "key": "terminal"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles": {"config": {}, "time": {"hooks": {}, "register": 63}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles", "key": "tmpFiles"}, "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader": {"config": {}, "time": {"hooks": {}, "register": 30}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader", "key": "clientLoader"}, "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps", "key": "routeProps"}, "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/ssr/ssr.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr", "key": "ssr"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes": {"config": {}, "time": {"hooks": {}, "register": 329}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes", "key": "configTypes"}, "./node_modules/@umijs/preset-umi/dist/features/transform/transform": {"config": {}, "time": {"hooks": {}, "register": 91}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/transform/transform.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/transform/transform", "key": "transform"}, "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport": {"config": {}, "time": {"hooks": {}, "register": 134}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport", "key": "lowImport"}, "./node_modules/@umijs/preset-umi/dist/features/vite/vite": {"config": {}, "time": {"hooks": {}, "register": 97}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/vite/vite.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/vite/vite", "key": "vite"}, "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute": {"config": {}, "time": {"hooks": {}, "register": 388}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute", "key": "apiRoute"}, "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect": {"config": {}, "time": {"hooks": {}, "register": 239}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/monorepo/redirect.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect", "key": "monorepoRedirect"}, "./node_modules/@umijs/preset-umi/dist/features/test/test": {"config": {}, "time": {"hooks": {}, "register": 6}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/test/test.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/test/test", "key": "test"}, "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent", "key": "clickToComponent"}, "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/legacy/legacy.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy", "key": "legacy"}, "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose", "key": "classPropertiesLoose"}, "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack": {"config": {}, "time": {"hooks": {}, "register": 4}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/webpack/webpack.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack", "key": "preset-umi:webpack"}, "./node_modules/@umijs/preset-umi/dist/features/swc/swc": {"config": {}, "time": {"hooks": {"addOnDemandDeps": [0]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/swc/swc.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/swc/swc", "key": "swc"}, "./node_modules/@umijs/preset-umi/dist/features/ui/ui": {"config": {}, "time": {"hooks": {}, "register": 30}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/ui/ui.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/ui/ui", "key": "ui"}, "./node_modules/@umijs/preset-umi/dist/features/mako/mako": {"config": {}, "time": {"hooks": {"modifyConfig": [0], "onStart": [3]}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/mako/mako.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/mako/mako", "key": "mako"}, "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian": {"config": {}, "time": {"hooks": {}, "register": 5}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian", "key": "hm<PERSON><PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad": {"config": {}, "time": {"hooks": {}, "register": 5}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad", "key": "routePreloadOnLoad"}, "./node_modules/@umijs/preset-umi/dist/features/forget/forget": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/forget/forget.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/forget/forget", "key": "forget"}, "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/features/bundler/bundler.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler", "key": "preset-umi:bundler"}, "./node_modules/@umijs/preset-umi/dist/commands/build": {"config": {}, "time": {"hooks": {}, "register": 16}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/build.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/build", "key": "build"}, "./node_modules/@umijs/preset-umi/dist/commands/config/config": {"config": {}, "time": {"hooks": {}, "register": 246}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/config/config.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/config/config", "key": "config"}, "./node_modules/@umijs/preset-umi/dist/commands/dev/dev": {"config": {}, "time": {"hooks": {"modifyAppData": [34], "onStart": [0]}, "register": 141}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/dev/dev.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/dev/dev", "key": "dev"}, "./node_modules/@umijs/preset-umi/dist/commands/help": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/help.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/help", "key": "help"}, "./node_modules/@umijs/preset-umi/dist/commands/lint": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/lint.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/lint", "key": "lint"}, "./node_modules/@umijs/preset-umi/dist/commands/setup": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/setup.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/setup", "key": "setup"}, "./node_modules/@umijs/preset-umi/dist/commands/deadcode": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/deadcode.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/deadcode", "key": "deadcode"}, "./node_modules/@umijs/preset-umi/dist/commands/version": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/version.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/version", "key": "version"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/page": {"config": {}, "time": {"hooks": {}, "register": 6}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/page.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/page", "key": "generator:page"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/prettier.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier", "key": "generator:prettier"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig", "key": "generator:tsconfig"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/jest": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/jest.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/jest", "key": "generator:jest"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss", "key": "generator:tailwindcss"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/dva": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/dva.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/dva", "key": "generator:dva"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/component": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/component.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/component", "key": "generator:component"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/mock": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/mock.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/mock", "key": "generator:mock"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/cypress.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress", "key": "generator:cypress"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/api": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/api.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/api", "key": "generator:api"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/precommit.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit", "key": "generator:precommit"}, "./node_modules/@umijs/preset-umi/dist/commands/plugin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/plugin.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/plugin", "key": "command:plugin"}, "./node_modules/@umijs/preset-umi/dist/commands/verify-commit": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/verify-commit.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/verify-commit", "key": "verifyCommit"}, "./node_modules/@umijs/preset-umi/dist/commands/preview": {"config": {}, "time": {"hooks": {}, "register": 47}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/preview.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/preview", "key": "preview"}, "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu", "key": "mfsu-cli"}, "@umijs/plugin-run": {"config": {}, "time": {"hooks": {}, "register": 24}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugin-run/dist/index.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "@umijs/plugin-run", "key": "run"}, "./node_modules/@umijs/plugins/dist/access": {"config": {}, "time": {"hooks": {}, "register": 14}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/access.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/access", "key": "access"}, "./node_modules/@umijs/plugins/dist/analytics": {"config": {"onChange": "reload"}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/analytics.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/analytics", "key": "analytics"}, "./node_modules/@umijs/plugins/dist/antd": {"config": {}, "time": {"hooks": {"modifyConfig": [27], "modifyAppData": [0]}, "register": 27}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/antd.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/antd", "key": "antd"}, "./node_modules/@umijs/plugins/dist/dva": {"config": {}, "time": {"hooks": {}, "register": 16}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/dva.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/dva", "key": "dva"}, "./node_modules/@umijs/plugins/dist/initial-state": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/initial-state.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/initial-state", "key": "initialState"}, "./node_modules/@umijs/plugins/dist/layout": {"config": {"onChange": "regenerateTmpFiles"}, "time": {"hooks": {"modifyConfig": [1], "addLayouts": [0], "modifyAppData": [1]}, "register": 6}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/layout.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/layout", "key": "layout"}, "./node_modules/@umijs/plugins/dist/locale": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/locale.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/locale", "key": "locale"}, "./node_modules/@umijs/plugins/dist/mf": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/mf.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/mf", "key": "mf"}, "./node_modules/@umijs/plugins/dist/model": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/model.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/model", "key": "model"}, "./node_modules/@umijs/plugins/dist/moment2dayjs": {"config": {}, "time": {"hooks": {"modifyConfig": [1]}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/moment2dayjs.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/moment2dayjs", "key": "moment2dayjs"}, "./node_modules/@umijs/plugins/dist/qiankun": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/qiankun.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/qiankun", "key": "qiankun"}, "./node_modules/@umijs/plugins/dist/qiankun/master": {"config": {}, "time": {"hooks": {}, "register": 52}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/qiankun/master.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/qiankun/master", "key": "<PERSON><PERSON><PERSON>n-master"}, "./node_modules/@umijs/plugins/dist/qiankun/slave": {"config": {}, "time": {"hooks": {}, "register": 4}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/qiankun/slave.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/qiankun/slave", "key": "qiankun-slave"}, "./node_modules/@umijs/plugins/dist/react-query": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/react-query.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/react-query", "key": "reactQuery"}, "./node_modules/@umijs/plugins/dist/request": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/request.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/request", "key": "request"}, "./node_modules/@umijs/plugins/dist/styled-components": {"config": {}, "time": {"hooks": {}, "register": 9}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/styled-components.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/styled-components", "key": "styledComponents"}, "./node_modules/@umijs/plugins/dist/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/tailwindcss.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/tailwindcss", "key": "tailwindcss"}, "./node_modules/@umijs/plugins/dist/valtio": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/plugins/dist/valtio.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/plugins/dist/valtio", "key": "valtio"}, "./node_modules/@umijs/max/dist/plugins/maxAlias": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 8}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/max/dist/plugins/maxAlias.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/max/dist/plugins/maxAlias", "key": "max<PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/max/dist/plugins/maxAppData": {"config": {}, "time": {"hooks": {"modifyAppData": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/max/dist/plugins/maxAppData.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/max/dist/plugins/maxAppData", "key": "maxAppData"}, "./node_modules/@umijs/max/dist/plugins/maxChecker": {"config": {}, "time": {"hooks": {"onCheckPkgJSON": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/max/dist/plugins/maxChecker.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/max/dist/plugins/maxChecker", "key": "max<PERSON><PERSON><PERSON>"}, "./node_modules/umi-presets-pro/dist/features/proconfig": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/umi-presets-pro/dist/features/proconfig.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/umi-presets-pro/dist/features/proconfig", "key": "proconfig"}, "./node_modules/umi-presets-pro/dist/features/maxtabs": {"config": {}, "time": {"hooks": {}, "register": 37}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/umi-presets-pro/dist/features/maxtabs.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/umi-presets-pro/dist/features/maxtabs", "key": "maxtabs"}, "@umijs/max-plugin-openapi": {"config": {}, "time": {"hooks": {}, "register": 2159}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/max-plugin-openapi/dist/index.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "@umijs/max-plugin-openapi", "key": "openAPI"}, "./node_modules/@alita/plugins/dist/keepalive": {"config": {}, "time": {"hooks": {}, "register": 202}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@alita/plugins/dist/keepalive.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@alita/plugins/dist/keepalive", "key": "keepalive"}, "./node_modules/@alita/plugins/dist/tabs-layout": {"config": {"onChange": "regenerateTmpFiles"}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@alita/plugins/dist/tabs-layout.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@alita/plugins/dist/tabs-layout", "key": "tabsLayout"}, "@umijs/request-record": {"config": {"default": {"mock": {"outputDir": "./mock", "fileName": "requestRecord.mock.js", "usingRole": "default"}, "outputDir": "./types"}}, "time": {"hooks": {}, "register": 325}, "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/request-record/dist/cjs/index.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "@umijs/request-record", "key": "requestRecord"}, "./node_modules/@umijs/core/dist/service/generatePlugin": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/frontend/node_modules/@umijs/core/dist/service/generatePlugin.js", "cwd": "F:\\htdocs\\development_team\\frontend", "id": "./node_modules/@umijs/core/dist/service/generatePlugin", "key": "generatePlugin"}}, "presets": [], "name": "dev", "args": {"_": []}, "userConfig": {"hash": true, "base": "/development_team/v2/", "publicPath": "/development_team/v2/", "outputPath": "../v2", "routes": [{"path": "/user", "layout": false, "routes": [{"name": "<PERSON><PERSON>", "path": "/user/login", "component": "./User/Login"}]}, {"key": "dispatch", "path": "/tenant", "name": "Dispatch", "routes": [{"path": "/tenant/dashboard", "name": "Dispatches", "external": true}, {"path": "/tenant/zone/list", "name": "Zones", "external": true}]}, {"path": "/tenant/invoice", "name": "Estimates & Invoices", "routes": [{"path": "/tenant/invoice/estimate/list", "name": "Estimates", "component": "./tenant/Estimate"}, {"path": "/tenant/invoice/list", "name": "Invoices", "component": "./tenant/Invoice"}]}, {"path": "/tenant/pricebook", "name": "Pricebook", "routes": [{"path": "/tenant/pricebook/service", "name": "Services", "external": true}, {"path": "/tenant/pricebook/service-category", "name": "Service Category", "external": true}]}, {"path": "tenant/employee", "name": "Employees", "routes": [{"path": "/tenant/employee/list", "name": "Employees List", "external": true}, {"path": "/tenant/employee/add", "name": "New Employees", "external": true}, {"path": "/tenant/employee/roles", "name": "Employee Roles List", "external": true}]}, {"path": "/tenant/driver", "name": "Driver / Device", "routes": [{"path": "/tenant/driver/device/list", "name": "Devices List", "external": true}]}, {"path": "/tenant/integration", "name": "Integration", "routes": [{"path": "/tenant/integration/twilio", "name": "<PERSON><PERSON><PERSON>", "external": true}]}, {"path": "/tenant/gps", "name": "GPS", "routes": [{"path": "/tenant/gps", "redirect": "/tenant/gps/job-board"}, {"path": "/tenant/gps/job-board", "name": "GPS Job Board", "component": "./tenant/gps/GpsJobBoard"}, {"path": "/tenant/gps/job-notes-management", "name": "Job Notes Management", "component": "./tenant/gps/GpsDispatchNoteList"}, {"path": "/tenant/gps/statuses", "name": "GPS Job Statuses", "component": "./tenant/gps/GpsDispatchStatusList"}]}, {"path": "/tenant/dispatch", "name": "Dispatch", "routes": [{"path": "/tenant/dispatch/list", "name": "Dispatch List", "component": "./tenant/dispatch/DispatchDashboard"}, {"path": "/tenant/dispatch/dashboard", "name": "Dispatch Dashboard", "component": "./tenant/dispatch/DispatchDashboard"}]}, {"path": "/tenant/settings", "name": "Settings", "routes": [{"path": "/tenant/settings/system-message-template/edit", "name": "Automatic SMS Template", "external": true}, {"path": "/tenant/settings/dispatch/greetings", "name": "Automatic SMS Logs", "external": true}, {"path": "/tenant/settings/address", "name": "Tenant Addresses", "external": true}]}, {"path": "/tenant/call_board", "name": "Call Board", "external": true}, {"path": "/tenant/tenant_profile", "name": "My Profile", "external": true}, {"path": "/admin", "name": "Admin", "icon": "crown", "access": "canAdmin", "routes": [{"path": "/admin", "redirect": "/admin/sub-page"}, {"path": "/admin/sub-page", "name": "Sub page", "component": "./Admin"}]}, {"path": "/", "redirect": "/tenant/dispatch/list"}, {"path": "*", "layout": false, "component": "./404"}], "theme": {"root-entry-name": "variable"}, "ignoreMomentLocale": true, "fastRefresh": true, "model": {}, "initialState": {}, "title": "Fast Response", "layout": {"locale": false, "navTheme": "light", "colorPrimary": "#00796b", "layout": "top", "contentWidth": "Fluid", "fixedHeader": false, "fixSiderbar": true, "colorWeak": false, "title": "Fast Response", "pwa": true, "iconfontUrl": "", "token": {"header": {"colorHeaderTitle": "#ffffff", "colorBgHeader": "#00796b", "colorBgMenuItemSelected": "#00695c", "colorTextRightActionsItem": "#ffffff", "colorTextMenuSelected": "#ffffff"}, "sider": {"colorTextMenuTitle": "white"}, "pageContainer": {"paddingBlockPageContainerContent": 8, "paddingInlinePageContainerContent": 16}}, "favicon": "/v2/favicon.ico"}, "moment2dayjs": {"preset": "antd", "plugins": ["duration"]}, "locale": {"default": "en-US", "antd": true, "baseNavigator": false, "baseSeparator": "-"}, "antd": {"theme": {"components": {"Card": {"colorBorderSecondary": "#eeeeee"}, "Table": {"borderColor": "#dcdcdc", "rowSelectedBg": "rgb(216,241,238)"}, "Select": {"optionSelectedBg": "rgb(216,241,238)"}}, "token": {"colorBorder": "#dcdcdc"}}, "compact": false}, "request": {}, "access": {}, "headScripts": [{"src": "\\development_team\\v2\\scripts\\loading.js", "async": true}], "presets": ["umi-presets-pro"], "mako": {}, "esbuildMinifyIIFE": true, "requestRecord": {}, "define": {"API_URL": "http://localhost/development_team", "PUBLIC_PATH": "/development_team/v2/"}, "devtool": "eval"}, "mainConfigFile": "F:\\htdocs\\development_team\\frontend\\config\\config.ts", "config": {"routeLoader": {"moduleType": "esm"}, "mountElementId": "root", "history": {"type": "browser"}, "base": "/development_team/v2/", "svgr": {}, "publicPath": "/development_team/v2/", "mfsu": false, "ignoreMomentLocale": true, "externals": {}, "autoCSSModules": true, "alias": {"umi": "@@/exports", "react": "F:\\htdocs\\development_team\\frontend\\node_modules\\react", "react-dom": "F:\\htdocs\\development_team\\frontend\\node_modules\\react-dom", "react-router": "F:\\htdocs\\development_team\\frontend\\node_modules\\react-router", "react-router-dom": "F:\\htdocs\\development_team\\frontend\\node_modules\\react-router-dom", "@": "F:/htdocs/development_team/frontend/src", "@@": "F:/htdocs/development_team/frontend/src/.umi", "regenerator-runtime": "F:\\htdocs\\development_team\\frontend\\node_modules\\regenerator-runtime", "antd": "F:\\htdocs\\development_team\\frontend\\node_modules\\antd", "moment": "F:\\htdocs\\development_team\\frontend\\node_modules\\dayjs", "@umijs/max": "@@/exports"}, "requestRecord": {"mock": {"outputDir": "./mock", "fileName": "requestRecord.mock.js", "usingRole": "default"}, "outputDir": "./types"}, "hash": true, "outputPath": "../v2", "routes": [{"path": "/user", "layout": false, "routes": [{"name": "<PERSON><PERSON>", "path": "/user/login", "component": "./User/Login"}]}, {"key": "dispatch", "path": "/tenant", "name": "Dispatch", "routes": [{"path": "/tenant/dashboard", "name": "Dispatches", "external": true}, {"path": "/tenant/zone/list", "name": "Zones", "external": true}]}, {"path": "/tenant/invoice", "name": "Estimates & Invoices", "routes": [{"path": "/tenant/invoice/estimate/list", "name": "Estimates", "component": "./tenant/Estimate"}, {"path": "/tenant/invoice/list", "name": "Invoices", "component": "./tenant/Invoice"}]}, {"path": "/tenant/pricebook", "name": "Pricebook", "routes": [{"path": "/tenant/pricebook/service", "name": "Services", "external": true}, {"path": "/tenant/pricebook/service-category", "name": "Service Category", "external": true}]}, {"path": "tenant/employee", "name": "Employees", "routes": [{"path": "/tenant/employee/list", "name": "Employees List", "external": true}, {"path": "/tenant/employee/add", "name": "New Employees", "external": true}, {"path": "/tenant/employee/roles", "name": "Employee Roles List", "external": true}]}, {"path": "/tenant/driver", "name": "Driver / Device", "routes": [{"path": "/tenant/driver/device/list", "name": "Devices List", "external": true}]}, {"path": "/tenant/integration", "name": "Integration", "routes": [{"path": "/tenant/integration/twilio", "name": "<PERSON><PERSON><PERSON>", "external": true}]}, {"path": "/tenant/gps", "name": "GPS", "routes": [{"path": "/tenant/gps", "redirect": "/tenant/gps/job-board"}, {"path": "/tenant/gps/job-board", "name": "GPS Job Board", "component": "./tenant/gps/GpsJobBoard"}, {"path": "/tenant/gps/job-notes-management", "name": "Job Notes Management", "component": "./tenant/gps/GpsDispatchNoteList"}, {"path": "/tenant/gps/statuses", "name": "GPS Job Statuses", "component": "./tenant/gps/GpsDispatchStatusList"}]}, {"path": "/tenant/dispatch", "name": "Dispatch", "routes": [{"path": "/tenant/dispatch/list", "name": "Dispatch List", "component": "./tenant/dispatch/DispatchDashboard"}, {"path": "/tenant/dispatch/dashboard", "name": "Dispatch Dashboard", "component": "./tenant/dispatch/DispatchDashboard"}]}, {"path": "/tenant/settings", "name": "Settings", "routes": [{"path": "/tenant/settings/system-message-template/edit", "name": "Automatic SMS Template", "external": true}, {"path": "/tenant/settings/dispatch/greetings", "name": "Automatic SMS Logs", "external": true}, {"path": "/tenant/settings/address", "name": "Tenant Addresses", "external": true}]}, {"path": "/tenant/call_board", "name": "Call Board", "external": true}, {"path": "/tenant/tenant_profile", "name": "My Profile", "external": true}, {"path": "/admin", "name": "Admin", "icon": "crown", "access": "canAdmin", "routes": [{"path": "/admin", "redirect": "/admin/sub-page"}, {"path": "/admin/sub-page", "name": "Sub page", "component": "./Admin"}]}, {"path": "/", "redirect": "/tenant/dispatch/list"}, {"path": "*", "layout": false, "component": "./404"}], "theme": {"blue-base": "#1890ff", "blue-1": "#e6f7ff", "blue-2": "#bae7ff", "blue-3": "#91d5ff", "blue-4": "#69c0ff", "blue-5": "#40a9ff", "blue-6": "#1890ff", "blue-7": "#096dd9", "blue-8": "#0050b3", "blue-9": "#003a8c", "blue-10": "#002766", "purple-base": "#722ed1", "purple-1": "#f9f0ff", "purple-2": "#efdbff", "purple-3": "#d3adf7", "purple-4": "#b37feb", "purple-5": "#9254de", "purple-6": "#722ed1", "purple-7": "#531dab", "purple-8": "#391085", "purple-9": "#22075e", "purple-10": "#120338", "cyan-base": "#13c2c2", "cyan-1": "#e6fffb", "cyan-2": "#b5f5ec", "cyan-3": "#87e8de", "cyan-4": "#5cdbd3", "cyan-5": "#36cfc9", "cyan-6": "#13c2c2", "cyan-7": "#08979c", "cyan-8": "#006d75", "cyan-9": "#00474f", "cyan-10": "#002329", "green-base": "#52c41a", "green-1": "#f6ffed", "green-2": "#d9f7be", "green-3": "#b7eb8f", "green-4": "#95de64", "green-5": "#73d13d", "green-6": "#52c41a", "green-7": "#389e0d", "green-8": "#237804", "green-9": "#135200", "green-10": "#092b00", "magenta-base": "#eb2f96", "magenta-1": "#fff0f6", "magenta-2": "#ffd6e7", "magenta-3": "#ffadd2", "magenta-4": "#ff85c0", "magenta-5": "#f759ab", "magenta-6": "#eb2f96", "magenta-7": "#c41d7f", "magenta-8": "#9e1068", "magenta-9": "#780650", "magenta-10": "#520339", "pink-base": "#eb2f96", "pink-1": "#fff0f6", "pink-2": "#ffd6e7", "pink-3": "#ffadd2", "pink-4": "#ff85c0", "pink-5": "#f759ab", "pink-6": "#eb2f96", "pink-7": "#c41d7f", "pink-8": "#9e1068", "pink-9": "#780650", "pink-10": "#520339", "red-base": "#f5222d", "red-1": "#fff1f0", "red-2": "#ffccc7", "red-3": "#ffa39e", "red-4": "#ff7875", "red-5": "#ff4d4f", "red-6": "#f5222d", "red-7": "#cf1322", "red-8": "#a8071a", "red-9": "#820014", "red-10": "#5c0011", "orange-base": "#fa8c16", "orange-1": "#fff7e6", "orange-2": "#ffe7ba", "orange-3": "#ffd591", "orange-4": "#ffc069", "orange-5": "#ffa940", "orange-6": "#fa8c16", "orange-7": "#d46b08", "orange-8": "#ad4e00", "orange-9": "#873800", "orange-10": "#612500", "yellow-base": "#fadb14", "yellow-1": "#feffe6", "yellow-2": "#ffffb8", "yellow-3": "#fffb8f", "yellow-4": "#fff566", "yellow-5": "#ffec3d", "yellow-6": "#fadb14", "yellow-7": "#d4b106", "yellow-8": "#ad8b00", "yellow-9": "#876800", "yellow-10": "#614700", "volcano-base": "#fa541c", "volcano-1": "#fff2e8", "volcano-2": "#ffd8bf", "volcano-3": "#ffbb96", "volcano-4": "#ff9c6e", "volcano-5": "#ff7a45", "volcano-6": "#fa541c", "volcano-7": "#d4380d", "volcano-8": "#ad2102", "volcano-9": "#871400", "volcano-10": "#610b00", "geekblue-base": "#2f54eb", "geekblue-1": "#f0f5ff", "geekblue-2": "#d6e4ff", "geekblue-3": "#adc6ff", "geekblue-4": "#85a5ff", "geekblue-5": "#597ef7", "geekblue-6": "#2f54eb", "geekblue-7": "#1d39c4", "geekblue-8": "#10239e", "geekblue-9": "#061178", "geekblue-10": "#030852", "lime-base": "#a0d911", "lime-1": "#fcffe6", "lime-2": "#f4ffb8", "lime-3": "#eaff8f", "lime-4": "#d3f261", "lime-5": "#bae637", "lime-6": "#a0d911", "lime-7": "#7cb305", "lime-8": "#5b8c00", "lime-9": "#3f6600", "lime-10": "#254000", "gold-base": "#faad14", "gold-1": "#fffbe6", "gold-2": "#fff1b8", "gold-3": "#ffe58f", "gold-4": "#ffd666", "gold-5": "#ffc53d", "gold-6": "#faad14", "gold-7": "#d48806", "gold-8": "#ad6800", "gold-9": "#874d00", "gold-10": "#613400", "preset-colors": "pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,", "theme": "default", "ant-prefix": "ant", "html-selector": "html", "primary-color": "#1890ff", "primary-color-hover": "#40a9ff", "primary-color-active": "#096dd9", "primary-color-outline": "rgba(24, 144, 255, 0.2)", "processing-color": "#1890ff", "info-color": "#1890ff", "info-color-deprecated-bg": "#e6f7ff", "info-color-deprecated-border": "#91d5ff", "success-color": "#52c41a", "success-color-hover": "#73d13d", "success-color-active": "#389e0d", "success-color-outline": "rgba(82, 196, 26, 0.2)", "success-color-deprecated-bg": "#f6ffed", "success-color-deprecated-border": "#b7eb8f", "warning-color": "#faad14", "warning-color-hover": "#ffc53d", "warning-color-active": "#d48806", "warning-color-outline": "rgba(250, 173, 20, 0.2)", "warning-color-deprecated-bg": "#fffbe6", "warning-color-deprecated-border": "#ffe58f", "error-color": "#ff4d4f", "error-color-hover": "#ff7875", "error-color-active": "#d9363e", "error-color-outline": "rgba(255, 77, 79, 0.2)", "error-color-deprecated-bg": "#fff2f0", "error-color-deprecated-border": "#ffccc7", "highlight-color": "#ff4d4f", "normal-color": "#d9d9d9", "white": "#fff", "black": "#000", "primary-1": "#e6f7ff", "primary-2": "#bae7ff", "primary-3": "#91d5ff", "primary-4": "#69c0ff", "primary-5": "#40a9ff", "primary-6": "#1890ff", "primary-7": "#096dd9", "primary-8": "#0050b3", "primary-9": "#003a8c", "primary-10": "#002766", "component-background": "#fff", "popover-background": "#fff", "popover-customize-border-color": "#f0f0f0", "font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "code-family": "'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', Menlo, Courier, monospace", "text-color": "rgba(0, 0, 0, 0.85)", "text-color-secondary": "rgba(0, 0, 0, 0.45)", "text-color-inverse": "#fff", "icon-color": "inherit", "icon-color-hover": "rgba(0, 0, 0, 0.75)", "heading-color": "rgba(0, 0, 0, 0.85)", "text-color-dark": "rgba(255, 255, 255, 0.85)", "text-color-secondary-dark": "rgba(255, 255, 255, 0.65)", "text-selection-bg": "#1890ff", "font-variant-base": "tabular-nums", "font-feature-settings-base": "tnum", "font-size-base": "14px", "font-size-lg": "16px", "font-size-sm": "12px", "heading-1-size": "38px", "heading-2-size": "30px", "heading-3-size": "24px", "heading-4-size": "20px", "heading-5-size": "16px", "line-height-base": "1.5715", "border-radius-base": "2px", "border-radius-sm": "2px", "control-border-radius": "2px", "arrow-border-radius": "2px", "padding-lg": "24px", "padding-md": "16px", "padding-sm": "12px", "padding-xs": "8px", "padding-xss": "4px", "control-padding-horizontal": "12px", "control-padding-horizontal-sm": "8px", "margin-lg": "24px", "margin-md": "16px", "margin-sm": "12px", "margin-xs": "8px", "margin-xss": "4px", "height-base": "32px", "height-lg": "40px", "height-sm": "24px", "item-active-bg": "#e6f7ff", "item-hover-bg": "#f5f5f5", "iconfont-css-prefix": "anticon", "link-color": "#1890ff", "link-hover-color": "#40a9ff", "link-active-color": "#096dd9", "link-decoration": "none", "link-hover-decoration": "none", "link-focus-decoration": "none", "link-focus-outline": "0", "ease-base-out": "cubic-bezier(0.7, 0.3, 0.1, 1)", "ease-base-in": "cubic-bezier(0.9, 0, 0.3, 0.7)", "ease-out": "cubic-bezier(0.215, 0.61, 0.355, 1)", "ease-in": "cubic-bezier(0.55, 0.055, 0.675, 0.19)", "ease-in-out": "cubic-bezier(0.645, 0.045, 0.355, 1)", "ease-out-back": "cubic-bezier(0.12, 0.4, 0.29, 1.46)", "ease-in-back": "cubic-bezier(0.71, -0.46, 0.88, 0.6)", "ease-in-out-back": "cubic-bezier(0.71, -0.46, 0.29, 1.46)", "ease-out-circ": "cubic-bezier(0.08, 0.82, 0.17, 1)", "ease-in-circ": "cubic-bezier(0.6, 0.04, 0.98, 0.34)", "ease-in-out-circ": "cubic-bezier(0.78, 0.14, 0.15, 0.86)", "ease-out-quint": "cubic-bezier(0.23, 1, 0.32, 1)", "ease-in-quint": "cubic-bezier(0.755, 0.05, 0.855, 0.06)", "ease-in-out-quint": "cubic-bezier(0.86, 0, 0.07, 1)", "border-color-base": "#d9d9d9", "border-color-split": "#f0f0f0", "border-color-inverse": "#fff", "border-width-base": "1px", "border-style-base": "solid", "outline-blur-size": "0", "outline-width": "2px", "outline-color": "#1890ff", "outline-fade": "20%", "background-color-light": "#fafafa", "background-color-base": "#f5f5f5", "disabled-color": "rgba(0, 0, 0, 0.25)", "disabled-bg": "#f5f5f5", "disabled-active-bg": "#e6e6e6", "disabled-color-dark": "rgba(255, 255, 255, 0.35)", "shadow-color": "rgba(0, 0, 0, 0.15)", "shadow-color-inverse": "#fff", "box-shadow-base": "0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)", "shadow-1-up": "0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-down": "0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-left": "-6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-right": "6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03)", "shadow-2": "0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)", "btn-font-weight": "400", "btn-border-radius-base": "2px", "btn-border-radius-sm": "2px", "btn-border-width": "1px", "btn-border-style": "solid", "btn-shadow": "0 2px 0 rgba(0, 0, 0, 0.015)", "btn-primary-shadow": "0 2px 0 rgba(0, 0, 0, 0.045)", "btn-text-shadow": "0 -1px 0 rgba(0, 0, 0, 0.12)", "btn-primary-color": "#fff", "btn-primary-bg": "#1890ff", "btn-default-color": "rgba(0, 0, 0, 0.85)", "btn-default-bg": "#fff", "btn-default-border": "#d9d9d9", "btn-danger-color": "#fff", "btn-danger-bg": "#ff4d4f", "btn-danger-border": "#ff4d4f", "btn-disable-color": "rgba(0, 0, 0, 0.25)", "btn-disable-bg": "#f5f5f5", "btn-disable-border": "#d9d9d9", "btn-default-ghost-color": "#fff", "btn-default-ghost-bg": "transparent", "btn-default-ghost-border": "#fff", "btn-font-size-lg": "16px", "btn-font-size-sm": "14px", "btn-padding-horizontal-base": "15px", "btn-padding-horizontal-lg": "15px", "btn-padding-horizontal-sm": "7px", "btn-height-base": "32px", "btn-height-lg": "40px", "btn-height-sm": "24px", "btn-line-height": "1.5715", "btn-circle-size": "32px", "btn-circle-size-lg": "40px", "btn-circle-size-sm": "24px", "btn-square-size": "32px", "btn-square-size-lg": "40px", "btn-square-size-sm": "24px", "btn-square-only-icon-size": "16px", "btn-square-only-icon-size-sm": "14px", "btn-square-only-icon-size-lg": "18px", "btn-group-border": "#40a9ff", "btn-link-hover-bg": "transparent", "btn-text-hover-bg": "rgba(0, 0, 0, 0.018)", "checkbox-size": "16px", "checkbox-color": "#1890ff", "checkbox-check-color": "#fff", "checkbox-check-bg": "#fff", "checkbox-border-width": "1px", "checkbox-border-radius": "2px", "checkbox-group-item-margin-right": "8px", "descriptions-bg": "#fafafa", "descriptions-title-margin-bottom": "20px", "descriptions-default-padding": "16px 24px", "descriptions-middle-padding": "12px 24px", "descriptions-small-padding": "8px 16px", "descriptions-item-padding-bottom": "16px", "descriptions-item-trailing-colon": "true", "descriptions-item-label-colon-margin-right": "8px", "descriptions-item-label-colon-margin-left": "2px", "descriptions-extra-color": "rgba(0, 0, 0, 0.85)", "divider-text-padding": "1em", "divider-orientation-margin": "5%", "divider-color": "rgba(0, 0, 0, 0.06)", "divider-vertical-gutter": "8px", "dropdown-selected-color": "#1890ff", "dropdown-menu-submenu-disabled-bg": "#fff", "dropdown-selected-bg": "#e6f7ff", "empty-font-size": "14px", "radio-size": "16px", "radio-top": "0.2em", "radio-border-width": "1px", "radio-dot-size": "8px", "radio-dot-color": "#1890ff", "radio-dot-disabled-color": "rgba(0, 0, 0, 0.2)", "radio-solid-checked-color": "#fff", "radio-button-bg": "#fff", "radio-button-checked-bg": "#fff", "radio-button-color": "rgba(0, 0, 0, 0.85)", "radio-button-hover-color": "#40a9ff", "radio-button-active-color": "#096dd9", "radio-button-padding-horizontal": "15px", "radio-disabled-button-checked-bg": "#e6e6e6", "radio-disabled-button-checked-color": "rgba(0, 0, 0, 0.25)", "radio-wrapper-margin-right": "8px", "screen-xs": "480px", "screen-xs-min": "480px", "screen-sm": "576px", "screen-sm-min": "576px", "screen-md": "768px", "screen-md-min": "768px", "screen-lg": "992px", "screen-lg-min": "992px", "screen-xl": "1200px", "screen-xl-min": "1200px", "screen-xxl": "1600px", "screen-xxl-min": "1600px", "screen-xs-max": "575px", "screen-sm-max": "767px", "screen-md-max": "991px", "screen-lg-max": "1199px", "screen-xl-max": "1599px", "grid-columns": "24", "layout-header-background": "#001529", "layout-header-height": "64px", "layout-header-padding": "0 50px", "layout-header-color": "rgba(0, 0, 0, 0.85)", "layout-footer-padding": "24px 50px", "layout-footer-background": "#f0f2f5", "layout-sider-background": "#001529", "layout-trigger-height": "48px", "layout-trigger-background": "#002140", "layout-trigger-color": "#fff", "layout-zero-trigger-width": "36px", "layout-zero-trigger-height": "42px", "layout-sider-background-light": "#fff", "layout-trigger-background-light": "#fff", "layout-trigger-color-light": "rgba(0, 0, 0, 0.85)", "zindex-badge": "auto", "zindex-table-fixed": "2", "zindex-affix": "10", "zindex-back-top": "10", "zindex-picker-panel": "10", "zindex-popup-close": "10", "zindex-modal": "1000", "zindex-modal-mask": "1000", "zindex-message": "1010", "zindex-notification": "1010", "zindex-popover": "1030", "zindex-dropdown": "1050", "zindex-picker": "1050", "zindex-popoconfirm": "1060", "zindex-tooltip": "1070", "zindex-image": "1080", "animation-duration-slow": "0.3s", "animation-duration-base": "0.2s", "animation-duration-fast": "0.1s", "collapse-panel-border-radius": "2px", "dropdown-menu-bg": "#fff", "dropdown-vertical-padding": "5px", "dropdown-edge-child-vertical-padding": "4px", "dropdown-font-size": "14px", "dropdown-line-height": "22px", "label-required-color": "#ff4d4f", "label-color": "rgba(0, 0, 0, 0.85)", "form-warning-input-bg": "#fff", "form-item-margin-bottom": "24px", "form-item-trailing-colon": "true", "form-vertical-label-padding": "0 0 8px", "form-vertical-label-margin": "0", "form-item-label-font-size": "14px", "form-item-label-height": "32px", "form-item-label-colon-margin-right": "8px", "form-item-label-colon-margin-left": "2px", "form-error-input-bg": "#fff", "input-height-base": "32px", "input-height-lg": "40px", "input-height-sm": "24px", "input-padding-horizontal": "11px", "input-padding-horizontal-base": "11px", "input-padding-horizontal-sm": "7px", "input-padding-horizontal-lg": "11px", "input-padding-vertical-base": "4px", "input-padding-vertical-sm": "0px", "input-padding-vertical-lg": "6.5px", "input-placeholder-color": "#bfbfbf", "input-color": "rgba(0, 0, 0, 0.85)", "input-icon-color": "rgba(0, 0, 0, 0.85)", "input-border-color": "#d9d9d9", "input-bg": "#fff", "input-number-hover-border-color": "#40a9ff", "input-number-handler-active-bg": "#f4f4f4", "input-number-handler-hover-bg": "#40a9ff", "input-number-handler-bg": "#fff", "input-number-handler-border-color": "#d9d9d9", "input-addon-bg": "#fafafa", "input-hover-border-color": "#40a9ff", "input-disabled-bg": "#f5f5f5", "input-outline-offset": "0 0", "input-icon-hover-color": "rgba(0, 0, 0, 0.85)", "input-disabled-color": "rgba(0, 0, 0, 0.25)", "mentions-dropdown-bg": "#fff", "mentions-dropdown-menu-item-hover-bg": "#fff", "select-border-color": "#d9d9d9", "select-item-selected-color": "rgba(0, 0, 0, 0.85)", "select-item-selected-font-weight": "600", "select-dropdown-bg": "#fff", "select-item-selected-bg": "#e6f7ff", "select-item-active-bg": "#f5f5f5", "select-dropdown-vertical-padding": "5px", "select-dropdown-font-size": "14px", "select-dropdown-line-height": "22px", "select-dropdown-height": "32px", "select-background": "#fff", "select-clear-background": "#fff", "select-selection-item-bg": "#f5f5f5", "select-selection-item-border-color": "#f0f0f0", "select-single-item-height-lg": "40px", "select-multiple-item-height": "24px", "select-multiple-item-height-lg": "32px", "select-multiple-item-spacing-half": "2px", "select-multiple-disabled-background": "#f5f5f5", "select-multiple-item-disabled-color": "#bfbfbf", "select-multiple-item-disabled-border-color": "#d9d9d9", "cascader-bg": "#fff", "cascader-item-selected-bg": "#e6f7ff", "cascader-menu-bg": "#fff", "cascader-menu-border-color-split": "#f0f0f0", "cascader-dropdown-vertical-padding": "5px", "cascader-dropdown-edge-child-vertical-padding": "4px", "cascader-dropdown-font-size": "14px", "cascader-dropdown-line-height": "22px", "anchor-bg": "transparent", "anchor-border-color": "#f0f0f0", "anchor-link-top": "4px", "anchor-link-left": "16px", "anchor-link-padding": "4px 0 4px 16px", "tooltip-max-width": "250px", "tooltip-color": "#fff", "tooltip-bg": "rgba(0, 0, 0, 0.75)", "tooltip-arrow-width": "11.3137085px", "tooltip-distance": "14.3137085px", "tooltip-arrow-color": "rgba(0, 0, 0, 0.75)", "tooltip-border-radius": "2px", "popover-bg": "#fff", "popover-color": "rgba(0, 0, 0, 0.85)", "popover-min-width": "177px", "popover-min-height": "32px", "popover-arrow-width": "11.3137085px", "popover-arrow-color": "#fff", "popover-arrow-outer-color": "#fff", "popover-distance": "15.3137085px", "popover-padding-horizontal": "16px", "modal-header-padding-vertical": "16px", "modal-header-padding-horizontal": "24px", "modal-header-bg": "#fff", "modal-header-padding": "16px 24px", "modal-header-border-width": "1px", "modal-header-border-style": "solid", "modal-header-title-line-height": "22px", "modal-header-title-font-size": "16px", "modal-header-border-color-split": "#f0f0f0", "modal-header-close-size": "54px", "modal-content-bg": "#fff", "modal-heading-color": "rgba(0, 0, 0, 0.85)", "modal-close-color": "rgba(0, 0, 0, 0.45)", "modal-footer-bg": "transparent", "modal-footer-border-color-split": "#f0f0f0", "modal-footer-border-style": "solid", "modal-footer-padding-vertical": "10px", "modal-footer-padding-horizontal": "16px", "modal-footer-border-width": "1px", "modal-mask-bg": "rgba(0, 0, 0, 0.45)", "modal-confirm-title-font-size": "16px", "modal-border-radius": "2px", "progress-default-color": "#1890ff", "progress-remaining-color": "#f5f5f5", "progress-info-text-color": "rgba(0, 0, 0, 0.85)", "progress-radius": "100px", "progress-steps-item-bg": "#f3f3f3", "progress-text-font-size": "1em", "progress-text-color": "rgba(0, 0, 0, 0.85)", "progress-circle-text-font-size": "1em", "menu-inline-toplevel-item-height": "40px", "menu-item-height": "40px", "menu-item-group-height": "1.5715", "menu-collapsed-width": "80px", "menu-bg": "#fff", "menu-popup-bg": "#fff", "menu-item-color": "rgba(0, 0, 0, 0.85)", "menu-inline-submenu-bg": "#fafafa", "menu-highlight-color": "#1890ff", "menu-highlight-danger-color": "#ff4d4f", "menu-item-active-bg": "#e6f7ff", "menu-item-active-danger-bg": "#fff1f0", "menu-item-active-border-width": "3px", "menu-item-group-title-color": "rgba(0, 0, 0, 0.45)", "menu-item-vertical-margin": "4px", "menu-item-font-size": "14px", "menu-item-boundary-margin": "8px", "menu-item-padding-horizontal": "20px", "menu-item-padding": "0 20px", "menu-horizontal-line-height": "46px", "menu-icon-margin-right": "10px", "menu-icon-size": "14px", "menu-icon-size-lg": "16px", "menu-item-group-title-font-size": "14px", "menu-dark-color": "rgba(255, 255, 255, 0.65)", "menu-dark-danger-color": "#ff4d4f", "menu-dark-bg": "#001529", "menu-dark-arrow-color": "#fff", "menu-dark-inline-submenu-bg": "#000c17", "menu-dark-highlight-color": "#fff", "menu-dark-item-active-bg": "#1890ff", "menu-dark-item-active-danger-bg": "#ff4d4f", "menu-dark-selected-item-icon-color": "#fff", "menu-dark-selected-item-text-color": "#fff", "menu-dark-item-hover-bg": "transparent", "spin-dot-size-sm": "14px", "spin-dot-size": "20px", "spin-dot-size-lg": "32px", "table-bg": "#fff", "table-header-bg": "#fafafa", "table-header-color": "rgba(0, 0, 0, 0.85)", "table-header-sort-bg": "#f5f5f5", "table-row-hover-bg": "#fafafa", "table-selected-row-color": "inherit", "table-selected-row-bg": "#e6f7ff", "table-selected-row-hover-bg": "#dcf4ff", "table-expanded-row-bg": "#fbfbfb", "table-padding-vertical": "16px", "table-padding-horizontal": "16px", "table-padding-vertical-md": "12px", "table-padding-horizontal-md": "8px", "table-padding-vertical-sm": "8px", "table-padding-horizontal-sm": "8px", "table-border-color": "#f0f0f0", "table-border-radius-base": "2px", "table-footer-bg": "#fafafa", "table-footer-color": "rgba(0, 0, 0, 0.85)", "table-header-bg-sm": "#fafafa", "table-font-size": "14px", "table-font-size-md": "14px", "table-font-size-sm": "14px", "table-header-cell-split-color": "rgba(0, 0, 0, 0.06)", "table-header-sort-active-bg": "rgba(0, 0, 0, 0.04)", "table-fixed-header-sort-active-bg": "#f5f5f5", "table-header-filter-active-bg": "rgba(0, 0, 0, 0.04)", "table-filter-btns-bg": "inherit", "table-filter-dropdown-bg": "#fff", "table-expand-icon-bg": "#fff", "table-selection-column-width": "32px", "table-sticky-scroll-bar-bg": "rgba(0, 0, 0, 0.35)", "table-sticky-scroll-bar-radius": "4px", "tag-border-radius": "2px", "tag-default-bg": "#fafafa", "tag-default-color": "rgba(0, 0, 0, 0.85)", "tag-font-size": "12px", "tag-line-height": "20px", "picker-bg": "#fff", "picker-basic-cell-hover-color": "#f5f5f5", "picker-basic-cell-active-with-range-color": "#e6f7ff", "picker-basic-cell-hover-with-range-color": "#cbe6ff", "picker-basic-cell-disabled-bg": "rgba(0, 0, 0, 0.04)", "picker-border-color": "#f0f0f0", "picker-date-hover-range-border-color": "#7ec1ff", "picker-date-hover-range-color": "#cbe6ff", "picker-time-panel-column-width": "56px", "picker-time-panel-column-height": "224px", "picker-time-panel-cell-height": "28px", "picker-panel-cell-height": "24px", "picker-panel-cell-width": "36px", "picker-text-height": "40px", "picker-panel-without-time-cell-height": "66px", "calendar-bg": "#fff", "calendar-input-bg": "#fff", "calendar-border-color": "#fff", "calendar-item-active-bg": "#e6f7ff", "calendar-column-active-bg": "rgba(230, 247, 255, 0.2)", "calendar-full-bg": "#fff", "calendar-full-panel-bg": "#fff", "carousel-dot-width": "16px", "carousel-dot-height": "3px", "carousel-dot-active-width": "24px", "badge-height": "20px", "badge-height-sm": "14px", "badge-dot-size": "6px", "badge-font-size": "12px", "badge-font-size-sm": "12px", "badge-font-weight": "normal", "badge-status-size": "6px", "badge-text-color": "#fff", "badge-color": "#ff4d4f", "rate-star-color": "#fadb14", "rate-star-bg": "#f0f0f0", "rate-star-size": "20px", "rate-star-hover-scale": "scale(1.1)", "card-head-color": "rgba(0, 0, 0, 0.85)", "card-head-background": "transparent", "card-head-font-size": "16px", "card-head-font-size-sm": "14px", "card-head-padding": "16px", "card-head-padding-sm": "8px", "card-head-height": "48px", "card-head-height-sm": "36px", "card-inner-head-padding": "12px", "card-padding-base": "24px", "card-padding-base-sm": "12px", "card-actions-background": "#fff", "card-actions-li-margin": "12px 0", "card-skeleton-bg": "#cfd8dc", "card-background": "#fff", "card-shadow": "0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09)", "card-radius": "2px", "card-head-tabs-margin-bottom": "-17px", "card-head-extra-color": "rgba(0, 0, 0, 0.85)", "comment-bg": "inherit", "comment-padding-base": "16px 0", "comment-nest-indent": "44px", "comment-font-size-base": "14px", "comment-font-size-sm": "12px", "comment-author-name-color": "rgba(0, 0, 0, 0.45)", "comment-author-time-color": "#ccc", "comment-action-color": "rgba(0, 0, 0, 0.45)", "comment-action-hover-color": "#595959", "comment-actions-margin-bottom": "inherit", "comment-actions-margin-top": "12px", "comment-content-detail-p-margin-bottom": "inherit", "tabs-card-head-background": "#fafafa", "tabs-card-height": "40px", "tabs-card-active-color": "#1890ff", "tabs-card-horizontal-padding": "8px 16px", "tabs-card-horizontal-padding-sm": "6px 16px", "tabs-card-horizontal-padding-lg": "7px 16px 6px", "tabs-title-font-size": "14px", "tabs-title-font-size-lg": "16px", "tabs-title-font-size-sm": "14px", "tabs-ink-bar-color": "#1890ff", "tabs-bar-margin": "0 0 16px 0", "tabs-horizontal-gutter": "32px", "tabs-horizontal-margin": "0 0 0 32px", "tabs-horizontal-margin-rtl": "0 0 0 32px", "tabs-horizontal-padding": "12px 0", "tabs-horizontal-padding-lg": "16px 0", "tabs-horizontal-padding-sm": "8px 0", "tabs-vertical-padding": "8px 24px", "tabs-vertical-margin": "16px 0 0 0", "tabs-scrolling-size": "32px", "tabs-highlight-color": "#1890ff", "tabs-hover-color": "#40a9ff", "tabs-active-color": "#096dd9", "tabs-card-gutter": "2px", "tabs-card-tab-active-border-top": "2px solid transparent", "back-top-color": "#fff", "back-top-bg": "rgba(0, 0, 0, 0.45)", "back-top-hover-bg": "rgba(0, 0, 0, 0.85)", "avatar-size-base": "32px", "avatar-size-lg": "40px", "avatar-size-sm": "24px", "avatar-font-size-base": "18px", "avatar-font-size-lg": "24px", "avatar-font-size-sm": "14px", "avatar-bg": "#ccc", "avatar-color": "#fff", "avatar-border-radius": "2px", "avatar-group-overlapping": "-8px", "avatar-group-space": "3px", "avatar-group-border-color": "#fff", "switch-height": "22px", "switch-sm-height": "16px", "switch-min-width": "44px", "switch-sm-min-width": "28px", "switch-disabled-opacity": "0.4", "switch-color": "#1890ff", "switch-bg": "#fff", "switch-shadow-color": "rgba(0, 35, 11, 0.2)", "switch-padding": "2px", "switch-inner-margin-min": "7px", "switch-inner-margin-max": "25px", "switch-sm-inner-margin-min": "5px", "switch-sm-inner-margin-max": "18px", "pagination-item-bg": "#fff", "pagination-item-size": "32px", "pagination-item-size-sm": "24px", "pagination-font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "pagination-font-weight-active": "500", "pagination-item-bg-active": "#fff", "pagination-item-link-bg": "#fff", "pagination-item-disabled-color-active": "rgba(0, 0, 0, 0.25)", "pagination-item-disabled-bg-active": "#e6e6e6", "pagination-item-input-bg": "#fff", "pagination-mini-options-size-changer-top": "0px", "page-header-padding": "24px", "page-header-padding-vertical": "16px", "page-header-padding-breadcrumb": "12px", "page-header-content-padding-vertical": "12px", "page-header-back-color": "#000", "page-header-ghost-bg": "inherit", "page-header-heading-title": "20px", "page-header-heading-sub-title": "14px", "page-header-tabs-tab-font-size": "16px", "breadcrumb-base-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-last-item-color": "rgba(0, 0, 0, 0.85)", "breadcrumb-font-size": "14px", "breadcrumb-icon-font-size": "14px", "breadcrumb-link-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-link-color-hover": "rgba(0, 0, 0, 0.85)", "breadcrumb-separator-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-separator-margin": "0 8px", "slider-margin": "10px 6px 10px", "slider-rail-background-color": "#f5f5f5", "slider-rail-background-color-hover": "#e1e1e1", "slider-track-background-color": "#91d5ff", "slider-track-background-color-hover": "#69c0ff", "slider-handle-border-width": "2px", "slider-handle-background-color": "#fff", "slider-handle-color": "#91d5ff", "slider-handle-color-hover": "#69c0ff", "slider-handle-color-focus": "#46a6ff", "slider-handle-color-focus-shadow": "rgba(24, 144, 255, 0.12)", "slider-handle-color-tooltip-open": "#1890ff", "slider-handle-size": "14px", "slider-handle-margin-top": "-5px", "slider-handle-shadow": "0", "slider-dot-border-color": "#f0f0f0", "slider-dot-border-color-active": "#8cc8ff", "slider-disabled-color": "rgba(0, 0, 0, 0.25)", "slider-disabled-background-color": "#fff", "tree-bg": "#fff", "tree-title-height": "24px", "tree-child-padding": "18px", "tree-directory-selected-color": "#fff", "tree-directory-selected-bg": "#1890ff", "tree-node-hover-bg": "#f5f5f5", "tree-node-selected-bg": "#bae7ff", "collapse-header-padding": "12px 16px", "collapse-header-padding-extra": "40px", "collapse-header-bg": "#fafafa", "collapse-content-padding": "16px", "collapse-content-bg": "#fff", "collapse-header-arrow-left": "16px", "skeleton-color": "rgba(190, 190, 190, 0.2)", "skeleton-to-color": "rgba(129, 129, 129, 0.24)", "skeleton-paragraph-margin-top": "28px", "skeleton-paragraph-li-margin-top": "16px", "skeleton-paragraph-li-height": "16px", "skeleton-title-height": "16px", "skeleton-title-paragraph-margin-top": "24px", "transfer-header-height": "40px", "transfer-item-height": "32px", "transfer-disabled-bg": "#f5f5f5", "transfer-list-height": "200px", "transfer-item-hover-bg": "#f5f5f5", "transfer-item-selected-hover-bg": "#dcf4ff", "transfer-item-padding-vertical": "6px", "transfer-list-search-icon-top": "12px", "message-notice-content-padding": "10px 16px", "message-notice-content-bg": "#fff", "wave-animation-width": "6px", "alert-success-border-color": "#b7eb8f", "alert-success-bg-color": "#f6ffed", "alert-success-icon-color": "#52c41a", "alert-info-border-color": "#91d5ff", "alert-info-bg-color": "#e6f7ff", "alert-info-icon-color": "#1890ff", "alert-warning-border-color": "#ffe58f", "alert-warning-bg-color": "#fffbe6", "alert-warning-icon-color": "#faad14", "alert-error-border-color": "#ffccc7", "alert-error-bg-color": "#fff2f0", "alert-error-icon-color": "#ff4d4f", "alert-message-color": "rgba(0, 0, 0, 0.85)", "alert-text-color": "rgba(0, 0, 0, 0.85)", "alert-close-color": "rgba(0, 0, 0, 0.45)", "alert-close-hover-color": "rgba(0, 0, 0, 0.75)", "alert-no-icon-padding-vertical": "8px", "alert-with-description-no-icon-padding-vertical": "15px", "alert-with-description-padding-vertical": "15px", "alert-with-description-padding": "15px 15px 15px 24px", "alert-icon-top": "12.0005px", "alert-with-description-icon-size": "24px", "list-header-background": "transparent", "list-footer-background": "transparent", "list-empty-text-padding": "16px", "list-item-padding": "12px 0", "list-item-padding-sm": "8px 16px", "list-item-padding-lg": "16px 24px", "list-item-meta-margin-bottom": "16px", "list-item-meta-avatar-margin-right": "16px", "list-item-meta-title-margin-bottom": "12px", "list-customize-card-bg": "#fff", "list-item-meta-description-font-size": "14px", "statistic-title-font-size": "14px", "statistic-content-font-size": "24px", "statistic-unit-font-size": "24px", "statistic-font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "drawer-header-padding": "16px 24px", "drawer-bg": "#fff", "drawer-footer-padding-vertical": "10px", "drawer-footer-padding-horizontal": "16px", "drawer-header-close-size": "56px", "drawer-title-font-size": "16px", "drawer-title-line-height": "22px", "timeline-width": "2px", "timeline-color": "#f0f0f0", "timeline-dot-border-width": "2px", "timeline-dot-color": "#1890ff", "timeline-dot-bg": "#fff", "timeline-item-padding-bottom": "20px", "typography-title-font-weight": "600", "typography-title-margin-top": "1.2em", "typography-title-margin-bottom": "0.5em", "upload-actions-color": "rgba(0, 0, 0, 0.45)", "process-tail-color": "#f0f0f0", "steps-nav-arrow-color": "rgba(0, 0, 0, 0.25)", "steps-background": "#fff", "steps-icon-size": "32px", "steps-icon-custom-size": "32px", "steps-icon-custom-top": "0px", "steps-icon-custom-font-size": "24px", "steps-icon-top": "-0.5px", "steps-icon-font-size": "16px", "steps-icon-margin": "0 8px 0 0", "steps-title-line-height": "32px", "steps-small-icon-size": "24px", "steps-small-icon-margin": "0 8px 0 0", "steps-dot-size": "8px", "steps-dot-top": "2px", "steps-current-dot-size": "10px", "steps-description-max-width": "140px", "steps-nav-content-max-width": "auto", "steps-vertical-icon-width": "16px", "steps-vertical-tail-width": "16px", "steps-vertical-tail-width-sm": "12px", "notification-bg": "#fff", "notification-padding-vertical": "16px", "notification-padding-horizontal": "24px", "result-title-font-size": "24px", "result-subtitle-font-size": "14px", "result-icon-font-size": "72px", "result-extra-margin": "24px 0 0 0", "image-size-base": "48px", "image-font-size-base": "24px", "image-bg": "#f5f5f5", "image-color": "#fff", "image-mask-font-size": "16px", "image-preview-operation-size": "18px", "image-preview-operation-color": "rgba(255, 255, 255, 0.85)", "image-preview-operation-disabled-color": "rgba(255, 255, 255, 0.25)", "segmented-bg": "rgba(0, 0, 0, 0.04)", "segmented-hover-bg": "rgba(0, 0, 0, 0.06)", "segmented-selected-bg": "#fff", "segmented-label-color": "rgba(0, 0, 0, 0.65)", "segmented-label-hover-color": "#262626", "root-entry-name": "variable"}, "fastRefresh": true, "model": {}, "initialState": {}, "title": "Fast Response", "layout": {"locale": false, "navTheme": "light", "colorPrimary": "#00796b", "layout": "top", "contentWidth": "Fluid", "fixedHeader": false, "fixSiderbar": true, "colorWeak": false, "title": "Fast Response", "pwa": true, "iconfontUrl": "", "token": {"header": {"colorHeaderTitle": "#ffffff", "colorBgHeader": "#00796b", "colorBgMenuItemSelected": "#00695c", "colorTextRightActionsItem": "#ffffff", "colorTextMenuSelected": "#ffffff"}, "sider": {"colorTextMenuTitle": "white"}, "pageContainer": {"paddingBlockPageContainerContent": 8, "paddingInlinePageContainerContent": 16}}, "favicon": "/v2/favicon.ico"}, "moment2dayjs": {"preset": "antd", "plugins": ["duration"]}, "locale": {"default": "en-US", "antd": true, "baseNavigator": false, "baseSeparator": "-"}, "antd": {"theme": {"components": {"Card": {"colorBorderSecondary": "#eeeeee"}, "Table": {"borderColor": "#dcdcdc", "rowSelectedBg": "rgb(216,241,238)"}, "Select": {"optionSelectedBg": "rgb(216,241,238)"}}, "token": {"colorBorder": "#dcdcdc"}}, "compact": false, "configProvider": {"theme": {"components": {"Card": {"colorBorderSecondary": "#eeeeee"}, "Table": {"borderColor": "#dcdcdc", "rowSelectedBg": "rgb(216,241,238)"}, "Select": {"optionSelectedBg": "rgb(216,241,238)"}}, "token": {"colorBorder": "#dcdcdc"}}}}, "request": {}, "access": {}, "headScripts": [{"src": "\\development_team\\v2\\scripts\\loading.js", "async": true}], "presets": ["umi-presets-pro"], "mako": {"plugins": [{"name": "UmiHtmlGenerationMako"}]}, "esbuildMinifyIIFE": true, "define": {"API_URL": "http://localhost/development_team", "PUBLIC_PATH": "/development_team/v2/", "ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION": "", "REACT_APP_ENV": "dev"}, "devtool": "eval", "targets": {"chrome": 80}, "hmrGuardian": false}, "routes": {"1": {"path": "/user", "layout": false, "id": "1", "absPath": "/user"}, "2": {"name": "<PERSON><PERSON>", "path": "/user/login", "file": "@/pages/User/Login/index.tsx", "parentId": "1", "id": "2", "absPath": "/user/login", "__content": "import { LockOutlined, UserOutlined } from '@ant-design/icons';\r\nimport { Alert, message } from 'antd';\r\nimport React, { useState } from 'react';\r\nimport { ProFormText, LoginForm } from '@ant-design/pro-form';\r\nimport { useModel, IRoute } from 'umi';\r\nimport Footer from '@/components/Footer';\r\nimport { login } from '@/services/app/login';\r\nimport styles from './index.less';\r\nimport { LS_TOKEN_NAME } from '@/constants';\r\nimport { __getRoot } from 'umi';\r\nimport { getQueryParamInUrl, urlFull } from '@/util';\r\n\r\nconst LoginMessage: React.FC<{\r\n  content: string;\r\n}> = ({ content }) => (\r\n  <Alert\r\n    style={{\r\n      marginBottom: 24,\r\n    }}\r\n    message={content}\r\n    type=\"error\"\r\n    showIcon\r\n  />\r\n);\r\n\r\nconst Login: React.FC<IRoute> = () => {\r\n  const [errorMsg, setErrorMsg] = useState('');\r\n  const { initialState, setInitialState } = useModel('@@initialState');\r\n\r\n  const fetchUserInfo = async () => {\r\n    const userInfo = await initialState?.fetchUserInfo?.();\r\n\r\n    if (userInfo) {\r\n      await setInitialState((s: any) => ({ ...s, currentUser: userInfo }));\r\n    }\r\n\r\n    return userInfo;\r\n  };\r\n\r\n  const handleSubmit = async (values: API.LoginParams) => {\r\n    try {\r\n      const res = await login({ ...values, user_type: 'tenant' });\r\n      console.log('Login Response:', res);\r\n\r\n      const defaultLoginSuccessMessage = 'Login successful!';\r\n      message.success(defaultLoginSuccessMessage);\r\n      localStorage.setItem(LS_TOKEN_NAME, res.token || '');\r\n\r\n      if (!location) return;\r\n\r\n      const redirect = getQueryParamInUrl(location.href, 'redirect');\r\n\r\n      /* if (userInfo?.role === UserRole.EDITOR) {\r\n        // location.href = redirect || '/item/ean-detail';\r\n      } else if (userInfo?.role === UserRole.WAREHOUSE) {\r\n        // location.href = redirect || '/orders/order-detail';\r\n      } else {\r\n        location.href = redirect || '/';\r\n    } */\r\n      location.href = redirect || '/';\r\n\r\n      return;\r\n    } catch (error: any) {\r\n      console.error(error);\r\n      setErrorMsg('Incorrect username or password');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={styles.container}>\r\n      <div className={styles.content}>\r\n        <LoginForm\r\n          logo={<img src={`${PUBLIC_PATH}logo.png`} />}\r\n          title=\"Fast Response\"\r\n          subTitle={' '}\r\n          initialValues={{\r\n            autoLogin: true,\r\n          }}\r\n          onFinish={async (values) => {\r\n            await handleSubmit(values as API.LoginParams);\r\n          }}\r\n          submitter={{ searchConfig: { submitText: 'Login' } }}\r\n        >\r\n          {errorMsg && <LoginMessage content={errorMsg} />}\r\n\r\n          <ProFormText\r\n            name=\"email\"\r\n            fieldProps={{\r\n              size: 'large',\r\n              prefix: <UserOutlined className={styles.prefixIcon} />,\r\n            }}\r\n            placeholder={'Email'}\r\n            rules={[\r\n              {\r\n                required: true,\r\n                message: 'Please input your email!',\r\n              },\r\n            ]}\r\n          />\r\n          <ProFormText.Password\r\n            name=\"password\"\r\n            fieldProps={{\r\n              size: 'large',\r\n              prefix: <LockOutlined className={styles.prefixIcon} />,\r\n            }}\r\n            placeholder={'Password'}\r\n            rules={[\r\n              {\r\n                required: true,\r\n                message: 'Please input your password!',\r\n              },\r\n            ]}\r\n          />\r\n        </LoginForm>\r\n      </div>\r\n      <Footer />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Login;\r\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/User/Login/index.tsx"}, "3": {"key": "dispatch", "path": "/tenant", "name": "Dispatch", "parentId": "ant-design-pro-layout", "id": "3", "absPath": "/tenant"}, "4": {"path": "/tenant/dashboard", "name": "Dispatches", "external": true, "parentId": "3", "id": "4", "absPath": "/tenant/dashboard"}, "5": {"path": "/tenant/zone/list", "name": "Zones", "external": true, "parentId": "3", "id": "5", "absPath": "/tenant/zone/list"}, "6": {"path": "/tenant/invoice", "name": "Estimates & Invoices", "parentId": "ant-design-pro-layout", "id": "6", "absPath": "/tenant/invoice"}, "7": {"path": "/tenant/invoice/estimate/list", "name": "Estimates", "file": "@/pages/tenant/Estimate/index.tsx", "parentId": "6", "id": "7", "absPath": "/tenant/invoice/estimate/list", "__content": "import { getDispatchEstimateListByPage, getDispatchEstimatePdf } from \"@/services/app/tenant/dispatch/dispatch-estimate\";\r\nimport Util, { nf2, sn } from \"@/util\";\r\nimport { EditOutlined, FilePdfOutlined, PlusOutlined, SendOutlined } from \"@ant-design/icons\";\r\nimport {\r\n  ActionType,\r\n  PageContainer,\r\n  ProColumns,\r\n  ProForm,\r\n  ProFormDigit,\r\n  ProFormInstance,\r\n  ProFormSelect,\r\n  ProFormText,\r\n  ProTable,\r\n} from \"@ant-design/pro-components\";\r\nimport { Button, Card, message, notification, Space, Tag, TagProps, Typography } from \"antd\";\r\nimport { useEffect, useRef, useState } from \"react\";\r\nimport CreateForm from \"./components/CreateForm\";\r\nimport { DEFAULT_PER_PAGE_PAGINATION, EstimateStatus, EstimateStatusKv } from \"@/constants\";\r\nimport UpdateForm from \"./components/UpdateForm\";\r\nimport { addDispatchInvoiceByEstimate } from \"@/services/app/tenant/dispatch/dispatch-invoice\";\r\n\r\nexport const EstimateStatusComp: React.FC<{ status?: EstimateStatus | string }> = ({ status }) => {\r\n  let color: TagProps[\"color\"] = \"default\";\r\n  switch (status) {\r\n    case EstimateStatus.CLOSED:\r\n      color = \"green-inverse\";\r\n      break;\r\n    case EstimateStatus.APPROVED:\r\n      color = \"success\";\r\n      break;\r\n    case EstimateStatus.SENT:\r\n      color = \"blue\";\r\n      break;\r\n    case EstimateStatus.EXPIRED:\r\n      color = \"geekblue-inverse\";\r\n      break;\r\n    case EstimateStatus.DECLINED:\r\n      color = \"red\";\r\n      break;\r\n  }\r\n\r\n  return <Tag color={color as any}>{EstimateStatusKv[status || \"-\"] ?? \"-\"}</Tag>;\r\n};\r\n\r\ntype RecordType = API.DispatchEstimate;\r\n\r\nconst InvoiceListPage: React.FC = () => {\r\n  const actionRef = useRef<ActionType>(null);\r\n  const searchFormRef = useRef<ProFormInstance>(null);\r\n\r\n  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);\r\n  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);\r\n\r\n  const [currentRow, setCurrentRow] = useState<RecordType>();\r\n  const [selectedRowsState, setSelectedRows] = useState<RecordType[]>([]);\r\n\r\n  const columns: ProColumns<RecordType>[] = [\r\n    {\r\n      title: \"Status\",\r\n      dataIndex: \"status\",\r\n      sorter: true,\r\n      width: 100,\r\n      align: \"center\",\r\n      render(dom, entity) {\r\n        return <EstimateStatusComp status={entity.status as EstimateStatus} />;\r\n      },\r\n    },\r\n    {\r\n      title: \"Est. No\",\r\n      dataIndex: \"id\",\r\n      sorter: true,\r\n      width: 100,\r\n      copyable: true,\r\n      defaultSortOrder: \"descend\",\r\n    },\r\n    {\r\n      title: \"Purchase No\",\r\n      dataIndex: \"purchase_no\",\r\n      sorter: true,\r\n      width: 120,\r\n      copyable: true,\r\n    },\r\n    {\r\n      title: \"File\",\r\n      dataIndex: [\"file\", \"url\"],\r\n      ellipsis: true,\r\n      width: 40,\r\n      render(dom, entity, index, action, schema) {\r\n        return entity.file ? (\r\n          <Typography.Link href={`${API_URL}/${entity.file?.url}`} target=\"_blank\">\r\n            <FilePdfOutlined />\r\n          </Typography.Link>\r\n        ) : null;\r\n      },\r\n    },\r\n    {\r\n      title: \"Job ID\",\r\n      dataIndex: \"dispatch_id\",\r\n      sorter: true,\r\n      width: 80,\r\n      align: \"center\",\r\n    },\r\n    {\r\n      title: \"External ID\",\r\n      dataIndex: [\"dispatch\", \"external_id\"],\r\n      sorter: true,\r\n      copyable: true,\r\n      width: 110,\r\n      align: \"center\",\r\n    },\r\n    {\r\n      title: \"Customer\",\r\n      dataIndex: \"customer_name\",\r\n      ellipsis: true,\r\n      width: 150,\r\n    },\r\n    {\r\n      title: \"Customer address\",\r\n      dataIndex: \"customer_full_address\",\r\n      ellipsis: true,\r\n      width: 320,\r\n    },\r\n    {\r\n      title: \"Issue Date\",\r\n      dataIndex: \"issue_date\",\r\n      sorter: true,\r\n      valueType: \"date\",\r\n      width: 90,\r\n      render(dom, entity) {\r\n        return Util.dtToMDY(entity.issue_date);\r\n      },\r\n    },\r\n    {\r\n      title: \"Work Start Date\",\r\n      dataIndex: \"work_start_date\",\r\n      sorter: true,\r\n      valueType: \"date\",\r\n      width: 90,\r\n      render(dom, entity) {\r\n        return Util.dtToMDY(entity.work_start_date);\r\n      },\r\n    },\r\n\r\n    {\r\n      title: \"Sub Total\",\r\n      dataIndex: \"subtotal\",\r\n      width: 100,\r\n      valueType: \"money\",\r\n      align: \"right\",\r\n      className: \"bl-2 b-gray\",\r\n      render: (_, record) => `$${nf2(record.subtotal || 0)}`,\r\n    },\r\n    {\r\n      title: \"Tax %\",\r\n      dataIndex: \"tax_percent\",\r\n      width: 65,\r\n      valueType: \"money\",\r\n      align: \"right\",\r\n      render: (_, record) => (sn(record.tax_percent) > 0 ? `${nf2(record.tax_percent || 0)}%` : null),\r\n    },\r\n    {\r\n      title: \"Tax\",\r\n      dataIndex: \"tax_amount\",\r\n      width: 90,\r\n      valueType: \"money\",\r\n      align: \"right\",\r\n      render: (_, record) => (sn(record.tax_amount) > 0 ? `$${nf2(record.tax_amount || 0)}` : null),\r\n    },\r\n    {\r\n      title: \"Grand Total\",\r\n      dataIndex: \"grand_total\",\r\n      width: 100,\r\n      valueType: \"money\",\r\n      align: \"right\",\r\n      className: \"bl-2 b-gray\",\r\n      render: (_, record) => `$${nf2(record.grand_total || 0)}`,\r\n    },\r\n    {\r\n      title: \"Payment Terms\",\r\n      dataIndex: \"payment_terms\",\r\n      className: \"bl-2 b-gray\",\r\n      ellipsis: true,\r\n      width: 120,\r\n    },\r\n\r\n    {\r\n      title: \"Created On\",\r\n      sorter: true,\r\n      dataIndex: \"created_at\",\r\n      valueType: \"dateTime\",\r\n      width: 130,\r\n      render(__, record) {\r\n        return Util.dtToMDYHHMM(record.created_at);\r\n      },\r\n    },\r\n    {\r\n      title: \"Option\",\r\n      dataIndex: \"option\",\r\n      valueType: \"option\",\r\n      width: 100,\r\n      fixed: \"right\",\r\n      render: (_, record) => [\r\n        <Button\r\n          key=\"edit\"\r\n          size=\"small\"\r\n          icon={<EditOutlined />}\r\n          title=\"Edit estimate\"\r\n          onClick={() => {\r\n            handleUpdateModalVisible(true);\r\n            setCurrentRow(record);\r\n          }}\r\n        />,\r\n        <Button\r\n          key=\"pdf\"\r\n          size=\"small\"\r\n          title=\"Generate PDF\"\r\n          type={record.file ? \"primary\" : \"default\"}\r\n          icon={<FilePdfOutlined />}\r\n          onClick={() => {\r\n            const hide = message.loading(\"Downloading PDF...\", 0);\r\n            getDispatchEstimatePdf(sn(record.id))\r\n              .then((res) => {\r\n                window.open(`${API_URL}/${res.url}`, \"_blank\");\r\n                message.success(\"Downloaded PDF successfully!\");\r\n                actionRef.current?.reload();\r\n              })\r\n              .catch(Util.error)\r\n              .finally(hide);\r\n          }}\r\n        />,\r\n        <Button\r\n          key=\"convert\"\r\n          size=\"small\"\r\n          type=\"primary\"\r\n          title=\"Create new invoice from this estimation\"\r\n          icon={<SendOutlined />}\r\n          onClick={() => {\r\n            const hide = message.loading(\"Create new invoice from this estimation...\", 0);\r\n            addDispatchInvoiceByEstimate(sn(record.id))\r\n              .then((res) => {\r\n                notification.success({\r\n                  duration: 0,\r\n                  message: `Created new invoice #${res.invoice_no} successfully!`,\r\n                  description: (\r\n                    <Space direction=\"vertical\" size={8}>\r\n                      <Typography.Link href={`${API_URL}/${res.file?.url}`} target=\"_blank\">\r\n                        View invoice\r\n                      </Typography.Link>\r\n                      <Typography.Link href={`${PUBLIC_PATH}tenant/invoice/list?id=${res.id}`} target=\"_blank\">\r\n                        Go to Invoices...\r\n                      </Typography.Link>\r\n                    </Space>\r\n                  ),\r\n                });\r\n                actionRef.current?.reload();\r\n              })\r\n              .catch(Util.error)\r\n              .finally(hide);\r\n          }}\r\n        />,\r\n      ],\r\n    },\r\n  ];\r\n\r\n  useEffect(() => {}, []);\r\n\r\n  return (\r\n    <PageContainer>\r\n      <Card size=\"small\" style={{ marginBottom: 16 }}>\r\n        <ProForm\r\n          formRef={searchFormRef}\r\n          layout=\"inline\"\r\n          isKeyPressSubmit\r\n          submitter={{\r\n            resetButtonProps: { style: { marginLeft: \"auto\" } },\r\n            searchConfig: { submitText: \"Search\" },\r\n            submitButtonProps: { htmlType: \"submit\" },\r\n            onReset(value) {\r\n              searchFormRef.current?.resetFields();\r\n              actionRef.current?.reload();\r\n            },\r\n            onSubmit(value) {\r\n              actionRef.current?.reload();\r\n            },\r\n            render(props, dom) {\r\n              return (\r\n                <Space style={{ marginLeft: \"auto\" }} size={8}>\r\n                  {dom}\r\n                </Space>\r\n              );\r\n            },\r\n          }}\r\n        >\r\n          <ProFormText name=\"id\" label=\"Estimate No#\" width={140} />\r\n          <ProFormDigit name=\"dispatch_id\" label=\"Job ID#\" width=\"xs\" />\r\n          <ProFormDigit name=\"dispatch_external_id\" label=\"External ID#\" width={120} />\r\n          <ProFormText name=\"purchase_no\" label=\"Purchase No#\" width={140} />\r\n          <ProFormSelect\r\n            name=\"status\"\r\n            label=\"Status\"\r\n            colProps={{ flex: \"100px\" }}\r\n            placeholder={\"All\"}\r\n            formItemProps={{ style: { marginBottom: 0 } }}\r\n            valueEnum={EstimateStatusKv}\r\n            fieldProps={{\r\n              popupMatchSelectWidth: false,\r\n              onChange(value, option) {\r\n                actionRef.current?.reload();\r\n              },\r\n            }}\r\n          />\r\n          <ProFormText name=\"ft_keyWords\" placeholder=\"Search Customer / Address\" />\r\n        </ProForm>\r\n      </Card>\r\n\r\n      <ProTable<RecordType, API.PageParams>\r\n        headerTitle={\"Estimates list\"}\r\n        actionRef={actionRef}\r\n        rowKey=\"id\"\r\n        revalidateOnFocus={false}\r\n        size=\"small\"\r\n        search={false}\r\n        scroll={{ x: 1000 }}\r\n        toolBarRender={() => [\r\n          <Button\r\n            type=\"primary\"\r\n            key=\"primary\"\r\n            onClick={() => {\r\n              handleCreateModalVisible(true);\r\n            }}\r\n          >\r\n            <PlusOutlined /> New\r\n          </Button>,\r\n        ]}\r\n        pagination={{ defaultPageSize: DEFAULT_PER_PAGE_PAGINATION }}\r\n        request={(params, sort, filter) => {\r\n          // const sfValues = Util.getSfValues(\"invoice_list\", {});\r\n          const sfValues = searchFormRef.current?.getFieldsValue();\r\n          Util.setSfValues(\"invoice_list\", sfValues);\r\n\r\n          const newParams = {\r\n            ...sfValues,\r\n            ...params,\r\n            with: \"items,logs\",\r\n          };\r\n          return getDispatchEstimateListByPage(newParams, sort, filter);\r\n        }}\r\n        onRequestError={Util.error}\r\n        columns={columns}\r\n        /* rowSelection={{\r\n          onChange: (_, selectedRows) => {\r\n            setSelectedRows(selectedRows);\r\n          },\r\n        }} */\r\n        rowSelection={false}\r\n        tableAlertRender={false}\r\n        columnEmptyText=\"\"\r\n        locale={{ emptyText: <></> }}\r\n      />\r\n\r\n      <CreateForm\r\n        modalVisible={createModalVisible}\r\n        handleModalVisible={handleCreateModalVisible}\r\n        onSubmit={(values) => {\r\n          handleCreateModalVisible(false);\r\n          actionRef.current?.reload();\r\n          return Promise.resolve(true);\r\n        }}\r\n      />\r\n\r\n      <UpdateForm\r\n        modalVisible={updateModalVisible}\r\n        handleModalVisible={handleUpdateModalVisible}\r\n        initialValues={currentRow}\r\n        onSubmit={(values) => {\r\n          handleUpdateModalVisible(false);\r\n          actionRef.current?.reload();\r\n          return Promise.resolve(true);\r\n        }}\r\n      />\r\n\r\n      {/* {selectedRowsState?.length > 0 && (\r\n        <FooterToolbar extra={<SFooterToolbarExtra title={\"invoice\"} selectedRowsState={selectedRowsState} actionRef={actionRef} />}>\r\n          <BatchDeleteAction\r\n            title=\"invoice\"\r\n            onConfirm={async () => {\r\n              await deleteDispatchEstimate(selectedRowsState.map((row) => row.id)?.join(\",\"));\r\n              setSelectedRows([]);\r\n              actionRef.current?.reloadAndRest?.();\r\n            }}\r\n          />\r\n        </FooterToolbar>\r\n      )} */}\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default InvoiceListPage;\r\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/tenant/Estimate/index.tsx"}, "8": {"path": "/tenant/invoice/list", "name": "Invoices", "file": "@/pages/tenant/Invoice/index.tsx", "parentId": "6", "id": "8", "absPath": "/tenant/invoice/list", "__content": "import { getDispatchInvoiceListByPage, getDispatchInvoicePdf } from \"@/services/app/tenant/dispatch/dispatch-invoice\";\r\nimport Util, { nf2, sn } from \"@/util\";\r\nimport { EditOutlined, FilePdfOutlined, PlusOutlined } from \"@ant-design/icons\";\r\nimport {\r\n  ActionType,\r\n  PageContainer,\r\n  ProColumns,\r\n  ProForm,\r\n  ProFormDigit,\r\n  ProFormInstance,\r\n  ProFormSelect,\r\n  ProFormText,\r\n  ProTable,\r\n} from \"@ant-design/pro-components\";\r\nimport { Button, Card, message, Space, Tag, TagProps, Typography } from \"antd\";\r\nimport { useEffect, useRef, useState } from \"react\";\r\nimport CreateForm from \"./components/CreateForm\";\r\nimport { DEFAULT_PER_PAGE_PAGINATION, InvoiceStatus, InvoiceStatusKv } from \"@/constants\";\r\nimport UpdateForm from \"./components/UpdateForm\";\r\n\r\nexport const InvoiceStatusComp: React.FC<{ status?: InvoiceStatus | string }> = ({ status }) => {\r\n  let color: TagProps[\"color\"] = \"default\";\r\n  switch (status) {\r\n    case InvoiceStatus.CLOSED:\r\n      color = \"green-inverse\";\r\n      break;\r\n    case InvoiceStatus.PAID:\r\n      color = \"success\";\r\n      break;\r\n    case InvoiceStatus.SENT:\r\n      color = \"blue\";\r\n      break;\r\n    case InvoiceStatus.PENDING:\r\n      color = \"orange\";\r\n      break;\r\n    case InvoiceStatus.CANCELED:\r\n      color = \"geekblue-inverse\";\r\n      break;\r\n    case InvoiceStatus.REFUNDED:\r\n      color = \"red\";\r\n      break;\r\n  }\r\n\r\n  return <Tag color={color as any}>{InvoiceStatusKv[status || \"-\"] ?? \"-\"}</Tag>;\r\n};\r\n\r\ntype RecordType = API.DispatchInvoice;\r\n\r\nconst InvoiceListPage: React.FC = () => {\r\n  const actionRef = useRef<ActionType>(null);\r\n  const searchFormRef = useRef<ProFormInstance>(null);\r\n\r\n  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);\r\n  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);\r\n\r\n  const [currentRow, setCurrentRow] = useState<RecordType>();\r\n  const [selectedRowsState, setSelectedRows] = useState<RecordType[]>([]);\r\n\r\n  const columns: ProColumns<RecordType>[] = [\r\n    {\r\n      title: \"Status\",\r\n      dataIndex: \"status\",\r\n      sorter: true,\r\n      width: 100,\r\n      align: \"center\",\r\n      render(dom, entity, index, action, schema) {\r\n        return <InvoiceStatusComp status={entity.status as InvoiceStatus} />;\r\n      },\r\n    },\r\n    {\r\n      title: \"Invoice No\",\r\n      dataIndex: \"invoice_no\",\r\n      sorter: true,\r\n      width: 100,\r\n      copyable: true,\r\n      defaultSortOrder: \"descend\",\r\n    },\r\n    {\r\n      title: \"File\",\r\n      dataIndex: [\"file\", \"url\"],\r\n      ellipsis: true,\r\n      width: 40,\r\n      render(dom, entity, index, action, schema) {\r\n        return entity.file ? (\r\n          <Typography.Link href={`${API_URL}/${entity.file?.url}`} target=\"_blank\">\r\n            <FilePdfOutlined />\r\n          </Typography.Link>\r\n        ) : null;\r\n      },\r\n    },\r\n    {\r\n      title: \"Job ID\",\r\n      dataIndex: \"dispatch_id\",\r\n      sorter: true,\r\n      width: 80,\r\n      align: \"center\",\r\n    },\r\n    {\r\n      title: \"External ID\",\r\n      dataIndex: [\"dispatch\", \"external_id\"],\r\n      sorter: true,\r\n      copyable: true,\r\n      width: 110,\r\n      align: \"center\",\r\n    },\r\n    {\r\n      title: \"Customer\",\r\n      dataIndex: \"customer_name\",\r\n      ellipsis: true,\r\n      width: 150,\r\n    },\r\n    {\r\n      title: \"Issue Date\",\r\n      dataIndex: \"issue_date\",\r\n      sorter: true,\r\n      valueType: \"date\",\r\n      width: 90,\r\n      render(dom, entity, index, action, schema) {\r\n        return Util.dtToMDY(entity.issue_date);\r\n      },\r\n    },\r\n    {\r\n      title: \"Service Date\",\r\n      dataIndex: \"service_date\",\r\n      sorter: true,\r\n      valueType: \"date\",\r\n      width: 90,\r\n      render(dom, entity, index, action, schema) {\r\n        return Util.dtToMDY(entity.service_date);\r\n      },\r\n    },\r\n\r\n    {\r\n      title: \"Sub Total\",\r\n      dataIndex: \"subtotal\",\r\n      width: 100,\r\n      valueType: \"money\",\r\n      align: \"right\",\r\n      className: \"bl-2 b-gray\",\r\n      render: (_, record) => `$${nf2(record.subtotal || 0)}`,\r\n    },\r\n    {\r\n      title: \"Tax %\",\r\n      dataIndex: \"tax_percent\",\r\n      width: 65,\r\n      valueType: \"money\",\r\n      align: \"right\",\r\n      render: (_, record) => (sn(record.tax_percent) > 0 ? `${nf2(record.tax_percent || 0)}%` : null),\r\n    },\r\n    {\r\n      title: \"Tax\",\r\n      dataIndex: \"tax_amount\",\r\n      width: 90,\r\n      valueType: \"money\",\r\n      align: \"right\",\r\n      render: (_, record) => (sn(record.tax_amount) > 0 ? `$${nf2(record.tax_amount || 0)}` : null),\r\n    },\r\n    {\r\n      title: \"Grand Total\",\r\n      dataIndex: \"grand_total\",\r\n      width: 100,\r\n      valueType: \"money\",\r\n      align: \"right\",\r\n      className: \"bl-2 b-gray\",\r\n      render: (_, record) => `$${nf2(record.grand_total || 0)}`,\r\n    },\r\n    {\r\n      title: \"Payment Terms\",\r\n      dataIndex: \"payment_terms\",\r\n      className: \"bl-2 b-gray\",\r\n      ellipsis: true,\r\n      width: 120,\r\n    },\r\n    {\r\n      title: \"Billing address\",\r\n      dataIndex: \"billing_full_address\",\r\n      ellipsis: true,\r\n      width: 320,\r\n    },\r\n    {\r\n      title: \"Created On\",\r\n      sorter: true,\r\n      dataIndex: \"created_at\",\r\n      valueType: \"dateTime\",\r\n      width: 130,\r\n      render(__, record) {\r\n        return Util.dtToMDYHHMM(record.created_at);\r\n      },\r\n    },\r\n    {\r\n      title: \"Option\",\r\n      dataIndex: \"option\",\r\n      valueType: \"option\",\r\n      width: 100,\r\n      render: (_, record) => [\r\n        <Button\r\n          key=\"edit\"\r\n          size=\"small\"\r\n          icon={<EditOutlined />}\r\n          onClick={() => {\r\n            handleUpdateModalVisible(true);\r\n            setCurrentRow(record);\r\n          }}\r\n        />,\r\n        <Button\r\n          key=\"pdf\"\r\n          size=\"small\"\r\n          title=\"Generate PDF\"\r\n          type={record.file ? \"primary\" : \"default\"}\r\n          icon={<FilePdfOutlined />}\r\n          onClick={() => {\r\n            const hide = message.loading(\"Downloading PDF...\", 0);\r\n            getDispatchInvoicePdf(sn(record.id))\r\n              .then((res) => {\r\n                window.open(`${API_URL}/${res.url}`, \"_blank\");\r\n                message.success(\"Downloaded PDF successfully!\");\r\n                actionRef.current?.reload();\r\n              })\r\n              .catch(Util.error)\r\n              .finally(hide);\r\n          }}\r\n        />,\r\n      ],\r\n    },\r\n  ];\r\n\r\n  useEffect(() => {}, []);\r\n\r\n  return (\r\n    <PageContainer>\r\n      <Card size=\"small\" style={{ marginBottom: 16 }}>\r\n        <ProForm\r\n          formRef={searchFormRef}\r\n          layout=\"inline\"\r\n          isKeyPressSubmit\r\n          submitter={{\r\n            resetButtonProps: { style: { marginLeft: \"auto\" } },\r\n            searchConfig: { submitText: \"Search\" },\r\n            submitButtonProps: { htmlType: \"submit\" },\r\n            onReset(value) {\r\n              searchFormRef.current?.resetFields();\r\n              actionRef.current?.reload();\r\n            },\r\n            onSubmit(value) {\r\n              actionRef.current?.reload();\r\n            },\r\n            render(props, dom) {\r\n              return (\r\n                <Space style={{ marginLeft: \"auto\" }} size={8}>\r\n                  {dom}\r\n                </Space>\r\n              );\r\n            },\r\n          }}\r\n        >\r\n          <ProFormText name=\"invoice_no\" label=\"Invoice No#\" width={140} />\r\n          <ProFormDigit name=\"dispatch_id\" label=\"Job ID#\" width=\"xs\" />\r\n          <ProFormDigit name=\"dispatch_external_id\" label=\"External ID#\" width={120} />\r\n          <ProFormSelect\r\n            name=\"status\"\r\n            label=\"Status\"\r\n            colProps={{ flex: \"100px\" }}\r\n            placeholder={\"All\"}\r\n            formItemProps={{ style: { marginBottom: 0 } }}\r\n            valueEnum={InvoiceStatusKv}\r\n            fieldProps={{\r\n              popupMatchSelectWidth: false,\r\n              onChange(value, option) {\r\n                actionRef.current?.reload();\r\n              },\r\n            }}\r\n          />\r\n          <ProFormText name=\"ft_keyWords\" placeholder=\"Search Customer / Address\" />\r\n        </ProForm>\r\n      </Card>\r\n\r\n      <ProTable<RecordType, API.PageParams>\r\n        headerTitle={\"Invoices list\"}\r\n        actionRef={actionRef}\r\n        rowKey=\"id\"\r\n        revalidateOnFocus={false}\r\n        size=\"small\"\r\n        search={false}\r\n        scroll={{ x: 1000 }}\r\n        toolBarRender={() => [\r\n          <Button\r\n            type=\"primary\"\r\n            key=\"primary\"\r\n            onClick={() => {\r\n              handleCreateModalVisible(true);\r\n            }}\r\n          >\r\n            <PlusOutlined /> New\r\n          </Button>,\r\n        ]}\r\n        pagination={{ defaultPageSize: DEFAULT_PER_PAGE_PAGINATION }}\r\n        request={(params, sort, filter) => {\r\n          // const sfValues = Util.getSfValues(\"invoice_list\", {});\r\n          const sfValues = searchFormRef.current?.getFieldsValue();\r\n          Util.setSfValues(\"invoice_list\", sfValues);\r\n\r\n          const newParams = {\r\n            ...sfValues,\r\n            ...params,\r\n            with: \"items,logs,estimate,file\",\r\n          };\r\n          return getDispatchInvoiceListByPage(newParams, sort, filter);\r\n        }}\r\n        onRequestError={Util.error}\r\n        columns={columns}\r\n        /* rowSelection={{\r\n          onChange: (_, selectedRows) => {\r\n            setSelectedRows(selectedRows);\r\n          },\r\n        }} */\r\n        rowSelection={false}\r\n        tableAlertRender={false}\r\n        columnEmptyText=\"\"\r\n        locale={{ emptyText: <></> }}\r\n      />\r\n\r\n      <CreateForm\r\n        modalVisible={createModalVisible}\r\n        handleModalVisible={handleCreateModalVisible}\r\n        onSubmit={(values) => {\r\n          handleCreateModalVisible(false);\r\n          actionRef.current?.reload();\r\n          return Promise.resolve(true);\r\n        }}\r\n      />\r\n\r\n      <UpdateForm\r\n        modalVisible={updateModalVisible}\r\n        handleModalVisible={handleUpdateModalVisible}\r\n        initialValues={currentRow}\r\n        onSubmit={(values) => {\r\n          handleUpdateModalVisible(false);\r\n          actionRef.current?.reload();\r\n          return Promise.resolve(true);\r\n        }}\r\n      />\r\n\r\n      {/* {selectedRowsState?.length > 0 && (\r\n        <FooterToolbar extra={<SFooterToolbarExtra title={\"invoice\"} selectedRowsState={selectedRowsState} actionRef={actionRef} />}>\r\n          <BatchDeleteAction\r\n            title=\"invoice\"\r\n            onConfirm={async () => {\r\n              await deleteDispatchInvoice(selectedRowsState.map((row) => row.id)?.join(\",\"));\r\n              setSelectedRows([]);\r\n              actionRef.current?.reloadAndRest?.();\r\n            }}\r\n          />\r\n        </FooterToolbar>\r\n      )} */}\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default InvoiceListPage;\r\n\r\n\r\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/tenant/Invoice/index.tsx"}, "9": {"path": "/tenant/pricebook", "name": "Pricebook", "parentId": "ant-design-pro-layout", "id": "9", "absPath": "/tenant/pricebook"}, "10": {"path": "/tenant/pricebook/service", "name": "Services", "external": true, "parentId": "9", "id": "10", "absPath": "/tenant/pricebook/service"}, "11": {"path": "/tenant/pricebook/service-category", "name": "Service Category", "external": true, "parentId": "9", "id": "11", "absPath": "/tenant/pricebook/service-category"}, "12": {"path": "tenant/employee", "name": "Employees", "parentId": "ant-design-pro-layout", "id": "12", "absPath": "/tenant/employee"}, "13": {"path": "/tenant/employee/list", "name": "Employees List", "external": true, "parentId": "12", "id": "13", "absPath": "/tenant/employee/list"}, "14": {"path": "/tenant/employee/add", "name": "New Employees", "external": true, "parentId": "12", "id": "14", "absPath": "/tenant/employee/add"}, "15": {"path": "/tenant/employee/roles", "name": "Employee Roles List", "external": true, "parentId": "12", "id": "15", "absPath": "/tenant/employee/roles"}, "16": {"path": "/tenant/driver", "name": "Driver / Device", "parentId": "ant-design-pro-layout", "id": "16", "absPath": "/tenant/driver"}, "17": {"path": "/tenant/driver/device/list", "name": "Devices List", "external": true, "parentId": "16", "id": "17", "absPath": "/tenant/driver/device/list"}, "18": {"path": "/tenant/integration", "name": "Integration", "parentId": "ant-design-pro-layout", "id": "18", "absPath": "/tenant/integration"}, "19": {"path": "/tenant/integration/twilio", "name": "<PERSON><PERSON><PERSON>", "external": true, "parentId": "18", "id": "19", "absPath": "/tenant/integration/twilio"}, "20": {"path": "/tenant/gps", "name": "GPS", "parentId": "ant-design-pro-layout", "id": "20", "absPath": "/tenant/gps"}, "21": {"path": "/tenant/gps", "redirect": "/tenant/gps/job-board", "parentId": "20", "id": "21", "absPath": "/tenant/gps"}, "22": {"path": "/tenant/gps/job-board", "name": "GPS Job Board", "file": "@/pages/tenant/gps/GpsJobBoard/index.tsx", "parentId": "20", "id": "22", "absPath": "/tenant/gps/job-board", "__content": "import { <PERSON><PERSON>, <PERSON>, Col, List, Row, Space, Splitter, Tag, Typography } from \"antd\";\r\nimport React, { useState, useRef, useCallback, useEffect, useMemo } from \"react\";\r\nimport { PageContainer } from \"@ant-design/pro-layout\";\r\nimport type { ActionType } from \"@ant-design/pro-table\";\r\nimport Util, { isNumeric } from \"@/util\";\r\nimport { ProForm, ProFormInstance, ProFormSelect, ProFormText, ProList } from \"@ant-design/pro-components\";\r\nimport { getDispatchListByPage } from \"@/services/app/tenant/dispatch/dispatch\";\r\nimport styles from \"./index.less\";\r\nimport { getDriverDeviceListByPage } from \"@/services/app/tenant/gps/driver-device\";\r\nimport { CardProps } from \"antd/lib\";\r\nimport { Link } from \"@umijs/max\";\r\nimport UpdateNotesForm from \"./components/UpdateNotesForm\";\r\nimport { SyncOutlined } from \"@ant-design/icons\";\r\n\r\nfunction agoString(iso: any, dt?: string) {\r\n  const diff = iso;\r\n  if (diff && isNumeric(diff)) {\r\n    if (diff < 60) return \"just now\";\r\n    if (diff < 3600) return Math.floor(diff / 60) + \" min ago\";\r\n    if (diff < 14400) return Math.floor(diff / 3600) + \" hr ago\";\r\n\r\n    const hrs = Math.floor(diff / 3600);\r\n    if (hrs > 24) {\r\n      return `${Math.floor(hrs / 24)}d ${hrs % 24} hr ago`;\r\n    } else {\r\n      return `${hrs} hr ago`;\r\n    }\r\n  }\r\n\r\n  if (dt) return new Date(dt).toLocaleString();\r\n}\r\n\r\nconst CardPropsInSideBar: CardProps = {\r\n  size: \"small\",\r\n  variant: \"borderless\",\r\n  styles: {\r\n    extra: { alignSelf: \"flex-start\", paddingTop: 4 },\r\n    body: { maxHeight: \"calc(100vh - 140px)\", overflowY: \"auto\", paddingRight: 0 },\r\n  },\r\n  style: { borderRadius: 0, height: \"100%\" },\r\n};\r\n\r\nconst GpsJobBoard: React.FC = () => {\r\n  const formRef = useRef<ProFormInstance>(null);\r\n  const actionRef = useRef<ActionType>(null);\r\n\r\n  const [currentRow, setCurrentRow] = useState<API.Dispatch>();\r\n  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);\r\n\r\n  const [loadingDevices, setLoadingDevices] = useState<boolean>(false);\r\n  const [devices, setDevices] = useState<API.DriverDevice[]>([]);\r\n\r\n  const loadDevices = useCallback(() => {\r\n    setLoadingDevices(true);\r\n    getDriverDeviceListByPage({ pageSize: 500, with: \"lastPosition\" })\r\n      .then((res) => {\r\n        setDevices(res.data);\r\n      })\r\n      .catch(Util.error)\r\n      .finally(() => {\r\n        setLoadingDevices(false);\r\n      });\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    loadDevices();\r\n  }, [loadDevices]);\r\n\r\n  const activeDevices = useMemo(() => {\r\n    return devices.filter((x) => x.last_position?.is_active == 1);\r\n  }, [devices]);\r\n\r\n  const inactiveDevices = useMemo(() => {\r\n    return devices.filter((x) => x.last_position?.is_active != 1);\r\n  }, [devices]);\r\n\r\n  const devicesOptions = useMemo(() => {\r\n    return devices.map((x) => ({ id: x.id, value: x.id, label: x.display_name }));\r\n  }, [devices]);\r\n\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      loadDevices();\r\n    }, 10000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [loadDevices]);\r\n\r\n  return (\r\n    <PageContainer title={false} className={styles.gpsJobBoard}>\r\n      <Splitter layout=\"horizontal\">\r\n        <Splitter.Panel defaultSize={280} max={280} collapsible style={{ height: \"calc(100vh - 80px)\" }}>\r\n          <Card\r\n            {...{\r\n              ...CardPropsInSideBar,\r\n              styles: {\r\n                ...CardPropsInSideBar.styles,\r\n                body: { maxHeight: \"calc(100vh - 160px)\", overflowY: \"auto\", paddingRight: 0 },\r\n              },\r\n            }}\r\n            title={\r\n              <>\r\n                <h2>Active Drivers</h2>\r\n                <div className=\"color-key\" style={{ display: \"flex\", alignItems: \"center\" }}>\r\n                  <span className=\"key-dot green\"></span> &lt;2min\r\n                  <span className=\"key-dot gray\"></span> 2–60min\r\n                  <span className=\"key-dot red\"></span> 1–4hr\r\n                  <svg className=\"home-icon-svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 16 16\">\r\n                    <path d=\"M8.707 1.5a1 1 0 0 0-1.414 0L.646 8.354a.5.5 0 0 0 .708.708L8 2.207l6.646 6.853a.5.5 0 0 0 .708-.708L8.707 1.5Z\" />\r\n                    <path d=\"m8 3.293 6 6V13.5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 13.5V9.293l6-6Z\" />\r\n                  </svg>{\" \"}\r\n                  Home\r\n                </div>\r\n              </>\r\n            }\r\n            className=\"sidebarCard\"\r\n            extra={<SyncOutlined spin style={{ visibility: loadingDevices ? \"visible\" : \"hidden\" }} />}\r\n          >\r\n            <ProList\r\n              dataSource={activeDevices}\r\n              size=\"small\"\r\n              renderItem={(item) => {\r\n                return (\r\n                  <List.Item key={item.id}>\r\n                    <Row gutter={4} wrap={false} style={{ width: \"100%\", height: \"100%\" }}>\r\n                      <Col flex=\"24px\">\r\n                        <span className=\"driver-dot green\"></span>\r\n                      </Col>\r\n                      <Col flex=\"120px\">\r\n                        <Typography.Text ellipsis>{item.display_name}</Typography.Text>\r\n                      </Col>\r\n                      <Col flex=\"auto\" style={{ textAlign: \"right\", fontSize: \"85%\" }} className=\"text-sm c-grey\">\r\n                        {agoString(item.last_position?.drive_status_duration_value, item.last_position?.dt_server)}\r\n                      </Col>\r\n                    </Row>\r\n                  </List.Item>\r\n                );\r\n              }}\r\n            />\r\n          </Card>\r\n        </Splitter.Panel>\r\n        <Splitter.Panel>\r\n          <div style={{ height: \"100%\" }}>\r\n            <Card\r\n              size=\"small\"\r\n              title={\r\n                <>\r\n                  <h2>Live Job Board</h2>\r\n                </>\r\n              }\r\n              variant=\"borderless\"\r\n              styles={{}}\r\n              style={{ borderRadius: 0, height: \"100%\" }}\r\n              extra={\r\n                <Space size={16}>\r\n                  <Button type=\"primary\" ghost>\r\n                    View/Edit Notes\r\n                  </Button>\r\n                  <Button type=\"primary\" ghost>\r\n                    View Map\r\n                  </Button>\r\n                  {/* <Button type=\"primary\" ghost>\r\n                    Driver Report\r\n                  </Button> */}\r\n                  <Link to={\"/tenant/gps/job-notes-management\"}>Management Page</Link>\r\n                  <Link to={\"/tenant/gps/statuses\"}>Control Panel</Link>\r\n                </Space>\r\n              }\r\n            >\r\n              <ProForm formRef={formRef} layout=\"horizontal\" grid submitter={false}>\r\n                <ProFormText name=\"\" colProps={{ span: 13 }} placeholder={\"Search by Customer Name...\"} />\r\n                <ProFormSelect name=\"device\" colProps={{ span: 4 }} options={[{ value: \"\", label: \"All Drivers\" }, ...devicesOptions]} />\r\n                <ProFormSelect name=\"\" colProps={{ span: 4 }} options={[{ value: \"\", label: \"All Statuses\" }]} />\r\n                <Col span=\"3\">\r\n                  <Button style={{ marginRight: 8 }}>Reset</Button>\r\n                  <Button\r\n                    type=\"primary\"\r\n                    onClick={() => {\r\n                      actionRef.current?.reload();\r\n                    }}\r\n                  >\r\n                    Search\r\n                  </Button>\r\n                </Col>\r\n              </ProForm>\r\n\r\n              <ProList\r\n                grid={{ gutter: 16, xs: 1, sm: 2, md: 3, lg: 3, xl: 4, xxl: 4 }}\r\n                itemCardProps={{ style: { height: \"100%\" } }}\r\n                cardProps={{ style: { height: \"100%\" } }}\r\n                actionRef={actionRef}\r\n                className=\"cardList\"\r\n                pagination={{ defaultPageSize: 500, hideOnSinglePage: true }}\r\n                polling={8000}\r\n                request={(params) => {\r\n                  const sfValues = Util.getSfValues(\"sf_gps_job_board\", {});\r\n                  const newParams = { ...sfValues, ...params };\r\n\r\n                  return getDispatchListByPage(newParams);\r\n                }}\r\n                renderItem={(item) => (\r\n                  <List.Item\r\n                    onClick={(e) => {\r\n                      console.log(\"itemClick\", e);\r\n                      setCurrentRow(item);\r\n                      handleUpdateModalVisible(true);\r\n                    }}\r\n                  >\r\n                    <Card\r\n                      title={item.customer?.name}\r\n                      size=\"small\"\r\n                      className=\"cardItem cursor-pointer\"\r\n                      extra={\r\n                        <Space size={0}>\r\n                          <Tag color=\"geekblue\">{item.env}</Tag>\r\n                          <Tag color=\"magenta\">{item.job_source}</Tag>\r\n                        </Space>\r\n                      }\r\n                    >\r\n                      <div>\r\n                        <label>Address:</label>\r\n                        <div>{item.address?.full_address}</div>\r\n                      </div>\r\n                      <Space style={{ width: \"100%\" }}>\r\n                        <label style={{ width: 100 }}>ID#</label>\r\n                        <div>{item.id}</div>\r\n                      </Space>\r\n                      <Space style={{ width: \"100%\" }}>\r\n                        <label style={{ width: 100 }}>Ext. ID#</label>\r\n                        <div>{item.external_id}</div>\r\n                      </Space>\r\n                    </Card>\r\n                  </List.Item>\r\n                )}\r\n              />\r\n            </Card>\r\n          </div>\r\n        </Splitter.Panel>\r\n        <Splitter.Panel defaultSize={280} max={280} collapsible>\r\n          <Card\r\n            {...{\r\n              ...CardPropsInSideBar,\r\n              styles: {\r\n                body: { maxHeight: \"calc(100vh - 160px)\", overflowY: \"auto\", paddingRight: 0 },\r\n              },\r\n            }}\r\n            title={\r\n              <>\r\n                <h2>Inactive Drivers</h2>\r\n                <div className=\"color-key\" style={{ display: \"flex\", alignItems: \"center\" }}>\r\n                  <span className=\"driver-dot idle\"></span> Idle&nbsp;&nbsp;\r\n                  <span className=\"key-dot red\"></span> Off&nbsp;&nbsp;\r\n                  <svg className=\"home-icon-svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 16 16\">\r\n                    <path d=\"M8.707 1.5a1 1 0 0 0-1.414 0L.646 8.354a.5.5 0 0 0 .708.708L8 2.207l6.646 6.853a.5.5 0 0 0 .708-.708L8.707 1.5Z\" />\r\n                    <path d=\"m8 3.293 6 6V13.5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 13.5V9.293l6-6Z\" />\r\n                  </svg>\r\n                  &nbsp; Home\r\n                </div>\r\n              </>\r\n            }\r\n            className=\"sidebarCard\"\r\n            extra={<SyncOutlined spin style={{ visibility: loadingDevices ? \"visible\" : \"hidden\" }} />}\r\n          >\r\n            <ProList\r\n              dataSource={inactiveDevices}\r\n              size=\"small\"\r\n              renderItem={(item) => {\r\n                let statusCls = \"driver-dot\";\r\n                if (item.last_position?.drive_status == \"off\") {\r\n                  statusCls += \" red\";\r\n                } else {\r\n                  statusCls += \" idle\";\r\n                }\r\n                return (\r\n                  <List.Item key={item.id}>\r\n                    <Row gutter={4} wrap={false} style={{ width: \"100%\", height: \"100%\" }}>\r\n                      <Col flex=\"24px\">\r\n                        <span className={statusCls}></span>\r\n                      </Col>\r\n                      <Col flex=\"120px\">\r\n                        <Typography.Text ellipsis>{item.display_name}</Typography.Text>\r\n                      </Col>\r\n                      <Col flex=\"auto\" style={{ textAlign: \"right\", fontSize: \"85%\" }} className=\"text-sm c-grey\">\r\n                        {agoString(item.last_position?.drive_status_duration_value, item.last_position?.dt_server)}\r\n                      </Col>\r\n                    </Row>\r\n                  </List.Item>\r\n                );\r\n              }}\r\n            />\r\n          </Card>\r\n        </Splitter.Panel>\r\n      </Splitter>\r\n\r\n      {currentRow ? (\r\n        <UpdateNotesForm\r\n          modalVisible={updateModalVisible}\r\n          handleModalVisible={handleUpdateModalVisible}\r\n          dispatch={currentRow}\r\n          devices={devicesOptions}\r\n        />\r\n      ) : null}\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default GpsJobBoard;\r\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/tenant/gps/GpsJobBoard/index.tsx"}, "23": {"path": "/tenant/gps/job-notes-management", "name": "Job Notes Management", "file": "@/pages/tenant/gps/GpsDispatchNoteList/index.tsx", "parentId": "20", "id": "23", "absPath": "/tenant/gps/job-notes-management", "__content": "import { getGpsDispatchNoteListByPage, updateGpsDispatchNote } from \"@/services/app/tenant/gps/gps-dispatch-notes\";\r\nimport Util, { sn } from \"@/util\";\r\nimport {\r\n  ActionType,\r\n  PageContainer,\r\n  ProColumns,\r\n  ProForm,\r\n  ProFormDatePicker,\r\n  ProFormInstance,\r\n  ProFormSelect,\r\n  ProFormText,\r\n  ProTable,\r\n} from \"@ant-design/pro-components\";\r\nimport { Link } from \"@umijs/max\";\r\nimport { Button, Card, message } from \"antd\";\r\nimport { useEffect, useRef } from \"react\";\r\nimport useGpsJobStatusOptions from \"../GpsJobBoard/hooks/useGpsJobStatusOptions\";\r\nimport { DefaultOptionType } from \"antd/es/select\";\r\nimport useDriverDeviceOptions from \"../GpsJobBoard/hooks/useDriverDeviceOptions\";\r\nimport styles from \"./index.less\";\r\nimport { DEFAULT_PER_PAGE_PAGINATION } from \"@/constants\";\r\n\r\nexport const ViewedOptions: DefaultOptionType[] = [\r\n  { value: \"new\", label: \"New\" },\r\n  { value: \"working\", label: \"Working\" },\r\n  { value: \"submitted\", label: \"Submitted\" },\r\n];\r\n\r\ntype RecordType = API.GpsDispatchNote;\r\nconst isShaded = (entity: RecordType) => entity.submitted || entity.acknowledged == \"submitted\";\r\n\r\nconst GpsDispatchNoteList: React.FC = () => {\r\n  const actionRef = useRef<ActionType>(null);\r\n  const searchFormRef = useRef<ProFormInstance>(null);\r\n\r\n  const { statusOptions, statusOptionsKv } = useGpsJobStatusOptions();\r\n  const { driverDeviceOptions } = useDriverDeviceOptions();\r\n\r\n  const defaultColumnProps: ProColumns<RecordType> = {\r\n    onCell: (entity) => {\r\n      const job_status = entity.job_status;\r\n      return {\r\n        style: {\r\n          background: isShaded(entity) ? \"auto\" : (statusOptionsKv?.[entity.job_status ?? \"\"]?.color_hex ?? \"auto\"),\r\n        },\r\n      };\r\n    },\r\n  };\r\n\r\n  const columns: ProColumns<RecordType>[] = [\r\n    {\r\n      title: \"Call By Call Date\",\r\n      dataIndex: \"note_date\",\r\n      width: 100,\r\n      ellipsis: true,\r\n      align: \"center\",\r\n      render(dom, entity) {\r\n        return Util.dtToMDY(entity.note_date);\r\n      },\r\n      ...defaultColumnProps,\r\n    },\r\n    {\r\n      title: \"Address\",\r\n      dataIndex: \"customer_full_address\",\r\n      width: 400,\r\n      ...defaultColumnProps,\r\n    },\r\n    {\r\n      title: \"Address/WO #\",\r\n      dataIndex: [\"dispatch\", \"external_id\"],\r\n      align: \"center\",\r\n      width: 120,\r\n      render(dom, entity, index, action, schema) {\r\n        const dispatch = entity.dispatch;\r\n        return dispatch && dispatch.external_id ? (\r\n          <a href={`https://work.dispatch.me/job/${dispatch.external_id}`} target=\"_blank\" rel=\"noreferrer\">\r\n            {dispatch.external_id}\r\n          </a>\r\n        ) : null;\r\n      },\r\n      ...defaultColumnProps,\r\n    },\r\n    {\r\n      title: \"Tech\",\r\n      dataIndex: \"driver_name\",\r\n      ellipsis: true,\r\n      width: 150,\r\n      ...defaultColumnProps,\r\n    },\r\n    {\r\n      title: \"Issue / Notes\",\r\n      dataIndex: \"issue_note\",\r\n      ellipsis: true,\r\n      width: 450,\r\n      ...defaultColumnProps,\r\n    },\r\n    {\r\n      title: \"Pics\",\r\n      dataIndex: \"has_pics\",\r\n      align: \"center\",\r\n      width: 50,\r\n      ...defaultColumnProps,\r\n    },\r\n    {\r\n      title: \"Inc. / Missing\",\r\n      dataIndex: \"job_status\",\r\n      width: 150,\r\n      render(dom, entity) {\r\n        return (\r\n          <ProFormSelect\r\n            name=\"job_status\"\r\n            fieldProps={{ defaultValue: entity.job_status, popupMatchSelectWidth: false }}\r\n            formItemProps={{ style: { marginBottom: 0 } }}\r\n            options={statusOptions}\r\n            placeholder={\"Job Status\"}\r\n            onChange={(value) => {\r\n              updateGpsDispatchNote(sn(entity.id), { job_status: value })\r\n                .then((res) => {\r\n                  message.success(\"Updated successfully.\");\r\n                  actionRef.current?.reload();\r\n                })\r\n                .catch(Util.error);\r\n            }}\r\n          />\r\n        );\r\n      },\r\n      ...defaultColumnProps,\r\n    },\r\n    {\r\n      title: \"Viewed\",\r\n      dataIndex: \"acknowledged\",\r\n      width: 120,\r\n      render(dom, entity) {\r\n        return (\r\n          <ProFormSelect\r\n            fieldProps={{ defaultValue: entity.acknowledged, popupMatchSelectWidth: false }}\r\n            formItemProps={{ style: { marginBottom: 0 } }}\r\n            options={ViewedOptions}\r\n            placeholder={\"\"}\r\n            onChange={(value) => {\r\n              updateGpsDispatchNote(sn(entity.id), { acknowledged: value })\r\n                .then((res) => {\r\n                  message.success(\"Updated successfully.\");\r\n                  actionRef.current?.reload();\r\n                })\r\n                .catch(Util.error);\r\n            }}\r\n          />\r\n        );\r\n      },\r\n      ...defaultColumnProps,\r\n    },\r\n\r\n    {\r\n      title: \"Request authorization\",\r\n      dataIndex: \"submitted\",\r\n      width: 200,\r\n      align: \"center\",\r\n      render(__, entity) {\r\n        return (\r\n          <Button\r\n            type=\"primary\"\r\n            disabled={!!entity.submitted}\r\n            onClick={(e) => {\r\n              const hide = message.loading(\"Requesting authorization...\", 0);\r\n              updateGpsDispatchNote(sn(entity.id), { submitted: 1 })\r\n                .then((res) => {\r\n                  message.success(\"Authorization has been sent successfully.\");\r\n                  actionRef.current?.reload();\r\n                })\r\n                .catch(Util.error)\r\n                .finally(() => {\r\n                  hide();\r\n                });\r\n            }}\r\n          >\r\n            {entity.submitted ? \"Authorization Sent\" : \"Request authorization\"}\r\n          </Button>\r\n        );\r\n      },\r\n      ...defaultColumnProps,\r\n    },\r\n    {\r\n      title: \"ID\",\r\n      dataIndex: \"id\",\r\n      ...defaultColumnProps,\r\n    },\r\n  ];\r\n\r\n  useEffect(() => {\r\n    searchFormRef.current?.setFieldsValue(Util.getSfValues(\"gps_notes_list\", {}));\r\n  }, []);\r\n\r\n  return (\r\n    <PageContainer\r\n      className={styles.gpsDispatchNoteList}\r\n      extra={\r\n        <>\r\n          <Link to={\"/tenant/gps/job-board\"}>Back to Job Board</Link>\r\n        </>\r\n      }\r\n    >\r\n      <Card size=\"small\">\r\n        <ProForm formRef={searchFormRef} layout=\"vertical\" grid submitter={false}>\r\n          <ProFormSelect\r\n            name=\"acknowledged\"\r\n            label=\"Viewed\"\r\n            colProps={{ flex: \"100px\" }}\r\n            options={ViewedOptions}\r\n            placeholder={\"All\"}\r\n            formItemProps={{ style: { marginBottom: 4 } }}\r\n            fieldProps={{\r\n              onChange(value, option) {\r\n                actionRef.current?.reload();\r\n              },\r\n            }}\r\n          />\r\n          <ProFormSelect\r\n            name=\"has_pics\"\r\n            label=\"Pics\"\r\n            colProps={{ flex: \"100px\" }}\r\n            options={[\"Yes\", \"No\"]}\r\n            placeholder={\"All\"}\r\n            formItemProps={{ style: { marginBottom: 4 } }}\r\n            fieldProps={{\r\n              onChange(value, option) {\r\n                actionRef.current?.reload();\r\n              },\r\n            }}\r\n          />\r\n          <ProFormSelect\r\n            name=\"job_status\"\r\n            label=\"Inc. / Missing\"\r\n            showSearch\r\n            allowClear\r\n            colProps={{ flex: \"170px\" }}\r\n            options={statusOptions}\r\n            placeholder={\"All\"}\r\n            formItemProps={{ style: { marginBottom: 4 } }}\r\n            fieldProps={{\r\n              onChange(value, option) {\r\n                actionRef.current?.reload();\r\n              },\r\n            }}\r\n          />\r\n          <ProFormSelect\r\n            name=\"device_id\"\r\n            label=\"Driver\"\r\n            showSearch\r\n            allowClear\r\n            colProps={{ flex: \"240px\" }}\r\n            options={driverDeviceOptions}\r\n            placeholder={\"All\"}\r\n            formItemProps={{ style: { marginBottom: 4 } }}\r\n            fieldProps={{\r\n              onChange(value, option) {\r\n                actionRef.current?.reload();\r\n              },\r\n            }}\r\n          />\r\n\r\n          <ProFormText\r\n            name=\"like_city\"\r\n            label=\"City\"\r\n            colProps={{ flex: \"160px\" }}\r\n            placeholder=\"Search city...\"\r\n            formItemProps={{ style: { marginBottom: 4 } }}\r\n          />\r\n\r\n          <ProFormDatePicker\r\n            name=\"note_date\"\r\n            label=\"Call By Call Date\"\r\n            format=\"MM/DD/YYYY\"\r\n            colProps={{ flex: \"130px\" }}\r\n            formItemProps={{ style: { marginBottom: 4 } }}\r\n          />\r\n\r\n          <ProFormText\r\n            name=\"like_customer_full_address\"\r\n            label=\"Address\"\r\n            colProps={{ flex: \"240px\" }}\r\n            placeholder=\"Search address...\"\r\n            formItemProps={{ style: { marginBottom: 4 } }}\r\n          />\r\n          <ProFormText\r\n            name=\"dispatch_external_id\"\r\n            label=\"Address/WO #\"\r\n            colProps={{ flex: \"200px\" }}\r\n            placeholder=\"Search WO #...\"\r\n            formItemProps={{ style: { marginBottom: 4 } }}\r\n          />\r\n\r\n          <ProFormSelect\r\n            name=\"submitted\"\r\n            label=\"Authorization\"\r\n            colProps={{ flex: \"100px\" }}\r\n            options={[\r\n              { value: 0, label: \"Not Submitted\" },\r\n              { value: 1, label: \"Submitted\" },\r\n            ]}\r\n            placeholder={\"All\"}\r\n            formItemProps={{ style: { marginBottom: 4 } }}\r\n            fieldProps={{\r\n              onChange(value, option) {\r\n                actionRef.current?.reload();\r\n              },\r\n            }}\r\n          />\r\n          <ProForm.Item colProps={{ flex: \"auto\" }} label=\"  \" style={{ marginLeft: \"auto\", marginBottom: 4 }}>\r\n            <Button\r\n              style={{ marginRight: 8 }}\r\n              onClick={() => {\r\n                searchFormRef.current?.resetFields();\r\n                actionRef.current?.reload();\r\n              }}\r\n            >\r\n              Reset\r\n            </Button>\r\n            <Button\r\n              type=\"primary\"\r\n              htmlType=\"submit\"\r\n              onClick={() => {\r\n                actionRef.current?.reload();\r\n              }}\r\n            >\r\n              Search\r\n            </Button>\r\n          </ProForm.Item>\r\n        </ProForm>\r\n      </Card>\r\n\r\n      <ProTable<RecordType, API.PageParams>\r\n        headerTitle={false}\r\n        actionRef={actionRef}\r\n        rowKey=\"id\"\r\n        revalidateOnFocus={false}\r\n        options={false}\r\n        size=\"small\"\r\n        pagination={{ defaultPageSize: sn(Util.getSfValues(\"gps_notes_list_p\")?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION) }}\r\n        request={async (params, sort, filter) => {\r\n          const sfValues = searchFormRef.current?.getFieldsValue();\r\n          Util.setSfValues(\"gps_notes_list\", sfValues);\r\n          Util.setSfValues(\"gps_notes_list_p\", params);\r\n\r\n          const res = await getGpsDispatchNoteListByPage(\r\n            { ...sfValues, ...params, with: \"dispatch,driverDevice\" },\r\n            { note_date: \"descend\", dispatch_id: \"descend\" },\r\n            filter,\r\n          );\r\n          return res;\r\n        }}\r\n        onRequestError={Util.error}\r\n        search={false}\r\n        columns={columns}\r\n        columnEmptyText=\"\"\r\n        tableAlertRender={false}\r\n        style={{ marginTop: 16 }}\r\n        sticky\r\n        rowClassName={(entity) => {\r\n          let cls = \"grid-row\";\r\n          if (entity.acknowledged == \"new\") {\r\n            cls += \" status-acknowledged-new\";\r\n          } else if (entity.acknowledged == \"working\") {\r\n            cls += \" status-acknowledged-working\";\r\n          }\r\n\r\n          if (entity.submitted || entity.acknowledged == \"submitted\") {\r\n            cls += \" shaded\";\r\n          }\r\n\r\n          return cls;\r\n        }}\r\n      />\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default GpsDispatchNoteList;\r\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/tenant/gps/GpsDispatchNoteList/index.tsx"}, "24": {"path": "/tenant/gps/statuses", "name": "GPS Job Statuses", "file": "@/pages/tenant/gps/GpsDispatchStatusList/index.tsx", "parentId": "20", "id": "24", "absPath": "/tenant/gps/statuses", "__content": "import { EditOutlined, PlusOutlined } from \"@ant-design/icons\";\r\nimport { <PERSON><PERSON>, ColorPicker, message } from \"antd\";\r\nimport React, { useState, useRef } from \"react\";\r\nimport { PageContainer, FooterToolbar } from \"@ant-design/pro-layout\";\r\nimport type { ProColumns, ActionType } from \"@ant-design/pro-table\";\r\nimport ProTable from \"@ant-design/pro-table\";\r\nimport { addUser } from \"@/services/app/user\";\r\nimport Util from \"@/util\";\r\nimport CreateForm from \"./components/CreateForm\";\r\nimport UpdateForm from \"./components/UpdateForm\";\r\nimport { deleteGpsDispatchStatus, getGpsDispatchStatusListByPage, updateGpsDispatchStatus } from \"@/services/app/tenant/gps/gps-dispatch-status\";\r\nimport BatchDeleteAction from \"@/components/Table/BatchDeleteAction\";\r\nimport { Link } from \"@umijs/max\";\r\n\r\n/**\r\n *  Delete node\r\n *\r\n * @param selectedRows\r\n */\r\n\r\nconst handleRemove = async (selectedRows: API.GpsDispatchStatus[]) => {\r\n  const hide = message.loading(\"Deleting\");\r\n  if (!selectedRows) return true;\r\n\r\n  try {\r\n    await deleteGpsDispatchStatus(selectedRows.map((row) => row.id)?.join(\",\"));\r\n    hide();\r\n    message.success(\"Deleted successfully and will refresh soon\");\r\n    return true;\r\n  } catch (error) {\r\n    hide();\r\n    Util.error(\"Delete failed, please try again!\");\r\n    return false;\r\n  }\r\n};\r\n\r\nconst GpsDispatchStatusList: React.FC = () => {\r\n  const [createModalVisible, handleModalVisible] = useState<boolean>(false);\r\n\r\n  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);\r\n  const actionRef = useRef<ActionType>(null);\r\n\r\n  const [currentRow, setCurrentRow] = useState<API.GpsDispatchStatus>();\r\n  const [selectedRowsState, setSelectedRows] = useState<API.GpsDispatchStatus[]>([]);\r\n\r\n  const [colors, setColors] = useState<any>({});\r\n\r\n  const columns: ProColumns<API.GpsDispatchStatus>[] = [\r\n    {\r\n      title: \"Status Name\",\r\n      dataIndex: \"status_name\",\r\n      sorter: true,\r\n      defaultSortOrder: \"ascend\",\r\n      width: 400,\r\n    },\r\n    {\r\n      title: \"Color\",\r\n      dataIndex: \"color_hex\",\r\n      width: 120,\r\n      render(dom, entity, index, action, schema) {\r\n        return (\r\n          <ColorPicker\r\n            value={colors[`${entity.id}`]}\r\n            showText\r\n            onChange={(value) => {\r\n              //dsRef.current[`${entity.id}`] = value.toHexString();\r\n              setColors((prev: any) => ({ ...prev, [`${entity.id}`]: value.toHexString() }));\r\n            }}\r\n            onChangeComplete={(value) => {\r\n              // dsRef.current[`${entity.id}`] = value.toHexString();\r\n              setColors((prev: any) => ({ ...prev, [`${entity.id}`]: value.toHexString() }));\r\n            }}\r\n            onOpenChange={(open) => {\r\n              if (!open) {\r\n                if (colors[`${entity.id}`] != entity.color_hex) {\r\n                  updateGpsDispatchStatus(entity.id, { color_hex: colors[`${entity.id}`] })\r\n                    .then((res) => {\r\n                      message.success(\"Saved successfully.\");\r\n                    })\r\n                    .catch(Util.error);\r\n                }\r\n              }\r\n            }}\r\n          />\r\n        );\r\n      },\r\n    },\r\n    {\r\n      title: \"ID\",\r\n      dataIndex: \"id\",\r\n      align: \"center\",\r\n      width: 80,\r\n    },\r\n    {\r\n      title: \"Option\",\r\n      dataIndex: \"option\",\r\n      valueType: \"option\",\r\n      render: (_, entity) => [\r\n        <a\r\n          key=\"config\"\r\n          onClick={() => {\r\n            handleUpdateModalVisible(true);\r\n            setCurrentRow({ ...entity, color_hex: colors[`${entity.id}`] });\r\n          }}\r\n        >\r\n          <EditOutlined />\r\n        </a>,\r\n      ],\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <PageContainer\r\n      extra={\r\n        <>\r\n          <Link to={\"/tenant/gps/job-board\"}>Back to Job Board</Link>\r\n        </>\r\n      }\r\n    >\r\n      <ProTable<API.GpsDispatchStatus, API.PageParams>\r\n        headerTitle={\"Job Status list\"}\r\n        actionRef={actionRef}\r\n        rowKey=\"id\"\r\n        revalidateOnFocus={false}\r\n        size=\"small\"\r\n        toolBarRender={() => [\r\n          <Button\r\n            type=\"primary\"\r\n            key=\"primary\"\r\n            onClick={() => {\r\n              handleModalVisible(true);\r\n            }}\r\n          >\r\n            <PlusOutlined /> New\r\n          </Button>,\r\n        ]}\r\n        request={async (params, sort, filter) => {\r\n          const res = await getGpsDispatchStatusListByPage(params, sort, filter);\r\n          /* dsRef.current = res.data\r\n            .filter((x) => x.id == 14)\r\n            .reduce((prev: any, x) => {\r\n              prev[`${x.id}`] = x.color_hex;\r\n              return prev;\r\n            }, {}); */\r\n          setColors(\r\n            res.data.reduce((prev: any, x) => {\r\n              prev[`${x.id}`] = x.color_hex;\r\n              return prev;\r\n            }, {}),\r\n          );\r\n          return res;\r\n        }}\r\n        onRequestError={Util.error}\r\n        search={false}\r\n        columns={columns}\r\n        rowSelection={{\r\n          onChange: (_, selectedRows) => {\r\n            setSelectedRows(selectedRows);\r\n          },\r\n        }}\r\n        columnEmptyText=\"\"\r\n        tableAlertRender={false}\r\n      />\r\n\r\n      {selectedRowsState?.length > 0 && (\r\n        <FooterToolbar\r\n          extra={\r\n            <div>\r\n              chosen{\" \"}\r\n              <a\r\n                style={{\r\n                  fontWeight: 600,\r\n                }}\r\n              >\r\n                {selectedRowsState.length}\r\n              </a>{\" \"}\r\n              status &nbsp;&nbsp;\r\n            </div>\r\n          }\r\n        >\r\n          <BatchDeleteAction\r\n            title=\"status\"\r\n            onConfirm={async () => {\r\n              if (!selectedRowsState) return true;\r\n\r\n              try {\r\n                await handleRemove(selectedRowsState);\r\n                setSelectedRows([]);\r\n                actionRef.current?.reloadAndRest?.();\r\n              } catch (error) {\r\n                Util.error(error);\r\n              }\r\n            }}\r\n          />\r\n        </FooterToolbar>\r\n      )}\r\n\r\n      <CreateForm\r\n        modalVisible={createModalVisible}\r\n        handleModalVisible={handleModalVisible}\r\n        onSubmit={async (value) => {\r\n          handleModalVisible(false);\r\n\r\n          if (actionRef.current) {\r\n            actionRef.current.reload();\r\n          }\r\n        }}\r\n      />\r\n\r\n      <UpdateForm\r\n        modalVisible={updateModalVisible}\r\n        handleModalVisible={handleUpdateModalVisible}\r\n        initialValues={currentRow || {}}\r\n        onSubmit={async (value) => {\r\n          setCurrentRow(undefined);\r\n\r\n          if (actionRef.current) {\r\n            actionRef.current.reload();\r\n          }\r\n        }}\r\n        onCancel={() => {\r\n          handleUpdateModalVisible(false);\r\n        }}\r\n      />\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default GpsDispatchStatusList;\r\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/tenant/gps/GpsDispatchStatusList/index.tsx"}, "25": {"path": "/tenant/dispatch", "name": "Dispatch", "parentId": "ant-design-pro-layout", "id": "25", "absPath": "/tenant/dispatch"}, "26": {"path": "/tenant/dispatch/list", "name": "Dispatch List", "file": "@/pages/tenant/dispatch/DispatchDashboard/index.tsx", "parentId": "25", "id": "26", "absPath": "/tenant/dispatch/list", "__content": "import React, { useState, useEffect, useCallback } from 'react';\r\nimport { PageContainer } from '@ant-design/pro-components';\r\n\r\nimport { Flex, Splitter, Typography, Card, theme, Spin, message as antdMessage } from 'antd';\r\nimport { DragDropContext, Droppable, DropResult, DraggableLocation } from \"react-beautiful-dnd\";\r\nimport reorder, { reorderQuoteMap, QuoteMap, groupDispatchByZip } from \"./reorder\";\r\nimport Column from \"./components/Column\";\r\nimport { searchDispatch, assignDispatch } from \"@/services/app/tenant/dispatch/dispatch\";\r\nimport { getTenantEmployees } from \"@/services/app/employee/employee\";\r\nimport { CalendarDayIcon, CalendarDayNumberIcon, CalendarWeekIcon, CalendarMonthIcon } from '@/components/ButlerIcon';\r\nimport { history, useModel } from '@umijs/max';\r\nimport dayjs from 'dayjs';\r\nimport timezone from 'dayjs/plugin/timezone';\r\nimport utc from 'dayjs/plugin/utc';\r\n\r\nconst Desc: React.FC<Readonly<{ text?: string | number }>> = (props) => (\r\n  <Flex justify=\"center\" align=\"center\" style={{ height: '100%' }}>    \r\n    <Typography.Title type=\"secondary\" level={5} style={{ whiteSpace: 'nowrap' }}>\r\n      Panel {props.text}\r\n    </Typography.Title>\r\n  </Flex>\r\n);\r\n\r\nconst DispatchDashboard: React.FC = () => {  \r\n  dayjs.extend(utc);\r\n  dayjs.extend(timezone);\r\n  const [messageApi, contextHolder] = antdMessage.useMessage();\r\n  const successMsg = (message?: string) => {\r\n    messageApi.open({\r\n      type: 'success',\r\n      content: message || '',\r\n    });\r\n  };\r\n\r\n  const errorMsg = (message?: string) => {\r\n    messageApi.open({\r\n      type: 'error',\r\n      content: message || '',\r\n    });\r\n  };\r\n\r\n  const warningMsg = (message?: string) => {\r\n    messageApi.open({\r\n      type: 'warning',\r\n      content: message || '',\r\n    });\r\n  };\r\n\r\n  const [officeDispatches, setOfficeDispatches] = useState<{[key: string]: API.Dispatch[]}>({});\r\n  const [officeColumns, setOfficeColumns] = useState<string[]>(['parked', 'unassigned']);  \r\n  const [fieldDispatches, setFieldDispatches] = useState<API.Dispatch[]>([]);  \r\n  const [employees, setEmployees] = useState<API.CurrentUser[]>([]);  \r\n  const { initialState } = useModel('@@initialState');\r\n  const { currentUser } = initialState || {};  \r\n  const { token } = theme.useToken();\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [success, setSuccess] = useState<string | null>(null);\r\n  const [message, setMessage] = useState<string | null>(null);\r\n  \r\n  const isCombineEnabled = false;\r\n  const useClone = false;\r\n  const withScrollableColumns = true;\r\n\r\n  const getInitialData = useCallback(async () => {\r\n    const employees = await getTenantEmployees({\r\n      with: \"userProfile,tenantRoles\"\r\n    });    \r\n    setEmployees(employees);\r\n\r\n\r\n    const unassigned_dispatches = await searchDispatch({\r\n      employeeId: \"unassigned\",\r\n      startDate: \"\",\r\n      endDate: \"\",\r\n      date: \"\",\r\n      jobStatus: \"250,260,20,140,150\",\r\n      appointmentStatus: \"\",\r\n      with: 'dispatchItems.symptoms,customer,customer.customerPhones,addresses,addresses.customerGeolocation',\r\n    });\r\n    \r\n    let officeDispatches = {\r\n      'unassigned': unassigned_dispatches.filter(x => !x.dispatch_user),\r\n      'parked': unassigned_dispatches.filter(x => x?.dispatch_status?.parking_status === 'parked'),      \r\n    };\r\n\r\n    const date = new Date();\r\n    const easternTime = dayjs(date).tz(\"America/New_York\").format(\"YYYY-MM-DD\");\r\n    const weekStart = dayjs(date).tz(\"America/New_York\").startOf('week').format(\"YYYY-MM-DD\");\r\n    const weekEnd = dayjs(date).tz(\"America/New_York\").endOf('week').format(\"YYYY-MM-DD\");\r\n    \r\n    const assigned_dispatches = await searchDispatch({\r\n      employeeId: 'all',\r\n      startDate: weekStart,\r\n      endDate: weekEnd,\r\n      date: \"\",\r\n      jobStatus: '20,140,150',\r\n      with: 'dispatchItems.symptoms,customer,customer.customerPhones,addresses,addresses.customerGeolocation'\r\n    })\r\n    \r\n    setOfficeDispatches(officeDispatches);    \r\n    setFieldDispatches(assigned_dispatches);\r\n  }, []);\r\n\r\n  const assignDispatchToEmployee = useCallback(async (data: API.DispatchAssignPayload) => {    \r\n    const assign_result = await assignDispatch(data);\r\n    return assign_result;\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    getInitialData();\r\n  }, [getInitialData]);\r\n  \r\n  const onDragEnd = (result: DropResult) => {\r\n    console.log(\"[onDragEnd]\", result);\r\n\r\n    // Dropped nowhere\r\n    if (!result.destination) {\r\n      return;\r\n    }\r\n\r\n    const source: DraggableLocation = result.source;\r\n    const destination: DraggableLocation = result.destination;\r\n\r\n    // Did not move anywhere - can bail early\r\n    if (\r\n      source.droppableId === destination.droppableId &&\r\n      source.index === destination.index\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    // Reordering column\r\n    if (result.type === \"OFFICE_COLUMN\") {\r\n      if (source.droppableId === destination.droppableId && \r\n        source.index !== destination.index &&\r\n        source.droppableId === 'office-columns' &&\r\n        destination.droppableId === 'office-columns'\r\n      ) {\r\n        const reordered = reorder(officeColumns, source.index, destination.index);\r\n        setOfficeColumns(reordered);\r\n      }\r\n      return;\r\n    }\r\n\r\n    if (result.type === \"FIELD_COLUMN\") {\r\n      if (source.droppableId === destination.droppableId && \r\n        source.index !== destination.index &&\r\n        source.droppableId === 'field-columns' &&\r\n        destination.droppableId === 'field-columns'\r\n      ) {\r\n        const reordered = reorder(employees, source.index, destination.index);\r\n        setEmployees(reordered);\r\n      }\r\n      return;\r\n    }\r\n\r\n    if (result.type === \"Dispatch\") {\r\n      const checkZone = source.droppableId.indexOf('zone_');\r\n      const checkEmployee = destination.droppableId.indexOf('employee_');\r\n      \r\n      if (checkZone == 0 && checkEmployee == 0) {\r\n        const employeeId = destination.droppableId.split('_')[1];\r\n        const app_date = destination.droppableId.split('_')[2];\r\n        const dispatch_id = Number(result.draggableId.split('_')[1]);\r\n        const assignDispatchData: API.DispatchAssignPayload = {\r\n          job_id: dispatch_id,\r\n          assigned_techs: employeeId,\r\n          appointment_set: app_date,\r\n          index: destination.index\r\n        }\r\n        \r\n        setMessage(\"Assigning job to employee...\");        \r\n        setIsLoading(true);\r\n        const assign_result = assignDispatchToEmployee(assignDispatchData);\r\n        assign_result.then(res => {\r\n          if (res?.success) {\r\n            getInitialData();\r\n            setMessage(\"\");        \r\n            setIsLoading(false);     \r\n            successMsg('Job was assigned successfully.');\r\n          } else {\r\n            setMessage('');        \r\n            setIsLoading(false);\r\n            errorMsg(res?.message);\r\n          }\r\n        }).catch(err => {          \r\n          setMessage('');        \r\n          setIsLoading(false);\r\n          errorMsg(err?.errorMessage);\r\n        });        \r\n        \r\n      } else if ( source.droppableId.indexOf('employee_') === destination.droppableId.indexOf('employee_') && \r\n        checkEmployee == 0\r\n      ) {\r\n        const employeeId = destination.droppableId.split('_')[1];\r\n        const app_date = destination.droppableId.split('_')[2];\r\n        const dispatch_id = Number(result.draggableId.split('_')[1]);\r\n        const assignDispatchData: API.DispatchAssignPayload = {\r\n          job_id: dispatch_id,\r\n          assigned_techs: employeeId,\r\n          appointment_set: app_date,\r\n          index: destination.index\r\n        }\r\n        \r\n        setMessage(\"Assigning job to employee...\");        \r\n        setIsLoading(true);\r\n        const assign_result = assignDispatchToEmployee(assignDispatchData);\r\n        assign_result.then(res => {\r\n          if (res?.success) {\r\n            getInitialData();\r\n            setMessage(\"\");        \r\n            setIsLoading(false);       \r\n            successMsg('Job was assigned successfully.');            \r\n          } else {\r\n            setMessage('');        \r\n            setIsLoading(false);\r\n            errorMsg(res?.errorMessage);\r\n          }\r\n        }).catch(err => {\r\n          setMessage('');        \r\n          setIsLoading(false);\r\n          errorMsg(err?.message);\r\n        });        \r\n      }\r\n\r\n    }\r\n\r\n    // // Reordering items within or between columns\r\n    // const data = reorderQuoteMap({\r\n    //   quoteMap: columns,\r\n    //   source,\r\n    //   destination\r\n    // });\r\n\r\n    // setColumns(data.quoteMap);\r\n  };\r\n  \r\n  return (\r\n    <PageContainer>\r\n      {contextHolder}\r\n      <Spin \r\n        tip={message} \r\n        fullscreen={isLoading}\r\n        style={{ \r\n          display: isLoading ? 'block' : 'none'  \r\n        }}\r\n      >        \r\n      </Spin>\r\n      {/* <Card>\r\n          <CalendarDayNumberIcon style={{ fontSize: '32px' }} />\r\n          <CalendarWeekIcon style={{ fontSize: '32px' }} />\r\n          <CalendarMonthIcon style={{ fontSize: '32px' }} />          \r\n      </Card> */}\r\n      <DragDropContext onDragEnd={onDragEnd}>\r\n      <Splitter style={{ height: 'calc(100vh - 200px)', overflow: 'hidden', marginTop: 16, boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)' }}>\r\n        <Splitter.Panel collapsible style={{ overflow: 'hidden' }}>\r\n          <Droppable\r\n            droppableId=\"office-columns\"\r\n            type=\"OFFICE_COLUMN\"\r\n            direction=\"horizontal\"\r\n            ignoreContainerClipping={true}\r\n            isCombineEnabled={false}\r\n            isDropDisabled={false}\r\n          >\r\n            {(provided) => (\r\n              <div \r\n                ref={provided.innerRef} \r\n                {...provided.droppableProps}\r\n                style={{\r\n                  backgroundColor: token.colorBgLayout,\r\n                  minHeight: '300px',\r\n                  minWidth: '100%',\r\n                  display: 'inline-flex'\r\n                }}\r\n              >\r\n                \r\n                {officeColumns.map((key, index) => (                  \r\n                  <Column\r\n                    key={key}\r\n                    index={index}\r\n                    title={key}\r\n                    dispatches={officeDispatches[key] || []}\r\n                    isScrollable={withScrollableColumns}\r\n                    isCombineEnabled={isCombineEnabled}\r\n                    useClone={useClone}\r\n                    listType={key === 'unassigned'? 'Zone': 'Dispatch'}\r\n                    listId={`office_list_${key}`}\r\n                  />                  \r\n                ))}\r\n                {provided.placeholder}\r\n              </div>\r\n            )}\r\n          </Droppable>          \r\n        </Splitter.Panel>\r\n        <Splitter.Panel style={{ overflow: 'hidden' }}>\r\n          <Droppable\r\n            droppableId=\"field-columns\"\r\n            type=\"FIELD_COLUMN\"\r\n            direction=\"horizontal\"\r\n            ignoreContainerClipping={true}\r\n            isCombineEnabled={false}\r\n            isDropDisabled={false}\r\n          >\r\n            {(provided) => (\r\n              <div \r\n                ref={provided.innerRef} \r\n                {...provided.droppableProps}\r\n                style={{\r\n                  backgroundColor: token.colorBgLayout,\r\n                  minHeight: '300px',\r\n                  minWidth: '100%',\r\n                  display: 'inline-flex'\r\n                }}\r\n              >\r\n                \r\n                {employees.map((key, index) => (                  \r\n                  <Column\r\n                    key={key.id}\r\n                    index={index}\r\n                    title={key.user_name || \"Unknown\"}\r\n                    dispatches={fieldDispatches}\r\n                    isScrollable={withScrollableColumns}\r\n                    isCombineEnabled={isCombineEnabled}\r\n                    useClone={useClone}\r\n                    listType='Date'\r\n                    listId={`field_list_${key.id}`}\r\n                  />                  \r\n                ))}\r\n                {provided.placeholder}\r\n              </div>\r\n            )}\r\n          </Droppable>   \r\n        </Splitter.Panel>\r\n        <Splitter.Panel collapsible={{ start: true }}>\r\n          <Desc text=\"History\" />\r\n        </Splitter.Panel>\r\n      </Splitter>\r\n      </DragDropContext>\r\n    </PageContainer>\r\n  );\r\n}\r\n\r\nexport default DispatchDashboard;\r\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/tenant/dispatch/DispatchDashboard/index.tsx"}, "27": {"path": "/tenant/dispatch/dashboard", "name": "Dispatch Dashboard", "file": "@/pages/tenant/dispatch/DispatchDashboard/index.tsx", "parentId": "25", "id": "27", "absPath": "/tenant/dispatch/dashboard", "__content": "import React, { useState, useEffect, useCallback } from 'react';\r\nimport { PageContainer } from '@ant-design/pro-components';\r\n\r\nimport { Flex, Splitter, Typography, Card, theme, Spin, message as antdMessage } from 'antd';\r\nimport { DragDropContext, Droppable, DropResult, DraggableLocation } from \"react-beautiful-dnd\";\r\nimport reorder, { reorderQuoteMap, QuoteMap, groupDispatchByZip } from \"./reorder\";\r\nimport Column from \"./components/Column\";\r\nimport { searchDispatch, assignDispatch } from \"@/services/app/tenant/dispatch/dispatch\";\r\nimport { getTenantEmployees } from \"@/services/app/employee/employee\";\r\nimport { CalendarDayIcon, CalendarDayNumberIcon, CalendarWeekIcon, CalendarMonthIcon } from '@/components/ButlerIcon';\r\nimport { history, useModel } from '@umijs/max';\r\nimport dayjs from 'dayjs';\r\nimport timezone from 'dayjs/plugin/timezone';\r\nimport utc from 'dayjs/plugin/utc';\r\n\r\nconst Desc: React.FC<Readonly<{ text?: string | number }>> = (props) => (\r\n  <Flex justify=\"center\" align=\"center\" style={{ height: '100%' }}>    \r\n    <Typography.Title type=\"secondary\" level={5} style={{ whiteSpace: 'nowrap' }}>\r\n      Panel {props.text}\r\n    </Typography.Title>\r\n  </Flex>\r\n);\r\n\r\nconst DispatchDashboard: React.FC = () => {  \r\n  dayjs.extend(utc);\r\n  dayjs.extend(timezone);\r\n  const [messageApi, contextHolder] = antdMessage.useMessage();\r\n  const successMsg = (message?: string) => {\r\n    messageApi.open({\r\n      type: 'success',\r\n      content: message || '',\r\n    });\r\n  };\r\n\r\n  const errorMsg = (message?: string) => {\r\n    messageApi.open({\r\n      type: 'error',\r\n      content: message || '',\r\n    });\r\n  };\r\n\r\n  const warningMsg = (message?: string) => {\r\n    messageApi.open({\r\n      type: 'warning',\r\n      content: message || '',\r\n    });\r\n  };\r\n\r\n  const [officeDispatches, setOfficeDispatches] = useState<{[key: string]: API.Dispatch[]}>({});\r\n  const [officeColumns, setOfficeColumns] = useState<string[]>(['parked', 'unassigned']);  \r\n  const [fieldDispatches, setFieldDispatches] = useState<API.Dispatch[]>([]);  \r\n  const [employees, setEmployees] = useState<API.CurrentUser[]>([]);  \r\n  const { initialState } = useModel('@@initialState');\r\n  const { currentUser } = initialState || {};  \r\n  const { token } = theme.useToken();\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [success, setSuccess] = useState<string | null>(null);\r\n  const [message, setMessage] = useState<string | null>(null);\r\n  \r\n  const isCombineEnabled = false;\r\n  const useClone = false;\r\n  const withScrollableColumns = true;\r\n\r\n  const getInitialData = useCallback(async () => {\r\n    const employees = await getTenantEmployees({\r\n      with: \"userProfile,tenantRoles\"\r\n    });    \r\n    setEmployees(employees);\r\n\r\n\r\n    const unassigned_dispatches = await searchDispatch({\r\n      employeeId: \"unassigned\",\r\n      startDate: \"\",\r\n      endDate: \"\",\r\n      date: \"\",\r\n      jobStatus: \"250,260,20,140,150\",\r\n      appointmentStatus: \"\",\r\n      with: 'dispatchItems.symptoms,customer,customer.customerPhones,addresses,addresses.customerGeolocation',\r\n    });\r\n    \r\n    let officeDispatches = {\r\n      'unassigned': unassigned_dispatches.filter(x => !x.dispatch_user),\r\n      'parked': unassigned_dispatches.filter(x => x?.dispatch_status?.parking_status === 'parked'),      \r\n    };\r\n\r\n    const date = new Date();\r\n    const easternTime = dayjs(date).tz(\"America/New_York\").format(\"YYYY-MM-DD\");\r\n    const weekStart = dayjs(date).tz(\"America/New_York\").startOf('week').format(\"YYYY-MM-DD\");\r\n    const weekEnd = dayjs(date).tz(\"America/New_York\").endOf('week').format(\"YYYY-MM-DD\");\r\n    \r\n    const assigned_dispatches = await searchDispatch({\r\n      employeeId: 'all',\r\n      startDate: weekStart,\r\n      endDate: weekEnd,\r\n      date: \"\",\r\n      jobStatus: '20,140,150',\r\n      with: 'dispatchItems.symptoms,customer,customer.customerPhones,addresses,addresses.customerGeolocation'\r\n    })\r\n    \r\n    setOfficeDispatches(officeDispatches);    \r\n    setFieldDispatches(assigned_dispatches);\r\n  }, []);\r\n\r\n  const assignDispatchToEmployee = useCallback(async (data: API.DispatchAssignPayload) => {    \r\n    const assign_result = await assignDispatch(data);\r\n    return assign_result;\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    getInitialData();\r\n  }, [getInitialData]);\r\n  \r\n  const onDragEnd = (result: DropResult) => {\r\n    console.log(\"[onDragEnd]\", result);\r\n\r\n    // Dropped nowhere\r\n    if (!result.destination) {\r\n      return;\r\n    }\r\n\r\n    const source: DraggableLocation = result.source;\r\n    const destination: DraggableLocation = result.destination;\r\n\r\n    // Did not move anywhere - can bail early\r\n    if (\r\n      source.droppableId === destination.droppableId &&\r\n      source.index === destination.index\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    // Reordering column\r\n    if (result.type === \"OFFICE_COLUMN\") {\r\n      if (source.droppableId === destination.droppableId && \r\n        source.index !== destination.index &&\r\n        source.droppableId === 'office-columns' &&\r\n        destination.droppableId === 'office-columns'\r\n      ) {\r\n        const reordered = reorder(officeColumns, source.index, destination.index);\r\n        setOfficeColumns(reordered);\r\n      }\r\n      return;\r\n    }\r\n\r\n    if (result.type === \"FIELD_COLUMN\") {\r\n      if (source.droppableId === destination.droppableId && \r\n        source.index !== destination.index &&\r\n        source.droppableId === 'field-columns' &&\r\n        destination.droppableId === 'field-columns'\r\n      ) {\r\n        const reordered = reorder(employees, source.index, destination.index);\r\n        setEmployees(reordered);\r\n      }\r\n      return;\r\n    }\r\n\r\n    if (result.type === \"Dispatch\") {\r\n      const checkZone = source.droppableId.indexOf('zone_');\r\n      const checkEmployee = destination.droppableId.indexOf('employee_');\r\n      \r\n      if (checkZone == 0 && checkEmployee == 0) {\r\n        const employeeId = destination.droppableId.split('_')[1];\r\n        const app_date = destination.droppableId.split('_')[2];\r\n        const dispatch_id = Number(result.draggableId.split('_')[1]);\r\n        const assignDispatchData: API.DispatchAssignPayload = {\r\n          job_id: dispatch_id,\r\n          assigned_techs: employeeId,\r\n          appointment_set: app_date,\r\n          index: destination.index\r\n        }\r\n        \r\n        setMessage(\"Assigning job to employee...\");        \r\n        setIsLoading(true);\r\n        const assign_result = assignDispatchToEmployee(assignDispatchData);\r\n        assign_result.then(res => {\r\n          if (res?.success) {\r\n            getInitialData();\r\n            setMessage(\"\");        \r\n            setIsLoading(false);     \r\n            successMsg('Job was assigned successfully.');\r\n          } else {\r\n            setMessage('');        \r\n            setIsLoading(false);\r\n            errorMsg(res?.message);\r\n          }\r\n        }).catch(err => {          \r\n          setMessage('');        \r\n          setIsLoading(false);\r\n          errorMsg(err?.errorMessage);\r\n        });        \r\n        \r\n      } else if ( source.droppableId.indexOf('employee_') === destination.droppableId.indexOf('employee_') && \r\n        checkEmployee == 0\r\n      ) {\r\n        const employeeId = destination.droppableId.split('_')[1];\r\n        const app_date = destination.droppableId.split('_')[2];\r\n        const dispatch_id = Number(result.draggableId.split('_')[1]);\r\n        const assignDispatchData: API.DispatchAssignPayload = {\r\n          job_id: dispatch_id,\r\n          assigned_techs: employeeId,\r\n          appointment_set: app_date,\r\n          index: destination.index\r\n        }\r\n        \r\n        setMessage(\"Assigning job to employee...\");        \r\n        setIsLoading(true);\r\n        const assign_result = assignDispatchToEmployee(assignDispatchData);\r\n        assign_result.then(res => {\r\n          if (res?.success) {\r\n            getInitialData();\r\n            setMessage(\"\");        \r\n            setIsLoading(false);       \r\n            successMsg('Job was assigned successfully.');            \r\n          } else {\r\n            setMessage('');        \r\n            setIsLoading(false);\r\n            errorMsg(res?.errorMessage);\r\n          }\r\n        }).catch(err => {\r\n          setMessage('');        \r\n          setIsLoading(false);\r\n          errorMsg(err?.message);\r\n        });        \r\n      }\r\n\r\n    }\r\n\r\n    // // Reordering items within or between columns\r\n    // const data = reorderQuoteMap({\r\n    //   quoteMap: columns,\r\n    //   source,\r\n    //   destination\r\n    // });\r\n\r\n    // setColumns(data.quoteMap);\r\n  };\r\n  \r\n  return (\r\n    <PageContainer>\r\n      {contextHolder}\r\n      <Spin \r\n        tip={message} \r\n        fullscreen={isLoading}\r\n        style={{ \r\n          display: isLoading ? 'block' : 'none'  \r\n        }}\r\n      >        \r\n      </Spin>\r\n      {/* <Card>\r\n          <CalendarDayNumberIcon style={{ fontSize: '32px' }} />\r\n          <CalendarWeekIcon style={{ fontSize: '32px' }} />\r\n          <CalendarMonthIcon style={{ fontSize: '32px' }} />          \r\n      </Card> */}\r\n      <DragDropContext onDragEnd={onDragEnd}>\r\n      <Splitter style={{ height: 'calc(100vh - 200px)', overflow: 'hidden', marginTop: 16, boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)' }}>\r\n        <Splitter.Panel collapsible style={{ overflow: 'hidden' }}>\r\n          <Droppable\r\n            droppableId=\"office-columns\"\r\n            type=\"OFFICE_COLUMN\"\r\n            direction=\"horizontal\"\r\n            ignoreContainerClipping={true}\r\n            isCombineEnabled={false}\r\n            isDropDisabled={false}\r\n          >\r\n            {(provided) => (\r\n              <div \r\n                ref={provided.innerRef} \r\n                {...provided.droppableProps}\r\n                style={{\r\n                  backgroundColor: token.colorBgLayout,\r\n                  minHeight: '300px',\r\n                  minWidth: '100%',\r\n                  display: 'inline-flex'\r\n                }}\r\n              >\r\n                \r\n                {officeColumns.map((key, index) => (                  \r\n                  <Column\r\n                    key={key}\r\n                    index={index}\r\n                    title={key}\r\n                    dispatches={officeDispatches[key] || []}\r\n                    isScrollable={withScrollableColumns}\r\n                    isCombineEnabled={isCombineEnabled}\r\n                    useClone={useClone}\r\n                    listType={key === 'unassigned'? 'Zone': 'Dispatch'}\r\n                    listId={`office_list_${key}`}\r\n                  />                  \r\n                ))}\r\n                {provided.placeholder}\r\n              </div>\r\n            )}\r\n          </Droppable>          \r\n        </Splitter.Panel>\r\n        <Splitter.Panel style={{ overflow: 'hidden' }}>\r\n          <Droppable\r\n            droppableId=\"field-columns\"\r\n            type=\"FIELD_COLUMN\"\r\n            direction=\"horizontal\"\r\n            ignoreContainerClipping={true}\r\n            isCombineEnabled={false}\r\n            isDropDisabled={false}\r\n          >\r\n            {(provided) => (\r\n              <div \r\n                ref={provided.innerRef} \r\n                {...provided.droppableProps}\r\n                style={{\r\n                  backgroundColor: token.colorBgLayout,\r\n                  minHeight: '300px',\r\n                  minWidth: '100%',\r\n                  display: 'inline-flex'\r\n                }}\r\n              >\r\n                \r\n                {employees.map((key, index) => (                  \r\n                  <Column\r\n                    key={key.id}\r\n                    index={index}\r\n                    title={key.user_name || \"Unknown\"}\r\n                    dispatches={fieldDispatches}\r\n                    isScrollable={withScrollableColumns}\r\n                    isCombineEnabled={isCombineEnabled}\r\n                    useClone={useClone}\r\n                    listType='Date'\r\n                    listId={`field_list_${key.id}`}\r\n                  />                  \r\n                ))}\r\n                {provided.placeholder}\r\n              </div>\r\n            )}\r\n          </Droppable>   \r\n        </Splitter.Panel>\r\n        <Splitter.Panel collapsible={{ start: true }}>\r\n          <Desc text=\"History\" />\r\n        </Splitter.Panel>\r\n      </Splitter>\r\n      </DragDropContext>\r\n    </PageContainer>\r\n  );\r\n}\r\n\r\nexport default DispatchDashboard;\r\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/tenant/dispatch/DispatchDashboard/index.tsx"}, "28": {"path": "/tenant/settings", "name": "Settings", "parentId": "ant-design-pro-layout", "id": "28", "absPath": "/tenant/settings"}, "29": {"path": "/tenant/settings/system-message-template/edit", "name": "Automatic SMS Template", "external": true, "parentId": "28", "id": "29", "absPath": "/tenant/settings/system-message-template/edit"}, "30": {"path": "/tenant/settings/dispatch/greetings", "name": "Automatic SMS Logs", "external": true, "parentId": "28", "id": "30", "absPath": "/tenant/settings/dispatch/greetings"}, "31": {"path": "/tenant/settings/address", "name": "Tenant Addresses", "external": true, "parentId": "28", "id": "31", "absPath": "/tenant/settings/address"}, "32": {"path": "/tenant/call_board", "name": "Call Board", "external": true, "parentId": "ant-design-pro-layout", "id": "32", "absPath": "/tenant/call_board"}, "33": {"path": "/tenant/tenant_profile", "name": "My Profile", "external": true, "parentId": "ant-design-pro-layout", "id": "33", "absPath": "/tenant/tenant_profile"}, "34": {"path": "/admin", "name": "Admin", "icon": "crown", "access": "canAdmin", "parentId": "ant-design-pro-layout", "id": "34", "absPath": "/admin"}, "35": {"path": "/admin", "redirect": "/admin/sub-page", "parentId": "34", "id": "35", "absPath": "/admin"}, "36": {"path": "/admin/sub-page", "name": "Sub page", "file": "@/pages/Admin.tsx", "parentId": "34", "id": "36", "absPath": "/admin/sub-page", "__content": "import { PageContainer } from '@ant-design/pro-components';\nimport '@umijs/max';\nimport { <PERSON><PERSON>, <PERSON><PERSON>, Card, Space, Typography } from 'antd';\nimport React from 'react';\nconst Admin: React.FC = () => {\n  return (\n    <PageContainer content={' Admin page'}>\n      <Card>\n\n        <Alert\n          message={'Test page'}\n          type=\"success\"\n          showIcon\n          banner\n          style={{\n            marginBottom: 48,\n          }}\n        />\n\n        <Space size={32}>\n          <Button type='primary'>Primary</Button>\n          <Button type='primary' ghost>Primary (Ghost)</Button>\n        </Space>\n      </Card>\n      \n    </PageContainer>\n  );\n};\nexport default Admin;\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/Admin.tsx"}, "37": {"path": "/", "redirect": "/tenant/dispatch/list", "parentId": "ant-design-pro-layout", "id": "37", "absPath": "/"}, "38": {"path": "*", "layout": false, "file": "@/pages/404.tsx", "id": "38", "absPath": "/*", "__content": "import { getIntl, history, useIntl } from '@umijs/max';\nimport { Button, Result } from 'antd';\nimport React from 'react';\n\nconst NoFoundPage: React.FC = () => {\n  const intl = getIntl();\n  console.log('intl', intl);\n  \n  return (\n  <Result\n    status=\"404\"\n    title=\"404\"\n    subTitle={useIntl().formatMessage({ id: 'pages.404.subTitle' })}\n    extra={\n      <Button type=\"primary\" onClick={() => history.push('/')}>\n        {useIntl().formatMessage({ id: 'pages.404.buttonText' })}\n      </Button>\n    }\n  />\n);}\n\nexport default NoFoundPage;\n", "__isJSFile": true, "__absFile": "F:/htdocs/development_team/frontend/src/pages/404.tsx"}, "ant-design-pro-layout": {"id": "ant-design-pro-layout", "path": "/", "file": "F:/htdocs/development_team/frontend/src/.umi/plugin-layout/Layout.tsx", "absPath": "/", "isLayout": true, "__absFile": "F:/htdocs/development_team/frontend/src/.umi/plugin-layout/Layout.tsx"}}, "apiRoutes": {}, "hasSrcDir": true, "npmClient": "yarn", "umi": {"version": "4.4.11", "name": "<PERSON><PERSON>", "importSource": "@umijs/max", "cliName": "max"}, "bundleStatus": {"done": false}, "react": {"version": "19.1.0", "path": "F:\\htdocs\\development_team\\frontend\\node_modules\\react"}, "react-dom": {"version": "19.1.0", "path": "F:\\htdocs\\development_team\\frontend\\node_modules\\react-dom"}, "appJS": {"path": "F:\\htdocs\\development_team\\frontend\\src\\app.tsx", "exports": ["getInitialState", "layout", "request"]}, "locale": "en-US", "globalCSS": ["F:\\htdocs\\development_team\\frontend\\src\\global.less"], "globalJS": ["F:\\htdocs\\development_team\\frontend\\src\\global.tsx"], "overridesCSS": [], "bundler": "mako", "git": {"originUrl": "**************-pa:developmentbutler/development_team.git"}, "framework": "react", "typescript": {"tsVersion": "5.8.3", "tslibVersion": "2.8.1"}, "faviconFiles": [], "port": 3000, "host": "0.0.0.0", "ip": "************", "antd": {"pkgPath": "F:\\htdocs\\development_team\\frontend\\node_modules\\antd", "version": "5.26.3"}, "pluginLayout": {"pkgPath": "F:/htdocs/development_team/frontend/node_modules/@ant-design/pro-components", "version": "2.8.9"}}