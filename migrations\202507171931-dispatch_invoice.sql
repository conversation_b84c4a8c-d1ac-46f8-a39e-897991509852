CREATE TABLE `dispatch_invoice`
(
    `id`                        int(10) unsigned NOT NULL AUTO_INCREMENT,
    `dispatch_id`               int(10) unsigned NULL,
    `estimate_id`               int(10) unsigned NULL comment 'FK: Estimate No',
    `customer_id`               int(10) unsigned NULL comment 'FK: Customer ID',
    `invoice_no`                int(10) unsigned NULL comment 'UQ: invoice no to be generated by sequence',
    `status`                    varchar(31)      NOT NULL DEFAULT 'draft' COMMENT '''draft'',''sent'',''pending'',''overdue'',''paid'',''refunded'',''canceled'',''closed'',''approved''',
    `issue_date`                DATE                      DEFAULT NULL COMMENT 'Issue Date',
    `service_date`              DATE             NULL COMMENT 'Service Date',
    `grand_total`               decimal(20, 4)   NOT NULL DEFAULT 0.0000,
    `subtotal`                  decimal(20, 4)   NOT NULL DEFAULT 0.0000,
    `tax_percent`               decimal(6, 2)    NOT NULL DEFAULT 0.00 COMMENT 'Tax percentage',
    `tax_amount`                decimal(20, 4)   NOT NULL DEFAULT 0.0000 COMMENT 'Total Tax amount',
    `discount_amount`           decimal(20, 4)   NOT NULL DEFAULT 0.0000 COMMENT 'Discount amount',
    `total_item_count`          int(10) unsigned NOT NULL DEFAULT 0 COMMENT 'Total count of items',
    `notes`                     text                      DEFAULT NULL,
    `payment_terms`             varchar(255)              DEFAULT NULL COMMENT 'Payment terms',
    `valid_until`               date                      DEFAULT NULL,
    `billing_company`           varchar(255)              DEFAULT NULL,
    `billing_name`              varchar(255)              DEFAULT NULL,
    `billing_email`             varchar(255)              DEFAULT NULL,
    `billing_phone`             varchar(255)              DEFAULT NULL,
    `billing_city`              varchar(100)              DEFAULT NULL,
    `billing_state`             varchar(100)              DEFAULT NULL,
    `billing_street_direction`  varchar(100)              DEFAULT NULL,
    `billing_street_name`       varchar(100)              DEFAULT NULL,
    `billing_street_number`     varchar(100)              DEFAULT NULL,
    `billing_unit_number`       varchar(100)              DEFAULT NULL,
    `billing_unit_type`         varchar(100)              DEFAULT NULL,
    `billing_zip`               varchar(100)              DEFAULT NULL,
    `billing_zip_four`          varchar(100)              DEFAULT NULL,
    `customer_company`          varchar(255)              DEFAULT NULL,
    `customer_name`             varchar(255)              DEFAULT NULL,
    `customer_email`            varchar(255)              DEFAULT NULL,
    `customer_phone`            varchar(255)              DEFAULT NULL,
    `customer_city`             varchar(100)              DEFAULT NULL,
    `customer_state`            varchar(100)              DEFAULT NULL,
    `customer_street_direction` varchar(100)              DEFAULT NULL,
    `customer_street_name`      varchar(100)              DEFAULT NULL,
    `customer_street_number`    varchar(100)              DEFAULT NULL,
    `customer_unit_number`      varchar(100)              DEFAULT NULL,
    `customer_unit_type`        varchar(100)              DEFAULT NULL,
    `customer_zip`              varchar(100)              DEFAULT NULL,
    `customer_zip_four`         varchar(100)              DEFAULT NULL,
    `file_id`                   int(10) unsigned          DEFAULT NULL COMMENT 'Invoice document. e.g. PDF',
    `created_by`                int(10) unsigned NOT NULL,
    `created_at`                datetime         NOT NULL DEFAULT current_timestamp(),
    `updated_by`                int(10) unsigned NOT NULL,
    `updated_at`                timestamp        NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`),
    KEY `FK_dispatch_invoice_dispatch_id` (`dispatch_id`),
    KEY `FK_dispatch_invoice_estimate_id` (`estimate_id`),
    KEY `FK_dispatch_invoice_customer_id` (`customer_id`),
    KEY `IDX_dispatch_invoice_status` (`status`),
    KEY `IDX_dispatch_invoice_created_by` (`created_by`),
    KEY `FK_dispatch_invoice_file_id` (`file_id`),
    unique KEY `UQ_dispatch_invoice_invoice_no` (`invoice_no`),
    CONSTRAINT `FK_dispatch_invoice_customer_id` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_dispatch_invoice_dispatch_id` FOREIGN KEY (`dispatch_id`) REFERENCES `dispatches` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `FK_dispatch_invoice_estimate_id` FOREIGN KEY (`estimate_id`) REFERENCES `dispatch_estimates` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `FK_dispatch_invoice_file_id` FOREIGN KEY (`file_id`) REFERENCES `file` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;


CREATE TABLE `dispatch_invoice_item`
(
    `id`                  int(10) unsigned NOT NULL AUTO_INCREMENT,
    `dispatch_invoice_id` int(10) unsigned NOT NULL,
    `service_id`          int(10) unsigned          DEFAULT NULL,
    `name`                varchar(255)     NOT NULL,
    `description`         text                      DEFAULT NULL,
    `qty`                 decimal(10, 2)   NOT NULL DEFAULT 0.00,
    `cost`                decimal(20, 4)   NOT NULL DEFAULT 0.0000,
    `price`               decimal(20, 4)   NOT NULL DEFAULT 0.0000,
    `price_total`         decimal(20, 4)            DEFAULT 0.0000,
    `taxable`             tinyint(1)       NOT NULL DEFAULT 0,
    `tax_percent`         decimal(20, 2)   NOT NULL DEFAULT 0.00,
    `tax_amount`          decimal(20, 4)   NOT NULL DEFAULT 0.0000,
    `total`               decimal(20, 4)   NOT NULL DEFAULT 0.0000,
    `duration`            decimal(20, 4)   NOT NULL DEFAULT 0.0000,
    `sort_order`          int(11)          NOT NULL DEFAULT 0,
    `created_at`          datetime         NOT NULL DEFAULT current_timestamp(),
    `updated_at`          timestamp        NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`),
    KEY `FK_dispatch_invoice_item_dispatch_invoice_id` (`dispatch_invoice_id`),
    KEY `FK_dispatch_invoice_item_service_id` (`service_id`),
    CONSTRAINT `FK_dispatch_invoice_item_dispatch_invoice_id` FOREIGN KEY (`dispatch_invoice_id`) REFERENCES `dispatch_invoice` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_dispatch_invoice_item_service_id` FOREIGN KEY (`service_id`) REFERENCES `pb_service` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

CREATE TABLE `dispatch_invoice_log`
(
    `id`                  int(10) unsigned NOT NULL AUTO_INCREMENT,
    `dispatch_invoice_id` int(10) unsigned NOT NULL,
    `title`               varchar(255)     NOT NULL,
    `notes`               varchar(255)              DEFAULT NULL,
    `created_by`          int(10) unsigned          DEFAULT NULL,
    `created_at`          datetime         NOT NULL DEFAULT current_timestamp(),
    PRIMARY KEY (`id`),
    KEY `FK_dispatch_invoice_logs_dispatch_invoice_id` (`dispatch_invoice_id`),
    KEY `IDX_dispatch_invoice_logs_title` (`title`),
    KEY `FK_dispatch_invoice_logs_created_by` (`created_by`),
    CONSTRAINT `FK_dispatch_invoice_logs_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `FK_dispatch_invoice_logs_dispatch_invoice_id` FOREIGN KEY (`dispatch_invoice_id`) REFERENCES `dispatch_invoice` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;


CREATE TABLE `dispatch_invoice_payment`
(
    `id`                  int(10) unsigned NOT NULL AUTO_INCREMENT,
    `dispatch_invoice_id` int(10) unsigned NOT NULL comment 'FK: invoice ID',
    `amount_ordered`      decimal(20, 4) DEFAULT NULL COMMENT 'Amount Ordered',
    `amount_paid`         decimal(20, 4) DEFAULT NULL COMMENT 'Amount Paid',
    `amount_refunded`     decimal(20, 4) DEFAULT NULL COMMENT 'Amount Refunded',
    `amount_canceled`     decimal(20, 4) DEFAULT NULL COMMENT 'Amount Canceled',
    `method`              varchar(128)   DEFAULT NULL COMMENT 'Method',
    `details`             LONGTEXT       DEFAULT NULL COMMENT 'Details in JSON',
    `created_by`          int(10) unsigned NOT NULL,
    `created_at`          datetime         NOT NULL,
    `updated_by`          int(10) unsigned NOT NULL,
    `updated_at`          timestamp        NOT NULL,
    PRIMARY KEY (`id`),
    KEY `FK_dispatch_invoice_payment_dispatch_invoice_id` (`dispatch_invoice_id`),
    CONSTRAINT `FK_dispatch_invoice_payment_dispatch_invoice_id` FOREIGN KEY (`dispatch_invoice_id`) REFERENCES `dispatch_invoice` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

ALTER TABLE `dispatch_invoice`
    ADD COLUMN `tenant_id` INT UNSIGNED NULL AFTER `id`,
    ADD CONSTRAINT `FK_dispatch_invoice_tenant_id` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON UPDATE CASCADE ON DELETE SET NULL;


ALTER TABLE `dispatch_invoice` CHANGE `invoice_no` `invoice_no` VARCHAR(31) NOT NULL COMMENT 'UQ: invoice no to be generated by sequence';


