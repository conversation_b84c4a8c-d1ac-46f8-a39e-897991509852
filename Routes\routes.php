<?php

use Butler\Controllers\admins\Setting\SystemApiKeyController;
use Butler\Controllers\api\DownloadFileApi;
use Butler\Controllers\api\DriverPositionsApi;
use Butler\Controllers\api\Estimate\EstimateApiController;
use Butler\Controllers\api\Gps\GpsDispatchNotesApiController;
use Butler\Controllers\api\Gps\GpsDispatchStatusApiController;
use Butler\Controllers\OSGHook;
use Butler\Controllers\tenants\Address\TenantAddressController;
use Butler\Controllers\tenants\Dispatch\DispatchGreetingSmsLogController;
use Butler\Controllers\tenants\Driver\DriverDeviceApiController;
use Butler\Controllers\tenants\Driver\DriverDeviceController;
use Butler\Controllers\tenants\Estimate\EstimateController;
use Butler\Controllers\tenants\Invoice\InvoiceApiController;
use Butler\Controllers\tenants\PriceBook\ServiceApiController;
use Butler\Controllers\tenants\PriceBook\ServiceCategoryApiController;
use Butler\Controllers\tenants\System\SystemMessageTemplateController;
use Butler\Controllers\tenants\TenantDashboardController;
use Butler\Controllers\tenants\TenantCallBoardController;
use Butler\Controllers\tenants\Employee\EmployeeController;
use Butler\Controllers\tenants\Integration\TwilioController;
use Butler\Controllers\tenants\PriceBook\ServiceCategoryController;
use Butler\Controllers\tenants\PriceBook\ServiceController;
use Butler\Controllers\tenants\Zone\ZoneController;
use Butler\Controllers\tenants\TenantProfile;
use Butler\Controllers\tenants\Gps\GpsDispatchStatusController;
use Butler\Controllers\employees\EmployeeJobboard;
use Butler\Controllers\api\JobsApi;
use Butler\Controllers\api\ZoneApi;
use Butler\Controllers\api\EmployeesApi;
use Butler\Controllers\api\TenantsApi;
use Butler\Controllers\api\UserAuthApi;
use Butler\Controllers\api\CommentsApi;
use Butler\Controllers\api\TwilioApi;
use Butler\Controllers\api\SmsApi;
use Butler\Controllers\api\MediaApi;
use Butler\Controllers\api\frontdoor\FrontDoorApi;
use Butler\Controllers\api\GoogleApi;
use Butler\Controllers\tenants\TenantRoleController;
use Butler\Controllers\api\CustomersApi;
use Butler\Controllers\api\SyncApi;
use Butler\Lib\Route\Router;
use Butler\Config\SiteConfig;

$router = new Router();
$config = SiteConfig::getSiteConfig();

$router->add('', function() {
    header('Location: '.BASE_URL.'login/employee');
    exit;
});

// Auth
$router->add('login/employee', 'employees/EmployeeAuth', 'employeeLogin');
$router->add('login/tenant', 'tenants/TenantAuth', 'tenantLogin');
$router->add('login/admin', 'admins/AdminAuth', 'adminLogin');
$router->add('logout', function() use (&$config) {
    $user_type = $_SESSION['user_type'] ?? '';
    session_unset();
    session_destroy();
    setcookie(session_name(), '', time() - 3600, '/');
    
    // $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? "https://" : "http://";
    // $host = $_SERVER['HTTP_HOST'];
    $baseUrl = $config['BASE_URL'];
    
    if ($user_type === 'site_admin') {
        header("Location: ". $baseUrl . "login/admin");
    } else if ($user_type === 'tenant') {
        header("Location: " . $baseUrl . "login/tenant");
    } else {
        header("Location: " . $baseUrl . "login/employee");
    }
    exit;
});

// OneStepGPS Hook
$router->add('webhook/osg', OSGHook::class, 'index');

$router->add('file/download2', DownloadFileApi::class, 'download');
$router->add('file/download', DownloadFileApi::class, 'downloadByPath');

//Tenants
$router->add('tenant/dashboard', TenantDashboardController::class, 'index', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/zone/list', ZoneController::class, 'index', ['auth' => ['TenantAuthMiddleware']]);

$router->add('tenant/integration/twilio', TwilioController::class , 'index', ['auth' => ['TenantAuthMiddleware']]);

$router->add('tenant/pricebook/service-category', ServiceCategoryController::class , 'index', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/pricebook/service-category/add', ServiceCategoryController::class , 'add', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/pricebook/service-category/edit', ServiceCategoryController::class , 'edit', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/pricebook/service-category/delete', ServiceCategoryController::class , 'delete', ['auth' => ['TenantAuthMiddleware']]);

$router->add('tenant/pricebook/service', ServiceController::class , 'index', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/pricebook/service/add', ServiceController::class , 'add', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/pricebook/service/edit', ServiceController::class , 'edit', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/pricebook/service/delete', ServiceController::class , 'delete', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/pricebook/service/import', ServiceController::class , 'import', ['auth' => ['TenantAuthMiddleware']]);

$router->add('tenant/driver/device/dsDevices', DriverDeviceController::class , 'dsDevices', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/driver/device/list', DriverDeviceController::class , 'index', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/driver/device/usDevice', DriverDeviceController::class , 'usDevice', ['auth' => ['TenantAuthMiddleware']]);

$router->add('tenant/gps/status', GpsDispatchStatusController::class , 'index', ['auth' => ['TenantAuthMiddleware']]);

$router->add('tenant/call_board', TenantCallBoardController::class , 'index', ['auth' => ['TenantAuthMiddleware']]);

// Tenants APIs.
$router->add('api/tenant/pricebook/service-category', ServiceCategoryApiController::class , 'index', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/tenant/pricebook/service-category/edit', ServiceCategoryApiController::class , 'edit', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/tenant/pricebook/service/search', ServiceApiController::class , 'search', ['auth' => ['TenantAuthMiddleware']]);

// Tenants Estimate APIs
$router->add('tenant/estimates/createOrUpdate', EstimateController::class , 'createOrUpdate', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/estimates/update', EstimateController::class , 'update', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/estimates/getAll', EstimateController::class , 'getAll', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/estimates/getOne', EstimateController::class , 'getOne', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/estimates/edit', EstimateController::class , 'edit', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/estimates/add', EstimateController::class , 'add', ['auth' => ['TenantAuthMiddleware']]);

// Tenants Invoice APIs
$router->get('api/tenant/invoice', InvoiceApiController::class , 'index', ['auth' => ['TenantAuthMiddleware']]);
$router->get('api/tenant/invoice/getOne', InvoiceApiController::class , 'getOne', ['auth' => ['TenantAuthMiddleware']]);
$router->post('api/tenant/invoice/add', InvoiceApiController::class , 'add', ['auth' => ['TenantAuthMiddleware']]);
$router->put('api/tenant/invoice/{id}/update', InvoiceApiController::class , 'update', ['auth' => ['TenantAuthMiddleware']]);
$router->delete('api/tenant/invoice/{id}', InvoiceApiController::class , 'edit', ['auth' => ['TenantAuthMiddleware']]);
$router->get('api/tenant/invoice/getOnePdf', InvoiceApiController::class , 'getPdf', ['auth' => ['EmployeeAuthMiddleware']]);


$router->add('tenant/getCurrentTenant', TenantDashboardController::class , 'getCurrentTenant', ['auth' => ['TenantAuthMiddleware']]);

$router->get('api/tenant/driver/device', DriverDeviceApiController::class , 'index', ['auth' => ['TenantAuthMiddleware']]);


// Employees
$router->add('employee/jobboard', EmployeeJobboard::class, 'index', ['auth' => ['EmployeeAuthMiddleware']]);
$router->add('employee/employee_call_board', EmployeeJobboard::class, 'employeeCallBoard', ['auth' => ['EmployeeAuthMiddleware']]);
$router->add('employee/employee_profile', EmployeeJobboard::class, 'employeeProfile', ['auth' => ['EmployeeAuthMiddleware']]);

$router->add('tenant/employee', EmployeeController::class, 'index', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/employee/add', EmployeeController::class , 'add', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/employee/edit', EmployeeController::class , 'edit', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/employee/delete', EmployeeController::class , 'delete', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/tenant_profile', TenantProfile::class, 'tenantProfile', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/tenants/get_tenant_profile', TenantsApi::class, 'getTenantProfile', ['auth' => ['TenantAuthMiddleware']]);

$router->add('tenant/employee/role', TenantRoleController::class, 'index', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/employee/role/add', TenantRoleController::class , 'add', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/employee/role/edit', TenantRoleController::class , 'edit', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/employee/role/delete', TenantRoleController::class , 'delete', ['auth' => ['TenantAuthMiddleware']]);

$router->add('tenant/settings/address', TenantAddressController::class, 'index', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/settings/address/add', TenantAddressController::class , 'add', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/settings/address/edit', TenantAddressController::class , 'edit', ['auth' => ['TenantAuthMiddleware']]);
$router->add('tenant/settings/address/delete', TenantAddressController::class , 'delete', ['auth' => ['TenantAuthMiddleware']]);

$router->add('tenant/settings/system-message-template/edit', SystemMessageTemplateController::class , 'edit', ['auth' => ['TenantAuthMiddleware']]);

$router->add('tenant/settings/dispatch/greetings', DispatchGreetingSmsLogController::class , 'index', ['auth' => ['TenantAuthMiddleware']]);


// Admins
$router->add('admin/dashboard', 'admins/AdminDashboard', 'index', ['auth' => ['AdminAuthMiddleware']]);
$router->add('admin/webhook_list', 'admins/AdminDashboard', 'webhook_list', ['auth' => ['AdminAuthMiddleware']]);
$router->add('admin/tenant_list', 'admins/AdminDashboard', 'tenant_list', ['auth' => ['AdminAuthMiddleware']]);
$router->add('admin/tenant_list', 'admins/AdminDashboard', 'tenant_list', ['auth' => ['AdminAuthMiddleware']]);
$router->add('admin/tenant_add', 'admins/AdminDashboard', 'tenant_add', ['auth' => ['AdminAuthMiddleware']]);
$router->add('admin/tenant_edit', 'admins/AdminDashboard', 'tenant_edit', ['auth' => ['AdminAuthMiddleware']]);
$router->add('admin/tenant_delete', 'admins/AdminDashboard', 'tenant_delete', ['auth' => ['AdminAuthMiddleware']]);

// admins / api keys
$router->add('admin/setting/system-api-key', SystemApiKeyController::class, 'index', ['auth' => ['AdminAuthMiddleware']]);
$router->add('admin/setting/system-api-key/add', SystemApiKeyController::class, 'add', ['auth' => ['AdminAuthMiddleware']]);
$router->add('admin/setting/system-api-key/edit', SystemApiKeyController::class, 'edit', ['auth' => ['AdminAuthMiddleware']]);
$router->add('admin/setting/system-api-key/delete', SystemApiKeyController::class, 'delete', ['auth' => ['AdminAuthMiddleware']]);

// --------------------------------------------------------------------------- //
// API Routes
// --------------------------------------------------------------------------- //

// Employee Estimate APIs
$router->add('api/pricebook/service/search', \Butler\Controllers\api\PriceBook\ServiceApiController::class , 'search', ['auth' => ['EmployeeAuthMiddleware']]);

$router->add('api/estimates/getOne', EstimateApiController::class , 'getOne', ['auth' => ['EmployeeAuthMiddleware']]);
$router->add('api/estimates/getAll', EstimateApiController::class , 'getAll', ['auth' => ['EmployeeAuthMiddleware']]);
$router->add('api/estimates/createOrUpdate', EstimateApiController::class , 'createOrUpdate', ['auth' => ['EmployeeAuthMiddleware']]);
$router->get('api/estimates/getOnePdf', EstimateApiController::class , 'getPdf', ['auth' => ['EmployeeAuthMiddleware']]);


$router->add('api/tenants/get', TenantsApi::class, 'getTenants', ['auth' => ['AdminAuthMiddleware']]);
$router->add('api/tenants/tenant_contacts', TenantsApi::class, 'getTenantContacts', ['auth' => ['TenantAuthMiddleware']]);

$router->add('api/users/get', EmployeesApi::class, 'getUsers', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/users/change-password', EmployeesApi::class, 'changePassword', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/employees/get', EmployeesApi::class, 'getEmployees', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/employees/employee_contacts', EmployeesApi::class, 'getEmployeeContacts', ['auth' => ['EmployeeAuthMiddleware']]);
$router->add('api/employees/employee_conversation', EmployeesApi::class, 'getEmployeeConversation', ['auth' => ['EmployeeAuthMiddleware']]);
$router->add('api/tenants/tenant_conversation', TenantsApi::class, 'getTenantConversation', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/employees/get_profile', EmployeesApi::class, 'getEmployeeProfile', ['auth' => ['EmployeeAuthMiddleware']]);
$router->add('api/employees/update_profile', EmployeesApi::class, 'updateEmployeeProfile', ['auth' => ['EmployeeAuthMiddleware']]);

// Get user list with driver position info.
$router->add('api/employees/driver_positions', DriverPositionsApi::class, 'getDriverPositions', ['auth' => ['EmployeeAuthMiddleware']]);
$router->add('api/tenants/driver_positions', DriverPositionsApi::class, 'getDriverPositions', ['auth' => ['TenantAuthMiddleware']]);

$router->add('api/media/serve', MediaApi::class, 'serve', ['auth' => ['EmployeeAuthMiddleware']]);

$router->add('api/auth/user', UserAuthApi::class, 'index');
$router->add('api/currentUser', UserAuthApi::class, 'currentUser', ['auth' => ['TenantAuthMiddleware']]);
// GPS dashboard
$router->get('api/tenants/gps/dispatchStatus', GpsDispatchStatusApiController::class, 'getAll', ['auth' => ['TenantAuthMiddleware']]);
$router->post('api/tenants/gps/dispatchStatus/add', GpsDispatchStatusApiController::class, 'add', ['auth' => ['TenantAuthMiddleware']]);
$router->put('api/tenants/gps/dispatchStatus/{id}/update', GpsDispatchStatusApiController::class, 'update', ['auth' => ['TenantAuthMiddleware']]);
$router->delete('api/tenants/gps/dispatchStatus/{id}', GpsDispatchStatusApiController::class, 'delete', ['auth' => ['TenantAuthMiddleware']]);

$router->get('api/tenants/gps/dispatchNotes', GpsDispatchNotesApiController::class, 'index', ['auth' => ['TenantAuthMiddleware']]);
$router->post('api/tenants/gps/dispatchNotes/add', GpsDispatchNotesApiController::class, 'add', ['auth' => ['TenantAuthMiddleware']]);
$router->put('api/tenants/gps/dispatchNotes/{id}/update', GpsDispatchNotesApiController::class, 'update', ['auth' => ['TenantAuthMiddleware']]);
$router->delete('api/tenants/gps/dispatchNotes/{id}', GpsDispatchNotesApiController::class, 'delete', ['auth' => ['TenantAuthMiddleware']]);


$router->get('api/jobs/index', JobsApi::class, 'index', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/jobs/get', JobsApi::class, 'getJobs', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/jobs/search_dispatches', JobsApi::class, 'searchDispatches', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/jobs/get_employee_jobs', JobsApi::class, 'getEmployeeJobs', ['auth' => ['EmployeeAuthMiddleware']]);
$router->add('api/jobs/get_exit_garage', JobsApi::class, 'getExitGarageJobs', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/jobs/get_parked_garage', JobsApi::class, 'getParkedGarageJobs', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/jobs/update', JobsApi::class, 'updateJob', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/jobs/assign', JobsApi::class, 'assignJob', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/jobs/accept_decline', JobsApi::class, 'acceptDeclineJob', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/jobs/update_job_status', JobsApi::class, 'updateJobStatus', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/jobs/updateAssignedTechs', JobsApi::class, 'updateAssignedTechs', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/jobs/comment', CommentsApi::class, 'addComment', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/jobs/add_employee_comment', CommentsApi::class, 'addEmployeeComment', ['auth' => ['EmployeeAuthMiddleware']]);
$router->add('api/jobs/update_employee_comment', CommentsApi::class, 'updateEmployeeComment', ['auth' => ['EmployeeAuthMiddleware']]);
$router->add('api/jobs/delete_employee_comment', CommentsApi::class, 'deleteEmployeeComment', ['auth' => ['EmployeeAuthMiddleware']]);
$router->add('api/jobs/get_employee_comments', CommentsApi::class, 'getEmployeeComments', ['auth' => ['EmployeeAuthMiddleware']]);
$router->add('api/jobs/upload_employee_comment_attachments', CommentsApi::class, 'uploadEmployeeCommentAttachment', ['auth' => ['EmployeeAuthMiddleware']]);
$router->add('api/jobs/delete_employee_comment_attachment', CommentsApi::class, 'deleteEmployeeCommentAttachment', ['auth' => ['EmployeeAuthMiddleware']]);
$router->add('api/jobs/get_customer_jobs', JobsApi::class, 'getCustomerJobs', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/jobs/tenants/create_job', JobsApi::class, 'createNewJob', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/jobs/tenants/update_job_meta', JobsApi::class, 'updateJobMeta', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/jobs/employees/update_job_meta', JobsApi::class, 'updateJobMeta', ['auth' => ['EmployeeAuthMiddleware']]);
$router->add('api/jobs/tenants/get_job_meta', JobsApi::class, 'getJobMeta', ['auth' => ['TenantAuthMiddleware']]);

$router->add('api/customers/tenants/get_customer', CustomersApi::class, 'getCustomer', ['auth' => ['TenantAuthMiddleware']]);

$router->add('api/zones/get_zones', ZoneApi::class, 'getZones', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/zones/create_zone', ZoneApi::class, 'createZone', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/zones/zipcodes', ZoneApi::class, 'getZipcodes', ['auth' => true]);
$router->add('api/zones/search_zipcodes', ZoneApi::class, 'searchZipcodes', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/zones/get_state_zones', ZoneApi::class, 'getStateZones', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/zones/get_zone_counts_by_state', ZoneApi::class, 'getZoneCountByState', ['auth' => ['TenantAuthMiddleware']]);

$router->add('api/twilio/twilio_access_token', TwilioApi::class, 'getTwilioEmpAccessToken', ['auth' => ['EmployeeAuthMiddleware']]);
$router->add('api/twilio/get_tenant_numbers', TwilioApi::class, 'getTenantNumbers', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/twilio/get_available_number', TwilioApi::class, 'getAvailableNumbers', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/twilio/buy_twilio_number', TwilioApi::class, 'buyTwilioNumber', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/twilio/assign_number', TwilioApi::class, 'assignNumberToEmployee', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/twilio/register_push_token', TwilioApi::class, 'registerPushToken', ['auth' => ['EmployeeAuthMiddleware']]);
$router->add('api/twilio/tenants/search_by_phone', TwilioApi::class, 'searchByPhone', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/twilio/employees/search_by_phone', TwilioApi::class, 'searchByPhone', ['auth' => ['EmployeeAuthMiddleware']]);

$router->add('api/calls/twilio_call_handler', TwilioApi::class, 'handleTwilioCall', ['auth' => []]);
$router->add('api/sms/twilio_sms_handler', TwilioApi::class, 'handleTwilioSms', ['auth' => []]);
$router->add('api/calls/twilio_callback_handler', TwilioApi::class, 'handleTwilioCallStatusCallback', ['auth' => []]);

$router->add('api/sync/developmentbutler_sync', SyncApi::class, 'syncDevelopmentButler', ['auth' => []]);

$router->add('api/sms/send_sms', SmsApi::class, 'sendSms', ['auth' => ['EmployeeAuthMiddleware']]);
$router->add('api/sms/get_incoming_sms', SmsApi::class, 'getIncomingSms', ['auth' => true]);
$router->add('api/sms/save_sms_template', SmsApi::class, 'saveSmsTemplate', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/sms/get_sms_templates', SmsApi::class, 'getSmsTemplate', ['auth' => ['EmployeeAuthMiddleware']]);
$router->add('api/sms/get_twilio_sms_logs', SmsApi::class, 'getTwilioSmsLogs', ['auth' => ['EmployeeAuthMiddleware']]);


$router->add('api/other/get_sentiment', 'api/OthersApi', 'getSentiment', ['auth' => true]);
$router->add('api/other/get_categories', 'api/OthersApi', 'getCategories', ['auth' => true]);
$router->add('api/other/get_category_mappings', 'api/OthersApi', 'getCategoryMappings', ['auth' => true]);

$router->add('api/google/get_google_maps_key', GoogleApi::class, 'getGoogleMapsKey', ['auth' => ['TenantAuthMiddleware']]);
$router->add('api/google/get_emp_google_maps_key', GoogleApi::class, 'getEmpGoogleMapsKey', ['auth' => ['EmployeeAuthMiddleware']]);

$router->add('api/webhooks/frontdoor_dispatch', 'api/WebhooksApi', 'handleDispatch', ['auth' => ['WebhookAuthMiddleware']]);
$router->add('api/webhooks/webhook_list', 'api/WebhooksApi', 'webhook_list', ['auth' => ['AdminAuthMiddleware']]);
$router->add('api/webhooks/get', 'api/WebhooksApi', 'webhook_get', ['auth' => ['AdminAuthMiddleware']]);
$router->add('api/webhooks/generate', 'api/WebhooksApi', 'webhook_token_generate', ['auth' => ['AdminAuthMiddleware']]);
$router->add('api/webhooks/save', 'api/WebhooksApi', 'save', ['auth' => ['AdminAuthMiddleware']]);
$router->add('api/webhooks/delete', 'api/WebhooksApi', 'delete', ['auth' => ['AdminAuthMiddleware']]);

$router->add('api/frontdoor/authenticate', FrontDoorApi::class, 'authenticate', ['auth' => ['EmployeeAuthMiddleware']]);


