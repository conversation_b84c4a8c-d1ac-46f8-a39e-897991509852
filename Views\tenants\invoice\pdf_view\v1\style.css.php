<?php
    $baseUrl = BASE_URL;
?>
body {
    font-family: <PERSON>homa, opensans, arial;
    font-size: 10pt;
}

/** Page Header
------------------------------------------------  */
.pageHeaderTop {
    height: 60px;
    background: #e6e6e6;
    width: 100%;
}

.pageHeader {
    padding: 20px 0 10px;
    height: 70px;
}

/** Page footer
------------------------------------------------  */
.pageFooter {
    padding: 0;
}

.pageFooter .footerDataRow {
    padding: 15px 30px 0;
    height: 30px;
}

.pageFooter .footerDataRow img {
    vertical-align: middle;
}

.pageFooter .footerDataRow2 {
    height: 20px;
    padding: 0 30px;
}

.pageFooter .footerRow {
    background: #e6e6e6;
    height: 20px;
}

/** Body part
------------------------------------------------  */

table {
    border-collapse: collapse;
    min-width: 100%;
    width: 800px;
    table-layout: fixed;
    border-spacing: 0;
}

table thead td {
    padding: 6px 8px;
    font-weight: bold;
    background-color: #eeeeee;
    border-top: 1px solid #000000;
    border-bottom: 1px solid #000000;
}

table tbody td {
    padding: 4px 8px;
    word-break: break-all;
}

table tbody tr.row td {
    border-bottom: 1px solid #eeeeee;
}

table tbody tr.row.bt2 td {
    border-top: 2px solid #555555;
}


.c-red {
    color: #ee2201;
}

.c-grey {
    color: grey;
}

.c-orange {
    color: #f9a409;
}

.c-yellow {
    color: #baba09;
}


.header {
    text-align: center;
    margin-bottom: 30px;
}

.company-name {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.estimate-title {
    font-size: 18px;
    color: #666;
    margin-top: 10px;
}

.info-section {
    margin-bottom: 20px;
}

.info-table {
    width: 100%;
}

.info-table td {
    padding: 5px;
    vertical-align: top;
}

.label {
    font-weight: bold;
    color: #333;
}

.line-items {
    margin: 20px 0;
}

.line-items table {
    width: 100%;
    border-collapse: collapse;
}

.line-items th, .line-items td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.line-items th {
    background-color: #f5f5f5;
    font-weight: bold;
}

.totals {
    margin-top: 20px;
}

.totals table {
    width: 300px;
    margin-left: auto;
    border-collapse: collapse;
}

.totals td {
    padding: 5px;
    border-bottom: 1px solid #ddd;
}

.total-row {
    font-weight: bold;
    font-size: 14px;
}

.notes {
    margin-top: 30px;
}

.terms {
    margin-top: 20px;
    font-size: 9px;
    color: #666;
}

.line-item-row {
    margin-bottom: 10px;
}

.estimate-totals {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
}


/** Invoice Detail at the top
-----------------------------------------------------  */
table.invoice-detail-box {
    width: 400px;
    border-collapse: collapse;
    border: 2px solid #B5B5B5;
}

table.invoice-detail-box td {
    padding: 8px 12px 0 12px;
    vertical-align: middle;
}
table.invoice-detail-box td.label {
    color: #666;
    font-weight: normal;
    font-size: 12px;
    text-align: left;
}
table.invoice-detail-box td.value {
    color: #666;
    font-size: 13px;
    font-weight: normal;
    text-align: right;
}
table.invoice-detail-box tr.amount-due-row td {
    border-top: 2px solid #B5B5B5;
    padding: 8px 12px;
}


/** Invoice billing info
-----------------------------------------------------  */
table.invoice-info {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 30px;
}

table.invoice-info td {
    padding: 0 0 6px 0;
    vertical-align: middle;
}

table.billing-info {
    width: 400px;
}

table.billing-info td{
    text-align: left;
}


