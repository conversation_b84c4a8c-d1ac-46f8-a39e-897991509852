<?php

namespace Butler\Models;

use <PERSON>\Helpers\Constants;
use <PERSON>\Helpers\Func;
use Butler\Models\BaseModel;
use Butler\Models\PriceBook\PbServiceCategory;

/**
 * @property integer $id
 * @property string $category
 * @property string $file_name
 * @property string $clean_file_name
 * @property string $path
 * @property string $org_path
 * @property integer $size
 * @property string $type
 * @property string $hash
 * @property integer $hits
 * @property string $created_at
 * @property string $updated_at
 *
  * @property mixed|string $url
 * @property int|mixed $uid
 * @property string $name
 * @property mixed|string $abs_path
 * @property mixed|string $thumb_url
 *
 * @property PbServiceCategory[] $pbServiceCategories
 */
class File extends BaseModel
{
    public $timestamps = true;

    const CAT_ESTIMATE_PDF = 'Estimate pdf';
    const CAT_INVOICE_PDF = 'Invoice pdf';

    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'file';

    /**
     * @var array
     */
    protected $fillable = ['category', 'file_name', 'clean_file_name', 'path', 'org_path', 'size', 'type', 'hash', 'hits', 'created_at', 'updated_at'];


    protected $hidden = ['abs_path'];

    public $appends = [
        'url', 'uid', 'thumb_url', 'name'
    ];

    public function getUidAttribute()
    {
        return $this->id;
    }

    public function getNameAttribute()
    {
        return $this->file_name;
    }

    public static function getBasePath($category)
    {
        $basePath = Func::getContainer()->get('settings')['upload_dir'];
        switch ($category) {
            case self::CAT_ESTIMATE_PDF:
                $basePath = UPLOAD_PATH;
                break;
        }

        return $basePath;
    }

    public function getAbsPathAttribute($fieldKey = null)
    {
        $container = Func::getContainer();

        $category = $this->getAttributeValue('category');
        $basePath = self::getBasePath($category);

        return $basePath . DS . $this->getAttributeValue($fieldKey ?? 'org_path');
    }

    public function getUrlAttribute()
    {
        $category = $this->getAttributeValue('category');

        switch ($category) {
            case self::CAT_ESTIMATE_PDF:
            case self::CAT_INVOICE_PDF:
                $kv = 1;
                $url = 'file/download?id=' . Func::mcrypt('encrypt', $this->id, ...Constants::FILE_SEC_KEYS[$kv])."&kv=$kv";
                break;
            default:
                $url = BASE_URL . $this->org_path;
        }

        return $url;
    }

    public function getThumbUrlAttribute()
    {
        $category = $this->getAttributeValue('category');
        switch ($category) {
            case self::CAT_ESTIMATE_PDF:
            case self::CAT_INVOICE_PDF:
                $kv = 1;
                $url = 'file/download?id=' . Func::mcrypt('encrypt', $this->id, ...Constants::FILE_SEC_KEYS[$kv])."&kv=$kv&thumb=1";
                return $url;
            default:
                return $this->org_path ? BASE_URL . ($this->path ?? $this->org_path) : null;
        }
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function pbServiceCategories()
    {
        return $this->hasMany(PbServiceCategory::class);
    }


}
